#!/usr/bin/env python3
"""OpenRouter LLM client for scientific paper analysis."""

import os
import json
import httpx
from typing import Dict, List, Optional, Any
from dotenv import load_dotenv
import time
import logging

# Load environment variables
load_dotenv()

logger = logging.getLogger(__name__)


class OpenRouterClient:
    """Client for OpenRouter API using mistralai/devstral-small model."""
    
    def __init__(self, api_key: Optional[str] = None, model: str = "mistralai/devstral-small"):
        self.api_key = api_key or os.getenv("OPENROUTER_API_KEY")
        if not self.api_key:
            raise ValueError("OpenRouter API key not found. Set OPENROUTER_API_KEY environment variable.")
        
        self.model = model
        self.base_url = "https://openrouter.ai/api/v1"
        self.headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json",
            "HTTP-Referer": "https://github.com/novelty-impact-graphs",
            "X-Title": "Scientific Paper Analysis"
        }
        
    async def _make_request(self, messages: List[Dict[str, str]], 
                           max_tokens: int = 2000, 
                           temperature: float = 0.1) -> Dict[str, Any]:
        """Make async request to OpenRouter API."""
        payload = {
            "model": self.model,
            "messages": messages,
            "max_tokens": max_tokens,
            "temperature": temperature,
            "stream": False
        }
        
        async with httpx.AsyncClient(timeout=60.0) as client:
            try:
                response = await client.post(
                    f"{self.base_url}/chat/completions",
                    headers=self.headers,
                    json=payload
                )
                response.raise_for_status()
                return response.json()
            except httpx.HTTPError as e:
                logger.error(f"HTTP error in OpenRouter request: {e}")
                raise
            except Exception as e:
                logger.error(f"Unexpected error in OpenRouter request: {e}")
                raise

    def _make_sync_request(self, messages: List[Dict[str, str]], 
                          max_tokens: int = 2000, 
                          temperature: float = 0.1) -> Dict[str, Any]:
        """Make synchronous request to OpenRouter API."""
        payload = {
            "model": self.model,
            "messages": messages,
            "max_tokens": max_tokens,
            "temperature": temperature,
            "stream": False
        }
        
        with httpx.Client(timeout=60.0) as client:
            try:
                response = client.post(
                    f"{self.base_url}/chat/completions",
                    headers=self.headers,
                    json=payload
                )
                response.raise_for_status()
                return response.json()
            except httpx.HTTPError as e:
                logger.error(f"HTTP error in OpenRouter request: {e}")
                raise
            except Exception as e:
                logger.error(f"Unexpected error in OpenRouter request: {e}")
                raise

    def extract_logic_graph(self, paper_text: str, title: str = "") -> Dict[str, Any]:
        """Extract logic graph structure from paper text using LLM."""
        
        system_prompt = """You are an expert scientific paper analyst. Your task is to extract the logical structure of research papers, identifying key hypotheses, lines of evidence, and their relationships.

Return your analysis as a JSON object with the following structure:
{
    "hypotheses": [
        {
            "text": "hypothesis statement",
            "type": "primary|secondary|null",
            "confidence": 0.0-1.0
        }
    ],
    "evidence": [
        {
            "text": "evidence statement",
            "type": "experimental|observational|computational|statistical",
            "supports_hypothesis": [hypothesis_indices],
            "strength": "strong|moderate|weak"
        }
    ],
    "conclusions": [
        {
            "text": "conclusion statement",
            "supported_by_evidence": [evidence_indices],
            "confidence": 0.0-1.0
        }
    ],
    "logic_relationships": [
        {
            "from_type": "hypothesis|evidence|conclusion",
            "from_index": 0,
            "to_type": "hypothesis|evidence|conclusion", 
            "to_index": 1,
            "relationship": "supports|contradicts|extends|refines"
        }
    ]
}

Focus on:
1. Clear hypothesis statements (what the authors predicted/proposed)
2. Concrete evidence (experimental results, observations, data)
3. Logical conclusions drawn from evidence
4. How these elements connect to form the paper's argument"""

        user_prompt = f"""Analyze this scientific paper and extract its logical structure:

Title: {title}

Paper text (first 4000 characters):
{paper_text[:4000]}

Extract the hypotheses, evidence, conclusions, and their logical relationships. Return only valid JSON."""

        messages = [
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": user_prompt}
        ]
        
        try:
            response = self._make_sync_request(messages, max_tokens=2000, temperature=0.1)
            content = response["choices"][0]["message"]["content"]
            
            # Try to parse JSON response
            try:
                logic_graph = json.loads(content)
                return logic_graph
            except json.JSONDecodeError:
                # If JSON parsing fails, try to extract JSON from response
                import re
                json_match = re.search(r'\{.*\}', content, re.DOTALL)
                if json_match:
                    logic_graph = json.loads(json_match.group())
                    return logic_graph
                else:
                    logger.warning("Could not parse JSON from LLM response")
                    return self._create_empty_logic_graph()
                    
        except Exception as e:
            logger.error(f"Error in logic graph extraction: {e}")
            return self._create_empty_logic_graph()

    def extract_key_hypotheses(self, paper_text: str, title: str = "") -> List[Dict[str, Any]]:
        """Extract key hypotheses from paper using LLM."""
        
        system_prompt = """You are an expert at identifying scientific hypotheses in research papers. 

Extract the key hypotheses from the given paper. Return a JSON array of hypothesis objects:
[
    {
        "text": "clear hypothesis statement",
        "type": "primary|secondary|null",
        "section": "abstract|introduction|methods|results|discussion",
        "confidence": 0.0-1.0,
        "testable": true|false
    }
]

Focus on:
- Explicit hypothesis statements ("we hypothesize that...", "we predict...", etc.)
- Implicit predictions or expectations
- Research questions that imply testable predictions
- Distinguish between primary (main) and secondary (supporting) hypotheses"""

        user_prompt = f"""Extract key hypotheses from this scientific paper:

Title: {title}

Paper text:
{paper_text[:3000]}

Return only the JSON array of hypotheses."""

        messages = [
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": user_prompt}
        ]
        
        try:
            response = self._make_sync_request(messages, max_tokens=1500, temperature=0.1)
            content = response["choices"][0]["message"]["content"]
            
            # Parse JSON response
            try:
                hypotheses = json.loads(content)
                return hypotheses if isinstance(hypotheses, list) else []
            except json.JSONDecodeError:
                logger.warning("Could not parse hypotheses JSON from LLM response")
                return []
                
        except Exception as e:
            logger.error(f"Error in hypothesis extraction: {e}")
            return []

    def extract_lines_of_evidence(self, paper_text: str, title: str = "") -> List[Dict[str, Any]]:
        """Extract lines of evidence from paper using LLM."""
        
        system_prompt = """You are an expert at identifying scientific evidence in research papers.

Extract lines of evidence from the given paper. Return a JSON array of evidence objects:
[
    {
        "text": "evidence statement",
        "type": "experimental|observational|computational|statistical|literature",
        "section": "methods|results|discussion",
        "strength": "strong|moderate|weak",
        "quantitative": true|false,
        "statistical_significance": "p<0.05|p<0.01|not_reported|not_applicable"
    }
]

Focus on:
- Experimental results and observations
- Statistical analyses and their outcomes
- Computational findings
- Literature support
- Quantitative measurements and their significance"""

        user_prompt = f"""Extract lines of evidence from this scientific paper:

Title: {title}

Paper text:
{paper_text[:3000]}

Return only the JSON array of evidence."""

        messages = [
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": user_prompt}
        ]
        
        try:
            response = self._make_sync_request(messages, max_tokens=1500, temperature=0.1)
            content = response["choices"][0]["message"]["content"]
            
            # Parse JSON response
            try:
                evidence = json.loads(content)
                return evidence if isinstance(evidence, list) else []
            except json.JSONDecodeError:
                logger.warning("Could not parse evidence JSON from LLM response")
                return []
                
        except Exception as e:
            logger.error(f"Error in evidence extraction: {e}")
            return []

    def _create_empty_logic_graph(self) -> Dict[str, Any]:
        """Create empty logic graph structure."""
        return {
            "hypotheses": [],
            "evidence": [],
            "conclusions": [],
            "logic_relationships": []
        }


# Rate limiting decorator
def rate_limit(calls_per_minute: int = 30):
    """Rate limiting decorator for API calls."""
    min_interval = 60.0 / calls_per_minute
    last_called = [0.0]
    
    def decorator(func):
        def wrapper(*args, **kwargs):
            elapsed = time.time() - last_called[0]
            left_to_wait = min_interval - elapsed
            if left_to_wait > 0:
                time.sleep(left_to_wait)
            ret = func(*args, **kwargs)
            last_called[0] = time.time()
            return ret
        return wrapper
    return decorator
