#!/usr/bin/env python3
"""OpenRouter LLM client for scientific paper analysis."""

import os
import json
import httpx
import re
from typing import Dict, List, Optional, Any
from dotenv import load_dotenv
import time
import logging

# Load environment variables
load_dotenv()

logger = logging.getLogger(__name__)


class OpenRouterClient:
    """Client for OpenRouter API using mistralai/devstral-small model."""

    def __init__(self, api_key: Optional[str] = None, model: str = "mistralai/devstral-small"):
        self.api_key = api_key or os.getenv("OPENROUTER_API_KEY")
        if not self.api_key:
            raise ValueError("OpenRouter API key not found. Set OPENROUTER_API_KEY environment variable.")

        self.model = model
        self.base_url = "https://openrouter.ai/api/v1"
        self.headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json",
            "HTTP-Referer": "https://github.com/novelty-impact-graphs",
            "X-Title": "Scientific Paper Analysis"
        }

    async def _make_request(self, messages: List[Dict[str, str]],
                           max_tokens: int = 2000,
                           temperature: float = 0.1) -> Dict[str, Any]:
        """Make async request to OpenRouter API."""
        payload = {
            "model": self.model,
            "messages": messages,
            "max_tokens": max_tokens,
            "temperature": temperature,
            "stream": False
        }

        async with httpx.AsyncClient(timeout=60.0) as client:
            try:
                response = await client.post(
                    f"{self.base_url}/chat/completions",
                    headers=self.headers,
                    json=payload
                )
                response.raise_for_status()
                return response.json()
            except httpx.HTTPError as e:
                logger.error(f"HTTP error in OpenRouter request: {e}")
                raise
            except Exception as e:
                logger.error(f"Unexpected error in OpenRouter request: {e}")
                raise

    def _make_sync_request(self, messages: List[Dict[str, str]],
                          max_tokens: int = 2000,
                          temperature: float = 0.1) -> Dict[str, Any]:
        """Make synchronous request to OpenRouter API."""
        payload = {
            "model": self.model,
            "messages": messages,
            "max_tokens": max_tokens,
            "temperature": temperature,
            "stream": False
        }

        with httpx.Client(timeout=60.0) as client:
            try:
                response = client.post(
                    f"{self.base_url}/chat/completions",
                    headers=self.headers,
                    json=payload
                )
                response.raise_for_status()
                return response.json()
            except httpx.HTTPError as e:
                logger.error(f"HTTP error in OpenRouter request: {e}")
                raise
            except Exception as e:
                logger.error(f"Unexpected error in OpenRouter request: {e}")
                raise

    def extract_logic_graph(self, paper_text: str, title: str = "") -> Dict[str, Any]:
        """Extract logic graph structure from paper text using LLM."""

        system_prompt = """You are an expert scientific paper analyst. Extract hypotheses and evidence using EXACT QUOTES from the paper.

CRITICAL REQUIREMENTS:
1. Use EXACT quotes from the paper - do not paraphrase or summarize
2. Include the section name where each item was found
3. Preserve original scientific terminology and phrasing
4. Distinguish clearly: hypotheses = predictions/expectations, evidence = findings/results

Return JSON with this EXACT structure:
{
    "hypotheses": [
        {
            "text": "EXACT quote from paper containing the hypothesis",
            "section": "abstract|introduction|methods|results|discussion",
            "type": "explicit_hypothesis|prediction|expectation|proposal",
            "confidence": 0.0-1.0
        }
    ],
    "evidence": [
        {
            "text": "EXACT quote from paper containing the evidence",
            "section": "methods|results|discussion",
            "type": "experimental_finding|statistical_evidence|analytical_finding|data_evidence",
            "strength": "strong|moderate|weak",
            "quantitative": true|false,
            "statistical_significance": "p<0.05|p<0.01|not_reported|not_applicable"
        }
    ],
    "conclusions": [
        {
            "text": "EXACT quote from paper containing the conclusion",
            "section": "results|discussion",
            "confidence": 0.0-1.0
        }
    ]
}

EXTRACTION GUIDELINES:
- Hypotheses: Look for "we hypothesize", "we predict", "we expect", "our hypothesis"
- Evidence: Look for "we found", "results show", "analysis revealed", statistical values
- Use exact quotations, not summaries
- Include section context for each item"""

        user_prompt = f"""Analyze this scientific paper and extract its logical structure:

Title: {title}

Paper text (first 4000 characters):
{paper_text[:4000]}

Extract the hypotheses, evidence, conclusions, and their logical relationships. Return only valid JSON."""

        messages = [
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": user_prompt}
        ]

        try:
            response = self._make_sync_request(messages, max_tokens=2000, temperature=0.1)
            content = response["choices"][0]["message"]["content"]

            # Log raw response for debugging
            logger.debug(f"Raw LLM response for logic graph: {content[:500]}...")

            # Use robust JSON parsing
            parsed_data = self._robust_json_parse(content, "object")

            if parsed_data:
                # Validate and normalize the structure
                normalized_data = self._validate_and_normalize_logic_graph(parsed_data)
                logger.info(f"Successfully extracted logic graph with {len(normalized_data['hypotheses'])} hypotheses, {len(normalized_data['evidence'])} evidence items")
                return normalized_data
            else:
                logger.warning("Could not parse logic graph from LLM response")
                return self._create_empty_logic_graph()

        except Exception as e:
            logger.error(f"Error in logic graph extraction: {e}")
            return self._create_empty_logic_graph()

    def extract_key_hypotheses(self, paper_text: str, title: str = "") -> List[Dict[str, Any]]:
        """Extract key hypotheses from paper using LLM."""

        system_prompt = """You are an expert at identifying scientific hypotheses in research papers.

Extract the key hypotheses from the given paper. Return a JSON array of hypothesis objects:
[
    {
        "text": "clear hypothesis statement",
        "type": "primary|secondary|null",
        "section": "abstract|introduction|methods|results|discussion",
        "confidence": 0.0-1.0,
        "testable": true|false
    }
]

Focus on:
- Explicit hypothesis statements ("we hypothesize that...", "we predict...", etc.)
- Implicit predictions or expectations
- Research questions that imply testable predictions
- Distinguish between primary (main) and secondary (supporting) hypotheses"""

        user_prompt = f"""Extract key hypotheses from this scientific paper:

Title: {title}

Paper text:
{paper_text[:3000]}

Return only the JSON array of hypotheses."""

        messages = [
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": user_prompt}
        ]

        try:
            response = self._make_sync_request(messages, max_tokens=1500, temperature=0.1)
            content = response["choices"][0]["message"]["content"]

            # Log raw response for debugging
            logger.debug(f"Raw LLM response for hypotheses: {content[:300]}...")

            # Use robust JSON parsing
            parsed_data = self._robust_json_parse(content, "array")

            if parsed_data and isinstance(parsed_data, list):
                # Normalize hypothesis data
                normalized_hypotheses = []
                for hyp in parsed_data:
                    if isinstance(hyp, dict):
                        normalized_hyp = {
                            "text": str(hyp.get("text", "")),
                            "type": hyp.get("type", "unknown"),
                            "section": hyp.get("section", "unknown"),
                            "confidence": float(hyp.get("confidence", 0.5)) if hyp.get("confidence") is not None else 0.5,
                            "testable": bool(hyp.get("testable", True))
                        }
                        normalized_hypotheses.append(normalized_hyp)
                    elif isinstance(hyp, str):
                        normalized_hypotheses.append({
                            "text": hyp,
                            "type": "unknown",
                            "section": "unknown",
                            "confidence": 0.5,
                            "testable": True
                        })

                logger.info(f"Successfully extracted {len(normalized_hypotheses)} hypotheses")
                return normalized_hypotheses
            else:
                logger.warning("Could not parse hypotheses from LLM response")
                return []

        except Exception as e:
            logger.error(f"Error in hypothesis extraction: {e}")
            return []

    def extract_lines_of_evidence(self, paper_text: str, title: str = "") -> List[Dict[str, Any]]:
        """Extract lines of evidence from paper using LLM."""

        system_prompt = """You are an expert at identifying scientific evidence in research papers.

Extract lines of evidence from the given paper. Return a JSON array of evidence objects:
[
    {
        "text": "evidence statement",
        "type": "experimental|observational|computational|statistical|literature",
        "section": "methods|results|discussion",
        "strength": "strong|moderate|weak",
        "quantitative": true|false,
        "statistical_significance": "p<0.05|p<0.01|not_reported|not_applicable"
    }
]

Focus on:
- Experimental results and observations
- Statistical analyses and their outcomes
- Computational findings
- Literature support
- Quantitative measurements and their significance"""

        user_prompt = f"""Extract lines of evidence from this scientific paper:

Title: {title}

Paper text:
{paper_text[:3000]}

Return only the JSON array of evidence."""

        messages = [
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": user_prompt}
        ]

        try:
            response = self._make_sync_request(messages, max_tokens=1500, temperature=0.1)
            content = response["choices"][0]["message"]["content"]

            # Log raw response for debugging
            logger.debug(f"Raw LLM response for evidence: {content[:300]}...")

            # Use robust JSON parsing
            parsed_data = self._robust_json_parse(content, "array")

            if parsed_data and isinstance(parsed_data, list):
                # Normalize evidence data
                normalized_evidence = []
                for ev in parsed_data:
                    if isinstance(ev, dict):
                        normalized_ev = {
                            "text": str(ev.get("text", "")),
                            "type": ev.get("type", "unknown"),
                            "section": ev.get("section", "unknown"),
                            "strength": ev.get("strength", "moderate"),
                            "quantitative": bool(ev.get("quantitative", False)),
                            "statistical_significance": ev.get("statistical_significance", "not_reported"),
                            "supports_hypothesis": ev.get("supports_hypothesis", [])
                        }
                        normalized_evidence.append(normalized_ev)
                    elif isinstance(ev, str):
                        normalized_evidence.append({
                            "text": ev,
                            "type": "unknown",
                            "section": "unknown",
                            "strength": "moderate",
                            "quantitative": False,
                            "statistical_significance": "not_reported",
                            "supports_hypothesis": []
                        })

                logger.info(f"Successfully extracted {len(normalized_evidence)} evidence items")
                return normalized_evidence
            else:
                logger.warning("Could not parse evidence from LLM response")
                return []

        except Exception as e:
            logger.error(f"Error in evidence extraction: {e}")
            return []

    def _create_empty_logic_graph(self) -> Dict[str, Any]:
        """Create empty logic graph structure."""
        return {
            "hypotheses": [],
            "evidence": [],
            "conclusions": [],
            "logic_relationships": []
        }

    def _robust_json_parse(self, content: str, expected_type: str = "object") -> Any:
        """Robust JSON parsing with multiple fallback strategies."""

        # Strategy 1: Direct JSON parsing
        try:
            parsed = json.loads(content)
            logger.debug("Successfully parsed JSON directly")
            return parsed
        except json.JSONDecodeError:
            pass

        # Strategy 2: Extract from markdown code blocks
        json_block_pattern = r'```(?:json)?\s*(\{.*?\}|\[.*?\])\s*```'
        json_match = re.search(json_block_pattern, content, re.DOTALL | re.IGNORECASE)
        if json_match:
            try:
                parsed = json.loads(json_match.group(1))
                logger.debug("Successfully parsed JSON from markdown block")
                return parsed
            except json.JSONDecodeError:
                pass

        # Strategy 3: Extract JSON object/array from text
        if expected_type == "array":
            # Look for array pattern
            array_pattern = r'\[.*?\]'
            array_match = re.search(array_pattern, content, re.DOTALL)
            if array_match:
                try:
                    parsed = json.loads(array_match.group())
                    logger.debug("Successfully parsed JSON array from text")
                    return parsed
                except json.JSONDecodeError:
                    pass
        else:
            # Look for object pattern
            obj_pattern = r'\{.*\}'
            obj_match = re.search(obj_pattern, content, re.DOTALL)
            if obj_match:
                try:
                    parsed = json.loads(obj_match.group())
                    logger.debug("Successfully parsed JSON object from text")
                    return parsed
                except json.JSONDecodeError:
                    pass

        # Strategy 4: Clean common JSON errors and retry
        cleaned_content = self._clean_json_content(content)
        if cleaned_content != content:
            try:
                parsed = json.loads(cleaned_content)
                logger.debug("Successfully parsed cleaned JSON")
                return parsed
            except json.JSONDecodeError:
                pass

        # Strategy 5: Extract and clean from patterns
        for pattern in [r'\{.*\}', r'\[.*\]']:
            match = re.search(pattern, content, re.DOTALL)
            if match:
                cleaned = self._clean_json_content(match.group())
                try:
                    parsed = json.loads(cleaned)
                    logger.debug("Successfully parsed cleaned extracted JSON")
                    return parsed
                except json.JSONDecodeError:
                    continue

        logger.warning(f"All JSON parsing strategies failed for content: {content[:200]}...")
        return None

    def _clean_json_content(self, content: str) -> str:
        """Clean common JSON formatting issues."""

        # Remove comments (// and /* */)
        content = re.sub(r'//.*?$', '', content, flags=re.MULTILINE)
        content = re.sub(r'/\*.*?\*/', '', content, flags=re.DOTALL)

        # Remove trailing commas before closing brackets/braces
        content = re.sub(r',(\s*[}\]])', r'\1', content)

        # Fix common quote issues
        content = re.sub(r'([{,]\s*)(\w+)(\s*:)', r'\1"\2"\3', content)  # Unquoted keys

        # Remove extra whitespace
        content = re.sub(r'\s+', ' ', content)

        return content.strip()

    def _validate_and_normalize_logic_graph(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Validate and normalize logic graph structure."""

        normalized = {
            "hypotheses": [],
            "evidence": [],
            "conclusions": [],
            "logic_relationships": []
        }

        # Normalize hypotheses
        if "hypotheses" in data and isinstance(data["hypotheses"], list):
            for hyp in data["hypotheses"]:
                if isinstance(hyp, dict):
                    normalized_hyp = {
                        "text": str(hyp.get("text", "")),
                        "type": hyp.get("type", "unknown"),
                        "confidence": float(hyp.get("confidence", 0.5)) if hyp.get("confidence") is not None else 0.5,
                        "section": hyp.get("section", "unknown"),
                        "testable": bool(hyp.get("testable", True))
                    }
                    normalized["hypotheses"].append(normalized_hyp)
                elif isinstance(hyp, str):
                    normalized["hypotheses"].append({
                        "text": hyp,
                        "type": "unknown",
                        "confidence": 0.5,
                        "section": "unknown",
                        "testable": True
                    })

        # Normalize evidence
        if "evidence" in data and isinstance(data["evidence"], list):
            for ev in data["evidence"]:
                if isinstance(ev, dict):
                    normalized_ev = {
                        "text": str(ev.get("text", "")),
                        "type": ev.get("type", "unknown"),
                        "section": ev.get("section", "unknown"),
                        "strength": ev.get("strength", "moderate"),
                        "quantitative": bool(ev.get("quantitative", False)),
                        "statistical_significance": ev.get("statistical_significance", "not_reported"),
                        "supports_hypothesis": ev.get("supports_hypothesis", [])
                    }
                    normalized["evidence"].append(normalized_ev)
                elif isinstance(ev, str):
                    normalized["evidence"].append({
                        "text": ev,
                        "type": "unknown",
                        "section": "unknown",
                        "strength": "moderate",
                        "quantitative": False,
                        "statistical_significance": "not_reported",
                        "supports_hypothesis": []
                    })

        # Normalize conclusions
        if "conclusions" in data and isinstance(data["conclusions"], list):
            for conc in data["conclusions"]:
                if isinstance(conc, dict):
                    normalized_conc = {
                        "text": str(conc.get("text", "")),
                        "confidence": float(conc.get("confidence", 0.5)) if conc.get("confidence") is not None else 0.5,
                        "supported_by_evidence": conc.get("supported_by_evidence", [])
                    }
                    normalized["conclusions"].append(normalized_conc)
                elif isinstance(conc, str):
                    normalized["conclusions"].append({
                        "text": conc,
                        "confidence": 0.5,
                        "supported_by_evidence": []
                    })

        # Normalize relationships
        if "logic_relationships" in data and isinstance(data["logic_relationships"], list):
            for rel in data["logic_relationships"]:
                if isinstance(rel, dict):
                    normalized_rel = {
                        "from_type": rel.get("from_type", "unknown"),
                        "from_index": int(rel.get("from_index", 0)) if rel.get("from_index") is not None else 0,
                        "to_type": rel.get("to_type", "unknown"),
                        "to_index": int(rel.get("to_index", 0)) if rel.get("to_index") is not None else 0,
                        "relationship": rel.get("relationship", "unknown")
                    }
                    normalized["logic_relationships"].append(normalized_rel)

        return normalized


# Rate limiting decorator
def rate_limit(calls_per_minute: int = 30):
    """Rate limiting decorator for API calls."""
    min_interval = 60.0 / calls_per_minute
    last_called = [0.0]

    def decorator(func):
        def wrapper(*args, **kwargs):
            elapsed = time.time() - last_called[0]
            left_to_wait = min_interval - elapsed
            if left_to_wait > 0:
                time.sleep(left_to_wait)
            ret = func(*args, **kwargs)
            last_called[0] = time.time()
            return ret
        return wrapper
    return decorator
