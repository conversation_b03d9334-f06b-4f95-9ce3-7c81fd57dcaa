#!/usr/bin/env python3
"""Quick verification script for LLM setup and API key."""

import os
import sys
import json
from dotenv import load_dotenv

def check_environment():
    """Check environment setup."""
    print("🔍 Checking Environment Setup")
    print("=" * 40)
    
    # Load environment variables
    load_dotenv()
    
    # Check for API key
    api_key = os.getenv('OPENROUTER_API_KEY')
    if api_key:
        print(f"✅ OPENROUTER_API_KEY found (length: {len(api_key)})")
        # Mask the key for security
        masked_key = api_key[:8] + "..." + api_key[-4:] if len(api_key) > 12 else "***"
        print(f"   Key preview: {masked_key}")
    else:
        print("❌ OPENROUTER_API_KEY not found")
        print("   Please set your OpenRouter API key:")
        print("   1. Copy .env.example to .env")
        print("   2. Edit .env and add: OPENROUTER_API_KEY=your_key_here")
        print("   3. Get a key from: https://openrouter.ai/keys")
        return False
    
    # Check other environment variables
    model = os.getenv('LLM_MODEL', 'mistralai/devstral-small')
    print(f"✅ LLM Model: {model}")
    
    max_tokens = os.getenv('LLM_MAX_TOKENS', '2000')
    print(f"✅ Max Tokens: {max_tokens}")
    
    return True

def test_api_connection():
    """Test basic API connection."""
    print("\n🌐 Testing API Connection")
    print("=" * 40)
    
    try:
        from llm_client import OpenRouterClient
        
        client = OpenRouterClient()
        print("✅ LLM client initialized successfully")
        
        # Test with minimal request
        test_messages = [
            {"role": "system", "content": "You are a helpful assistant. Respond with valid JSON."},
            {"role": "user", "content": 'Return this JSON: {"status": "success", "message": "API working"}'}
        ]
        
        print("📡 Making test API call...")
        response = client._make_sync_request(test_messages, max_tokens=100, temperature=0.1)
        
        if response and "choices" in response:
            content = response["choices"][0]["message"]["content"]
            print("✅ API call successful")
            print(f"   Response: {content[:100]}...")
            
            # Try to parse as JSON
            try:
                parsed = json.loads(content)
                print("✅ Response is valid JSON")
                return True
            except json.JSONDecodeError:
                print("⚠️  Response is not valid JSON, but API is working")
                return True
        else:
            print("❌ Unexpected API response format")
            return False
            
    except Exception as e:
        print(f"❌ API test failed: {e}")
        return False

def test_extraction_methods():
    """Test the extraction methods with sample text."""
    print("\n🧪 Testing Extraction Methods")
    print("=" * 40)
    
    try:
        from llm_client import OpenRouterClient
        
        client = OpenRouterClient()
        
        # Sample scientific text
        sample_text = """
        We hypothesize that microbial diversity decreases with increasing temperature.
        Our study analyzed samples from hydrothermal vents using 16S rRNA sequencing.
        Results showed significant negative correlation (R² = 0.78, p < 0.01) between
        temperature and Shannon diversity index. We conclude that temperature is a
        major driver of microbial community structure.
        """
        
        title = "Temperature effects on microbial diversity"
        
        print("🔬 Testing hypothesis extraction...")
        hypotheses = client.extract_key_hypotheses(sample_text, title)
        print(f"   Extracted {len(hypotheses)} hypotheses")
        
        print("📊 Testing evidence extraction...")
        evidence = client.extract_lines_of_evidence(sample_text, title)
        print(f"   Extracted {len(evidence)} evidence items")
        
        print("🧠 Testing logic graph extraction...")
        logic_graph = client.extract_logic_graph(sample_text, title)
        print(f"   Logic graph components:")
        print(f"   - Hypotheses: {len(logic_graph.get('hypotheses', []))}")
        print(f"   - Evidence: {len(logic_graph.get('evidence', []))}")
        print(f"   - Conclusions: {len(logic_graph.get('conclusions', []))}")
        
        return True
        
    except Exception as e:
        print(f"❌ Extraction test failed: {e}")
        return False

def main():
    """Run all verification tests."""
    print("🚀 LLM Setup Verification")
    print("=" * 50)
    
    tests = [
        ("Environment Check", check_environment),
        ("API Connection", test_api_connection),
        ("Extraction Methods", test_extraction_methods)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} crashed: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n📋 Verification Summary")
    print("=" * 40)
    
    passed = 0
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} {test_name}")
        if result:
            passed += 1
    
    print(f"\nOverall: {passed}/{len(results)} tests passed")
    
    if passed == len(results):
        print("\n🎉 All tests passed! Your LLM setup is working correctly.")
        print("\nNext steps:")
        print("1. Run: python pdf_extractor.py --use-llm --verbose")
        print("2. Or: python test_llm_integration.py")
    else:
        print("\n⚠️  Some tests failed. Please check the issues above.")
        
        if not results[0][1]:  # Environment check failed
            print("\n🔧 Quick fix:")
            print("1. Get API key: https://openrouter.ai/keys")
            print("2. cp .env.example .env")
            print("3. Edit .env and add your key")
    
    return passed == len(results)

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
