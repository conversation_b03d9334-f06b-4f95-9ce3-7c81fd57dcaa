# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is a scientific paper analysis project that builds novelty graphs from collections of research papers. The project analyzes PDFs from Nature Microbiology (nm_test directory) to extract logic structures, compute similarities, and visualize relationships between papers as interactive network graphs.

## Key Architecture Components

The project implements a sophisticated NLP pipeline following the architecture described in compass_artifact_wf-42d27f25-47bd-486a-bfac-92e6cadb6f4a_text_markdown.md:

1. **Document Processing**: Uses GROBID for PDF extraction and structured text parsing
2. **NLP Analysis**: Leverages SciBERT/SPECTER models for scientific text understanding
3. **Similarity Computing**: Implements multi-metric similarity (semantic, structural, citation-based)
4. **Graph Construction**: Builds novelty networks using NetworkX
5. **Visualization**: Creates interactive visualizations with Py<PERSON>is and Plotly

## Common Development Commands

### Setting up the environment
```bash
# Install system dependencies (Ubuntu/Debian)
sudo apt-get update
sudo apt-get install -y python3-dev python3-pip graphviz graphviz-dev \
    cairo-dev pkg-config libgirepository1.0-dev poppler-utils

# Install Python dependencies
pip install networkx==3.1 pyvis==0.3.2 pandas numpy scipy scikit-learn \
    matplotlib plotly sentence-transformers transformers torch \
    spacy gensim nltk PyMuPDF beautifulsoup4 requests \
    scispacy grobid-client-python

# Download scientific NLP models
python -m spacy download en_core_sci_sm
```

### Running the novelty graph pipeline
```python
# Initialize the main agent
agent = ScientificNoveltyGraphAgent()

# Process PDFs in nm_test directory
import glob
paper_paths = glob.glob("nm_test/*.pdf")
results = agent.process_paper_collection(paper_paths)

# Access outputs
novelty_graph = results['graph']
visualization_path = results['visualization']
most_novel_papers = results['most_novel_papers']
```

### Starting GROBID service (required for PDF processing)
```bash
# Download and run GROBID (if not already installed)
wget https://github.com/kermitt2/grobid/archive/refs/tags/0.7.3.zip
unzip 0.7.3.zip
cd grobid-0.7.3
./gradlew run
```

## Important Implementation Details

- The project processes Nature Microbiology PDFs stored in the `nm_test/` directory
- Each PDF is analyzed to extract hypotheses, evidence, and conclusions
- Papers are embedded using SPECTER/SciBERT models (768-dimensional vectors)
- Similarity threshold for graph edges is configurable (default: 0.7)
- Novelty scores are computed as 1 - max(similarity to other papers)
- Topic clustering uses K-means on paper embeddings
- Visualizations are saved as interactive HTML files

## Data Structure

Papers are represented as dictionaries with:
- `title`: Paper title
- `abstract`: Paper abstract
- `authors`: List of authors
- `year`: Publication year
- `hypotheses`: Extracted research hypotheses
- `evidence`: Supporting experimental evidence
- `conclusions`: Main findings and implications

The novelty graph stores papers as nodes with edges weighted by similarity scores.