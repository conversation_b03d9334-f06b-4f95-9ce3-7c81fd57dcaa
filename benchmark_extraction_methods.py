#!/usr/bin/env python3
"""Benchmark LLM vs rule-based extraction methods."""

import json
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from typing import Dict, List, Tuple, Any
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.metrics.pairwise import cosine_similarity
from sklearn.metrics import r2_score
from scipy.stats import pearsonr, spearmanr
import pandas as pd
from pathlib import Path
import logging

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class ExtractionBenchmark:
    """Benchmark LLM vs rule-based extraction methods."""
    
    def __init__(self):
        self.vectorizer = TfidfVectorizer(
            max_features=500,
            stop_words='english',
            ngram_range=(1, 2),
            min_df=1
        )
        
    def load_papers_with_both_extractions(self, json_file: str) -> List[Dict[str, Any]]:
        """Load papers that have both LLM and rule-based extractions."""
        
        with open(json_file, 'r') as f:
            papers = json.load(f)
        
        # Filter papers that have both extractions
        valid_papers = []
        for paper in papers:
            if (paper.get('llm_extraction_used', False) and 
                paper.get('hypotheses') and 
                paper.get('llm_enhanced_hypotheses')):
                valid_papers.append(paper)
        
        logger.info(f"Found {len(valid_papers)} papers with both LLM and rule-based extractions")
        return valid_papers
    
    def extract_metrics_from_papers(self, papers: List[Dict[str, Any]]) -> pd.DataFrame:
        """Extract comparable metrics from both extraction methods."""
        
        metrics_data = []
        
        for i, paper in enumerate(papers):
            # Basic paper info
            paper_id = i
            title = paper.get('title', f'Paper_{i}')[:50]
            
            # Rule-based metrics
            rule_hypotheses = paper.get('hypotheses', [])
            rule_evidence = paper.get('evidence', [])
            rule_conclusions = paper.get('conclusions', [])
            
            # LLM metrics
            llm_hypotheses = paper.get('llm_enhanced_hypotheses', [])
            llm_evidence = paper.get('llm_enhanced_evidence', [])
            
            # Count metrics
            rule_hyp_count = len(rule_hypotheses)
            llm_hyp_count = len(llm_hypotheses)
            
            rule_ev_count = len(rule_evidence)
            llm_ev_count = len(llm_evidence)
            
            rule_conc_count = len(rule_conclusions)
            
            # Text length metrics
            rule_hyp_text = ' '.join([str(h) for h in rule_hypotheses])
            llm_hyp_text = ' '.join([h.get('text', '') if isinstance(h, dict) else str(h) for h in llm_hypotheses])
            
            rule_ev_text = ' '.join([e.get('text', str(e)) if isinstance(e, dict) else str(e) for e in rule_evidence])
            llm_ev_text = ' '.join([e.get('text', '') if isinstance(e, dict) else str(e) for e in llm_evidence])
            
            # Semantic similarity between methods
            hyp_similarity = self._calculate_text_similarity(rule_hyp_text, llm_hyp_text)
            ev_similarity = self._calculate_text_similarity(rule_ev_text, llm_ev_text)
            
            # LLM-specific metrics
            llm_hyp_confidence = np.mean([h.get('confidence', 0.5) for h in llm_hypotheses if isinstance(h, dict)]) if llm_hypotheses else 0
            llm_primary_hyp_count = sum(1 for h in llm_hypotheses if isinstance(h, dict) and h.get('type') == 'primary')
            
            llm_strong_evidence_count = sum(1 for e in llm_evidence if isinstance(e, dict) and e.get('strength') == 'strong')
            llm_quantitative_evidence_count = sum(1 for e in llm_evidence if isinstance(e, dict) and e.get('quantitative', False))
            
            metrics_data.append({
                'paper_id': paper_id,
                'title': title,
                'rule_hyp_count': rule_hyp_count,
                'llm_hyp_count': llm_hyp_count,
                'rule_ev_count': rule_ev_count,
                'llm_ev_count': llm_ev_count,
                'rule_conc_count': rule_conc_count,
                'rule_hyp_length': len(rule_hyp_text),
                'llm_hyp_length': len(llm_hyp_text),
                'rule_ev_length': len(rule_ev_text),
                'llm_ev_length': len(llm_ev_text),
                'hyp_similarity': hyp_similarity,
                'ev_similarity': ev_similarity,
                'llm_hyp_confidence': llm_hyp_confidence,
                'llm_primary_hyp_count': llm_primary_hyp_count,
                'llm_strong_evidence_count': llm_strong_evidence_count,
                'llm_quantitative_evidence_count': llm_quantitative_evidence_count
            })
        
        return pd.DataFrame(metrics_data)
    
    def _calculate_text_similarity(self, text1: str, text2: str) -> float:
        """Calculate cosine similarity between two texts."""
        
        if not text1.strip() or not text2.strip():
            return 0.0
        
        try:
            # Vectorize texts
            vectors = self.vectorizer.fit_transform([text1, text2])
            similarity = cosine_similarity(vectors[0:1], vectors[1:2])[0][0]
            return float(similarity)
        except Exception as e:
            logger.warning(f"Error calculating similarity: {e}")
            return 0.0
    
    def create_benchmark_plots(self, df: pd.DataFrame, output_dir: str = "benchmark_plots"):
        """Create comprehensive benchmark visualization plots."""
        
        Path(output_dir).mkdir(exist_ok=True)
        
        # Set style
        plt.style.use('seaborn-v0_8')
        sns.set_palette("husl")
        
        # 1. Count Correlation Plot
        fig, axes = plt.subplots(2, 2, figsize=(15, 12))
        fig.suptitle('LLM vs Rule-Based Extraction: Count Correlations', fontsize=16, fontweight='bold')
        
        # Hypothesis counts
        axes[0, 0].scatter(df['rule_hyp_count'], df['llm_hyp_count'], alpha=0.6, s=50)
        axes[0, 0].plot([0, df['rule_hyp_count'].max()], [0, df['rule_hyp_count'].max()], 'r--', alpha=0.8)
        r2_hyp = r2_score(df['rule_hyp_count'], df['llm_hyp_count']) if len(df) > 1 else 0
        pearson_hyp, p_hyp = pearsonr(df['rule_hyp_count'], df['llm_hyp_count']) if len(df) > 1 else (0, 1)
        axes[0, 0].set_xlabel('Rule-based Hypothesis Count')
        axes[0, 0].set_ylabel('LLM Hypothesis Count')
        axes[0, 0].set_title(f'Hypothesis Counts\nR² = {r2_hyp:.3f}, r = {pearson_hyp:.3f}')
        
        # Evidence counts
        axes[0, 1].scatter(df['rule_ev_count'], df['llm_ev_count'], alpha=0.6, s=50, color='orange')
        axes[0, 1].plot([0, df['rule_ev_count'].max()], [0, df['rule_ev_count'].max()], 'r--', alpha=0.8)
        r2_ev = r2_score(df['rule_ev_count'], df['llm_ev_count']) if len(df) > 1 else 0
        pearson_ev, p_ev = pearsonr(df['rule_ev_count'], df['llm_ev_count']) if len(df) > 1 else (0, 1)
        axes[0, 1].set_xlabel('Rule-based Evidence Count')
        axes[0, 1].set_ylabel('LLM Evidence Count')
        axes[0, 1].set_title(f'Evidence Counts\nR² = {r2_ev:.3f}, r = {pearson_ev:.3f}')
        
        # Text length correlations
        axes[1, 0].scatter(df['rule_hyp_length'], df['llm_hyp_length'], alpha=0.6, s=50, color='green')
        r2_hyp_len = r2_score(df['rule_hyp_length'], df['llm_hyp_length']) if len(df) > 1 else 0
        axes[1, 0].set_xlabel('Rule-based Hypothesis Text Length')
        axes[1, 0].set_ylabel('LLM Hypothesis Text Length')
        axes[1, 0].set_title(f'Hypothesis Text Length\nR² = {r2_hyp_len:.3f}')
        
        axes[1, 1].scatter(df['rule_ev_length'], df['llm_ev_length'], alpha=0.6, s=50, color='purple')
        r2_ev_len = r2_score(df['rule_ev_length'], df['llm_ev_length']) if len(df) > 1 else 0
        axes[1, 1].set_xlabel('Rule-based Evidence Text Length')
        axes[1, 1].set_ylabel('LLM Evidence Text Length')
        axes[1, 1].set_title(f'Evidence Text Length\nR² = {r2_ev_len:.3f}')
        
        plt.tight_layout()
        plt.savefig(f'{output_dir}/count_correlations.png', dpi=300, bbox_inches='tight')
        plt.show()
        
        # 2. Semantic Similarity Analysis
        fig, axes = plt.subplots(1, 2, figsize=(15, 6))
        fig.suptitle('Semantic Similarity Between LLM and Rule-Based Extractions', fontsize=16, fontweight='bold')
        
        # Hypothesis similarity distribution
        axes[0].hist(df['hyp_similarity'], bins=20, alpha=0.7, color='skyblue', edgecolor='black')
        axes[0].axvline(df['hyp_similarity'].mean(), color='red', linestyle='--', 
                       label=f'Mean: {df["hyp_similarity"].mean():.3f}')
        axes[0].set_xlabel('Cosine Similarity')
        axes[0].set_ylabel('Frequency')
        axes[0].set_title('Hypothesis Semantic Similarity')
        axes[0].legend()
        
        # Evidence similarity distribution
        axes[1].hist(df['ev_similarity'], bins=20, alpha=0.7, color='lightcoral', edgecolor='black')
        axes[1].axvline(df['ev_similarity'].mean(), color='red', linestyle='--',
                       label=f'Mean: {df["ev_similarity"].mean():.3f}')
        axes[1].set_xlabel('Cosine Similarity')
        axes[1].set_ylabel('Frequency')
        axes[1].set_title('Evidence Semantic Similarity')
        axes[1].legend()
        
        plt.tight_layout()
        plt.savefig(f'{output_dir}/semantic_similarity.png', dpi=300, bbox_inches='tight')
        plt.show()
        
        # 3. LLM Enhancement Analysis
        fig, axes = plt.subplots(2, 2, figsize=(15, 12))
        fig.suptitle('LLM Enhancement Quality Analysis', fontsize=16, fontweight='bold')
        
        # Confidence distribution
        axes[0, 0].hist(df['llm_hyp_confidence'], bins=15, alpha=0.7, color='gold', edgecolor='black')
        axes[0, 0].set_xlabel('Average Hypothesis Confidence')
        axes[0, 0].set_ylabel('Frequency')
        axes[0, 0].set_title(f'LLM Hypothesis Confidence\nMean: {df["llm_hyp_confidence"].mean():.3f}')
        
        # Primary vs total hypotheses
        axes[0, 1].scatter(df['llm_hyp_count'], df['llm_primary_hyp_count'], alpha=0.6, s=50, color='red')
        axes[0, 1].plot([0, df['llm_hyp_count'].max()], [0, df['llm_hyp_count'].max()], 'k--', alpha=0.5)
        axes[0, 1].set_xlabel('Total LLM Hypotheses')
        axes[0, 1].set_ylabel('Primary Hypotheses')
        axes[0, 1].set_title('Primary vs Total Hypotheses')
        
        # Evidence strength analysis
        axes[1, 0].scatter(df['llm_ev_count'], df['llm_strong_evidence_count'], alpha=0.6, s=50, color='darkgreen')
        axes[1, 0].set_xlabel('Total LLM Evidence')
        axes[1, 0].set_ylabel('Strong Evidence Count')
        axes[1, 0].set_title('Strong vs Total Evidence')
        
        # Quantitative evidence
        axes[1, 1].scatter(df['llm_ev_count'], df['llm_quantitative_evidence_count'], alpha=0.6, s=50, color='navy')
        axes[1, 1].set_xlabel('Total LLM Evidence')
        axes[1, 1].set_ylabel('Quantitative Evidence Count')
        axes[1, 1].set_title('Quantitative vs Total Evidence')
        
        plt.tight_layout()
        plt.savefig(f'{output_dir}/llm_enhancement_analysis.png', dpi=300, bbox_inches='tight')
        plt.show()
        
        return {
            'hypothesis_count_r2': r2_hyp,
            'evidence_count_r2': r2_ev,
            'hypothesis_count_correlation': pearson_hyp,
            'evidence_count_correlation': pearson_ev,
            'mean_hypothesis_similarity': df['hyp_similarity'].mean(),
            'mean_evidence_similarity': df['ev_similarity'].mean(),
            'mean_llm_confidence': df['llm_hyp_confidence'].mean()
        }
    
    def generate_benchmark_report(self, df: pd.DataFrame, stats: Dict[str, float], output_file: str = "benchmark_report.txt"):
        """Generate a comprehensive benchmark report."""
        
        with open(output_file, 'w') as f:
            f.write("=" * 60 + "\n")
            f.write("LLM vs Rule-Based Extraction Benchmark Report\n")
            f.write("=" * 60 + "\n\n")
            
            f.write(f"Dataset: {len(df)} papers with both extractions\n\n")
            
            f.write("COUNT CORRELATION ANALYSIS:\n")
            f.write("-" * 30 + "\n")
            f.write(f"Hypothesis Count R²: {stats['hypothesis_count_r2']:.3f}\n")
            f.write(f"Hypothesis Count Correlation: {stats['hypothesis_count_correlation']:.3f}\n")
            f.write(f"Evidence Count R²: {stats['evidence_count_r2']:.3f}\n")
            f.write(f"Evidence Count Correlation: {stats['evidence_count_correlation']:.3f}\n\n")
            
            f.write("SEMANTIC SIMILARITY ANALYSIS:\n")
            f.write("-" * 30 + "\n")
            f.write(f"Mean Hypothesis Similarity: {stats['mean_hypothesis_similarity']:.3f}\n")
            f.write(f"Mean Evidence Similarity: {stats['mean_evidence_similarity']:.3f}\n\n")
            
            f.write("LLM ENHANCEMENT QUALITY:\n")
            f.write("-" * 30 + "\n")
            f.write(f"Mean LLM Confidence: {stats['mean_llm_confidence']:.3f}\n")
            f.write(f"Primary Hypothesis Rate: {(df['llm_primary_hyp_count'] / df['llm_hyp_count']).mean():.3f}\n")
            f.write(f"Strong Evidence Rate: {(df['llm_strong_evidence_count'] / df['llm_ev_count']).mean():.3f}\n")
            f.write(f"Quantitative Evidence Rate: {(df['llm_quantitative_evidence_count'] / df['llm_ev_count']).mean():.3f}\n\n")
            
            f.write("SUMMARY STATISTICS:\n")
            f.write("-" * 30 + "\n")
            f.write(df.describe().to_string())
            
        logger.info(f"Benchmark report saved to {output_file}")


def main():
    """Run the benchmark analysis."""
    
    # Check if extracted papers file exists
    papers_file = "extracted_papers.json"
    if not Path(papers_file).exists():
        print(f"❌ {papers_file} not found!")
        print("Please run the extraction first:")
        print("python pdf_extractor.py --use-llm --verbose")
        return
    
    print("🔍 Starting LLM vs Rule-Based Extraction Benchmark")
    print("=" * 60)
    
    benchmark = ExtractionBenchmark()
    
    # Load papers with both extractions
    papers = benchmark.load_papers_with_both_extractions(papers_file)
    
    if len(papers) < 2:
        print("❌ Need at least 2 papers with both LLM and rule-based extractions")
        print("Please run: python pdf_extractor.py --use-llm --verbose")
        return
    
    # Extract metrics
    print("📊 Extracting comparison metrics...")
    df = benchmark.extract_metrics_from_papers(papers)
    
    # Create plots
    print("📈 Creating benchmark plots...")
    stats = benchmark.create_benchmark_plots(df)
    
    # Generate report
    print("📝 Generating benchmark report...")
    benchmark.generate_benchmark_report(df, stats)
    
    print("\n✅ Benchmark analysis complete!")
    print("📁 Check the following files:")
    print("   - benchmark_plots/count_correlations.png")
    print("   - benchmark_plots/semantic_similarity.png") 
    print("   - benchmark_plots/llm_enhancement_analysis.png")
    print("   - benchmark_report.txt")
    
    print(f"\n📊 Quick Results:")
    print(f"   - Hypothesis Count R²: {stats['hypothesis_count_r2']:.3f}")
    print(f"   - Evidence Count R²: {stats['evidence_count_r2']:.3f}")
    print(f"   - Mean Semantic Similarity: {stats['mean_hypothesis_similarity']:.3f}")


if __name__ == '__main__':
    main()
