#!/usr/bin/env python3
"""Simple alignment benchmark: R² and correlation between LLM and rule-based extraction."""

import json
import numpy as np
import matplotlib.pyplot as plt
from sklearn.metrics import r2_score
from scipy.stats import pearsonr
from pathlib import Path
import pandas as pd


def load_and_compare_extractions(json_file: str):
    """Load papers and compare LLM vs rule-based extractions."""
    
    with open(json_file, 'r') as f:
        papers = json.load(f)
    
    # Filter papers with both extractions
    valid_papers = [p for p in papers if p.get('llm_extraction_used', False)]
    
    print(f"📊 Found {len(valid_papers)} papers with LLM extractions")
    print(f"📊 Total papers: {len(papers)}")
    
    if len(valid_papers) < 2:
        print("❌ Need at least 2 papers with LLM extractions for comparison")
        return None
    
    # Extract counts for comparison
    data = []
    for i, paper in enumerate(valid_papers):
        # Rule-based counts
        rule_hyp_count = len(paper.get('hypotheses', []))
        rule_ev_count = len(paper.get('evidence', []))
        rule_conc_count = len(paper.get('conclusions', []))
        
        # LLM counts
        llm_hyp_count = len(paper.get('llm_enhanced_hypotheses', []))
        llm_ev_count = len(paper.get('llm_enhanced_evidence', []))
        
        # Text lengths
        rule_hyp_text = ' '.join([str(h) for h in paper.get('hypotheses', [])])
        llm_hyp_text = ' '.join([
            h.get('text', '') if isinstance(h, dict) else str(h) 
            for h in paper.get('llm_enhanced_hypotheses', [])
        ])
        
        data.append({
            'paper_id': i,
            'title': paper.get('title', f'Paper_{i}')[:30],
            'rule_hyp_count': rule_hyp_count,
            'llm_hyp_count': llm_hyp_count,
            'rule_ev_count': rule_ev_count,
            'llm_ev_count': llm_ev_count,
            'rule_conc_count': rule_conc_count,
            'rule_hyp_length': len(rule_hyp_text),
            'llm_hyp_length': len(llm_hyp_text)
        })
    
    return pd.DataFrame(data)


def create_alignment_plots(df):
    """Create R² and correlation plots."""
    
    fig, axes = plt.subplots(2, 2, figsize=(14, 10))
    fig.suptitle('LLM vs Rule-Based Extraction Alignment', fontsize=16, fontweight='bold')
    
    # 1. Hypothesis Count Alignment
    x_hyp = df['rule_hyp_count']
    y_hyp = df['llm_hyp_count']
    
    if len(df) > 1 and x_hyp.var() > 0:
        r2_hyp = r2_score(x_hyp, y_hyp)
        corr_hyp, p_hyp = pearsonr(x_hyp, y_hyp)
    else:
        r2_hyp, corr_hyp, p_hyp = 0, 0, 1
    
    axes[0, 0].scatter(x_hyp, y_hyp, alpha=0.7, s=60, color='blue')
    
    # Add perfect alignment line
    max_val = max(x_hyp.max(), y_hyp.max())
    axes[0, 0].plot([0, max_val], [0, max_val], 'r--', alpha=0.8, label='Perfect Alignment')
    
    # Add trend line
    if len(df) > 1:
        z = np.polyfit(x_hyp, y_hyp, 1)
        p = np.poly1d(z)
        axes[0, 0].plot(x_hyp, p(x_hyp), "g-", alpha=0.8, label='Trend Line')
    
    axes[0, 0].set_xlabel('Rule-Based Hypothesis Count')
    axes[0, 0].set_ylabel('LLM Hypothesis Count')
    axes[0, 0].set_title(f'Hypothesis Counts\nR² = {r2_hyp:.3f}, r = {corr_hyp:.3f} (p={p_hyp:.3f})')
    axes[0, 0].legend()
    axes[0, 0].grid(True, alpha=0.3)
    
    # 2. Evidence Count Alignment
    x_ev = df['rule_ev_count']
    y_ev = df['llm_ev_count']
    
    if len(df) > 1 and x_ev.var() > 0:
        r2_ev = r2_score(x_ev, y_ev)
        corr_ev, p_ev = pearsonr(x_ev, y_ev)
    else:
        r2_ev, corr_ev, p_ev = 0, 0, 1
    
    axes[0, 1].scatter(x_ev, y_ev, alpha=0.7, s=60, color='orange')
    
    max_val = max(x_ev.max(), y_ev.max())
    axes[0, 1].plot([0, max_val], [0, max_val], 'r--', alpha=0.8, label='Perfect Alignment')
    
    if len(df) > 1:
        z = np.polyfit(x_ev, y_ev, 1)
        p = np.poly1d(z)
        axes[0, 1].plot(x_ev, p(x_ev), "g-", alpha=0.8, label='Trend Line')
    
    axes[0, 1].set_xlabel('Rule-Based Evidence Count')
    axes[0, 1].set_ylabel('LLM Evidence Count')
    axes[0, 1].set_title(f'Evidence Counts\nR² = {r2_ev:.3f}, r = {corr_ev:.3f} (p={p_ev:.3f})')
    axes[0, 1].legend()
    axes[0, 1].grid(True, alpha=0.3)
    
    # 3. Text Length Alignment
    x_len = df['rule_hyp_length']
    y_len = df['llm_hyp_length']
    
    if len(df) > 1 and x_len.var() > 0:
        r2_len = r2_score(x_len, y_len)
        corr_len, p_len = pearsonr(x_len, y_len)
    else:
        r2_len, corr_len, p_len = 0, 0, 1
    
    axes[1, 0].scatter(x_len, y_len, alpha=0.7, s=60, color='green')
    
    if len(df) > 1:
        z = np.polyfit(x_len, y_len, 1)
        p = np.poly1d(z)
        axes[1, 0].plot(x_len, p(x_len), "g-", alpha=0.8, label='Trend Line')
    
    axes[1, 0].set_xlabel('Rule-Based Hypothesis Text Length')
    axes[1, 0].set_ylabel('LLM Hypothesis Text Length')
    axes[1, 0].set_title(f'Text Lengths\nR² = {r2_len:.3f}, r = {corr_len:.3f} (p={p_len:.3f})')
    axes[1, 0].legend()
    axes[1, 0].grid(True, alpha=0.3)
    
    # 4. Overall Comparison Summary
    axes[1, 1].axis('off')
    
    # Summary statistics
    summary_text = f"""
    ALIGNMENT SUMMARY
    ═══════════════════
    
    Hypothesis Counts:
    • R² = {r2_hyp:.3f}
    • Correlation = {corr_hyp:.3f}
    • p-value = {p_hyp:.3f}
    
    Evidence Counts:
    • R² = {r2_ev:.3f}
    • Correlation = {corr_ev:.3f}
    • p-value = {p_ev:.3f}
    
    Text Lengths:
    • R² = {r2_len:.3f}
    • Correlation = {corr_len:.3f}
    • p-value = {p_len:.3f}
    
    Dataset: {len(df)} papers
    
    Interpretation:
    • R² > 0.7: Strong alignment
    • R² 0.3-0.7: Moderate alignment
    • R² < 0.3: Weak alignment
    """
    
    axes[1, 1].text(0.1, 0.9, summary_text, transform=axes[1, 1].transAxes, 
                   fontsize=10, verticalalignment='top', fontfamily='monospace',
                   bbox=dict(boxstyle="round,pad=0.3", facecolor="lightgray", alpha=0.8))
    
    plt.tight_layout()
    plt.savefig('extraction_alignment_benchmark.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    return {
        'hypothesis_r2': r2_hyp,
        'hypothesis_correlation': corr_hyp,
        'evidence_r2': r2_ev,
        'evidence_correlation': corr_ev,
        'text_length_r2': r2_len,
        'text_length_correlation': corr_len
    }


def print_detailed_comparison(df):
    """Print detailed paper-by-paper comparison."""
    
    print("\n" + "="*80)
    print("DETAILED PAPER-BY-PAPER COMPARISON")
    print("="*80)
    
    print(f"{'Paper':<30} {'Rule H':<8} {'LLM H':<8} {'Rule E':<8} {'LLM E':<8} {'Ratio H':<8} {'Ratio E':<8}")
    print("-"*80)
    
    for _, row in df.iterrows():
        ratio_h = row['llm_hyp_count'] / max(row['rule_hyp_count'], 1)
        ratio_e = row['llm_ev_count'] / max(row['rule_ev_count'], 1)
        
        print(f"{row['title']:<30} {row['rule_hyp_count']:<8} {row['llm_hyp_count']:<8} "
              f"{row['rule_ev_count']:<8} {row['llm_ev_count']:<8} {ratio_h:<8.2f} {ratio_e:<8.2f}")
    
    print("\nLegend: H=Hypotheses, E=Evidence, Ratio=LLM/Rule-based")


def main():
    """Run the simple alignment benchmark."""
    
    papers_file = "extracted_papers.json"
    
    if not Path(papers_file).exists():
        print(f"❌ {papers_file} not found!")
        print("Run: python pdf_extractor.py --use-llm --verbose")
        return
    
    print("🔍 LLM vs Rule-Based Extraction Alignment Benchmark")
    print("="*60)
    
    # Load and compare
    df = load_and_compare_extractions(papers_file)
    
    if df is None:
        return
    
    # Create alignment plots
    print("📈 Creating alignment plots...")
    stats = create_alignment_plots(df)
    
    # Print results
    print("\n📊 ALIGNMENT RESULTS:")
    print("-"*40)
    print(f"Hypothesis Count R²: {stats['hypothesis_r2']:.3f}")
    print(f"Hypothesis Correlation: {stats['hypothesis_correlation']:.3f}")
    print(f"Evidence Count R²: {stats['evidence_r2']:.3f}")
    print(f"Evidence Correlation: {stats['evidence_correlation']:.3f}")
    print(f"Text Length R²: {stats['text_length_r2']:.3f}")
    print(f"Text Length Correlation: {stats['text_length_correlation']:.3f}")
    
    # Interpretation
    print(f"\n🎯 INTERPRETATION:")
    print("-"*40)
    
    avg_r2 = np.mean([stats['hypothesis_r2'], stats['evidence_r2'], stats['text_length_r2']])
    
    if avg_r2 > 0.7:
        print("✅ STRONG alignment between LLM and rule-based methods")
    elif avg_r2 > 0.3:
        print("⚠️  MODERATE alignment between LLM and rule-based methods")
    else:
        print("❌ WEAK alignment between LLM and rule-based methods")
    
    print(f"Average R²: {avg_r2:.3f}")
    
    # Detailed comparison
    print_detailed_comparison(df)
    
    print(f"\n📁 Plot saved as: extraction_alignment_benchmark.png")


if __name__ == '__main__':
    main()
