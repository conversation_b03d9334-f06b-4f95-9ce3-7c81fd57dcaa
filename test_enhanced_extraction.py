#!/usr/bin/env python3
"""Test enhanced extraction methods and create detailed comparisons."""

import json
import logging
from pathlib import Path
from pdf_extractor import PDFExtractor
from enhanced_extraction_methods import EnhancedRuleBasedExtractor, EnhancedLLMExtractor, create_side_by_side_comparison

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')


def test_enhanced_methods_on_sample():
    """Test enhanced extraction methods on a sample paper."""
    
    print("🧪 TESTING ENHANCED EXTRACTION METHODS")
    print("=" * 60)
    
    # Find a sample PDF
    pdf_files = list(Path("nm_test").glob("*.pdf"))
    if not pdf_files:
        print("❌ No PDF files found in nm_test directory")
        return
    
    sample_pdf = pdf_files[0]
    print(f"Testing with: {sample_pdf.name}")
    
    # Extract text with enhanced PDF extractor
    print("\n📄 Extracting PDF text with enhanced methods...")
    extractor = PDFExtractor(use_llm=False)  # Just for text extraction
    paper_data = extractor.extract_pdf_text(str(sample_pdf))
    
    if not paper_data:
        print("❌ Failed to extract PDF text")
        return
    
    print(f"✅ Extracted {paper_data['extraction_metadata']['total_characters']} characters")
    print(f"   Pages: {paper_data['pages']}")
    print(f"   Sections found: {list(paper_data['sections'].keys())}")
    
    # Test enhanced rule-based extraction
    print("\n📝 Testing enhanced rule-based extraction...")
    rule_extractor = EnhancedRuleBasedExtractor()
    rule_results = rule_extractor.extract_with_citations(paper_data)
    
    print(f"✅ Rule-based results:")
    print(f"   Hypotheses: {len(rule_results['hypotheses'])}")
    print(f"   Evidence: {len(rule_results['evidence'])}")
    
    # Show sample rule-based results
    if rule_results['hypotheses']:
        print(f"   Sample hypothesis: {rule_results['hypotheses'][0].text[:100]}...")
        print(f"   From section: {rule_results['hypotheses'][0].section}")
    
    # Test enhanced LLM extraction
    print("\n🤖 Testing enhanced LLM extraction...")
    try:
        llm_extractor = EnhancedLLMExtractor()
        llm_results = llm_extractor.extract_with_citations(paper_data)
        
        print(f"✅ LLM results:")
        print(f"   Hypotheses: {len(llm_results['hypotheses'])}")
        print(f"   Evidence: {len(llm_results['evidence'])}")
        
        # Show sample LLM results
        if llm_results['hypotheses']:
            print(f"   Sample hypothesis: {llm_results['hypotheses'][0].text[:100]}...")
            print(f"   From section: {llm_results['hypotheses'][0].section}")
            print(f"   Confidence: {llm_results['hypotheses'][0].confidence}")
        
    except Exception as e:
        print(f"❌ LLM extraction failed: {e}")
        llm_results = {'hypotheses': [], 'evidence': []}
    
    # Create side-by-side comparison
    print("\n📊 Creating detailed comparison...")
    create_side_by_side_comparison(paper_data, "enhanced_extraction_comparison.txt")
    
    # Compare with original methods
    print("\n🔄 Comparing with original extraction methods...")
    original_extractor = PDFExtractor(use_llm=True)
    original_logic = original_extractor.extract_logic_structure(paper_data)
    
    print(f"Original rule-based:")
    print(f"   Hypotheses: {len(original_logic['hypotheses'])}")
    print(f"   Evidence: {len(original_logic['evidence'])}")
    
    if original_extractor.use_llm:
        try:
            original_llm_hyp = original_extractor.extract_enhanced_hypotheses(paper_data)
            original_llm_ev = original_extractor.extract_enhanced_evidence(paper_data)
            
            print(f"Original LLM:")
            print(f"   Hypotheses: {len(original_llm_hyp)}")
            print(f"   Evidence: {len(original_llm_ev)}")
        except Exception as e:
            print(f"Original LLM failed: {e}")
    
    # Summary comparison
    print(f"\n📈 EXTRACTION COMPARISON SUMMARY")
    print("-" * 50)
    print(f"{'Method':<20} {'Hypotheses':<12} {'Evidence':<10}")
    print("-" * 50)
    print(f"{'Enhanced Rule-based':<20} {len(rule_results['hypotheses']):<12} {len(rule_results['evidence']):<10}")
    print(f"{'Enhanced LLM':<20} {len(llm_results['hypotheses']):<12} {len(llm_results['evidence']):<10}")
    print(f"{'Original Rule-based':<20} {len(original_logic['hypotheses']):<12} {len(original_logic['evidence']):<10}")
    
    return {
        'paper_data': paper_data,
        'enhanced_rule': rule_results,
        'enhanced_llm': llm_results,
        'original_logic': original_logic
    }


def test_text_processing_quality():
    """Test text processing quality improvements."""
    
    print("\n🔍 TESTING TEXT PROCESSING QUALITY")
    print("=" * 50)
    
    # Find a sample PDF
    pdf_files = list(Path("nm_test").glob("*.pdf"))
    if not pdf_files:
        print("❌ No PDF files found")
        return
    
    sample_pdf = pdf_files[0]
    
    # Compare original vs enhanced extraction
    print("📄 Comparing original vs enhanced PDF extraction...")
    
    # Original extraction
    original_extractor = PDFExtractor(use_llm=False)
    original_data = original_extractor.extract_pdf_text(str(sample_pdf))
    
    if original_data:
        original_chars = len(original_data['full_text'])
        original_sections = len(original_data['sections'])
        print(f"Original extraction: {original_chars} chars, {original_sections} sections")
    else:
        print("❌ Original extraction failed")
        return
    
    # Check for common issues
    original_text = original_data['full_text']
    
    # Check for truncation
    truncation_indicators = original_text.count('...') + original_text.count('…')
    print(f"Truncation indicators: {truncation_indicators}")
    
    # Check for line break issues
    single_char_lines = len([line for line in original_text.split('\n') if len(line.strip()) == 1])
    print(f"Single character lines: {single_char_lines}")
    
    # Check for special character issues
    special_char_ratio = len([c for c in original_text if not c.isalnum() and c not in ' \n\t.,;:!?-()[]']) / len(original_text)
    print(f"Special character ratio: {special_char_ratio:.3f}")
    
    # Section quality
    print(f"\nSection analysis:")
    for section_name, section_text in original_data['sections'].items():
        print(f"  {section_name}: {len(section_text)} chars")
        if len(section_text) < 100:
            print(f"    ⚠️  Very short section")
    
    return original_data


def create_alignment_test_data():
    """Create test data specifically for alignment testing."""
    
    print("\n🎯 CREATING ALIGNMENT TEST DATA")
    print("=" * 50)
    
    # Process multiple papers with enhanced methods
    pdf_files = list(Path("nm_test").glob("*.pdf"))[:5]  # Test with first 5 papers
    
    if not pdf_files:
        print("❌ No PDF files found")
        return
    
    alignment_data = []
    
    for i, pdf_path in enumerate(pdf_files):
        print(f"\nProcessing {i+1}/{len(pdf_files)}: {pdf_path.name}")
        
        try:
            # Extract with enhanced methods
            extractor = PDFExtractor(use_llm=False)
            paper_data = extractor.extract_pdf_text(str(pdf_path))
            
            if not paper_data:
                print(f"  ❌ Failed to extract text")
                continue
            
            # Enhanced rule-based
            rule_extractor = EnhancedRuleBasedExtractor()
            rule_results = rule_extractor.extract_with_citations(paper_data)
            
            # Enhanced LLM
            try:
                llm_extractor = EnhancedLLMExtractor()
                llm_results = llm_extractor.extract_with_citations(paper_data)
                llm_success = True
            except Exception as e:
                print(f"  ⚠️  LLM extraction failed: {e}")
                llm_results = {'hypotheses': [], 'evidence': []}
                llm_success = False
            
            # Store results
            paper_result = {
                'file_path': str(pdf_path),
                'title': paper_data.get('title', ''),
                'extraction_metadata': paper_data.get('extraction_metadata', {}),
                'enhanced_rule_based': {
                    'hypotheses_count': len(rule_results['hypotheses']),
                    'evidence_count': len(rule_results['evidence']),
                    'hypotheses': [{'text': h.text, 'section': h.section, 'type': h.item_type} for h in rule_results['hypotheses']],
                    'evidence': [{'text': e.text, 'section': e.section, 'type': e.item_type} for e in rule_results['evidence']]
                },
                'enhanced_llm': {
                    'hypotheses_count': len(llm_results['hypotheses']),
                    'evidence_count': len(llm_results['evidence']),
                    'hypotheses': [{'text': h.text, 'section': h.section, 'type': h.item_type, 'confidence': h.confidence} for h in llm_results['hypotheses']],
                    'evidence': [{'text': e.text, 'section': e.section, 'type': e.item_type, 'confidence': e.confidence} for e in llm_results['evidence']],
                    'extraction_success': llm_success
                }
            }
            
            alignment_data.append(paper_result)
            
            print(f"  ✅ Rule: {len(rule_results['hypotheses'])}H/{len(rule_results['evidence'])}E")
            print(f"     LLM: {len(llm_results['hypotheses'])}H/{len(llm_results['evidence'])}E")
            
        except Exception as e:
            print(f"  ❌ Processing failed: {e}")
            continue
    
    # Save alignment test data
    with open('enhanced_alignment_test_data.json', 'w') as f:
        json.dump(alignment_data, f, indent=2)
    
    print(f"\n✅ Created alignment test data with {len(alignment_data)} papers")
    print("📁 Saved to: enhanced_alignment_test_data.json")
    
    return alignment_data


def main():
    """Run all enhanced extraction tests."""
    
    print("🚀 ENHANCED EXTRACTION TESTING SUITE")
    print("=" * 70)
    
    # Test 1: Enhanced methods on sample
    sample_results = test_enhanced_methods_on_sample()
    
    # Test 2: Text processing quality
    text_quality = test_text_processing_quality()
    
    # Test 3: Create alignment test data
    alignment_data = create_alignment_test_data()
    
    print(f"\n🎯 TESTING COMPLETE")
    print("=" * 50)
    print("Files created:")
    print("  - enhanced_extraction_comparison.txt")
    print("  - enhanced_alignment_test_data.json")
    
    print(f"\nNext steps:")
    print("1. Review enhanced_extraction_comparison.txt for detailed analysis")
    print("2. Run enhanced alignment benchmark on the new data")
    print("3. Compare results with original methods")


if __name__ == '__main__':
    main()
