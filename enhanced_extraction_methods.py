#!/usr/bin/env python3
"""Enhanced extraction methods with improved text processing and alignment."""

import re
import json
import logging
from typing import Dict, List, Tuple, Any
from dataclasses import dataclass
from llm_client import OpenRouterClient

logger = logging.getLogger(__name__)


@dataclass
class ExtractedItem:
    """Standardized structure for extracted items with source tracking."""
    text: str
    section: str
    start_char: int
    end_char: int
    confidence: float = 0.5
    item_type: str = "unknown"
    exact_quote: bool = True


class TextPreprocessor:
    """Enhanced text preprocessing with validation and section tracking."""
    
    def __init__(self):
        self.section_patterns = {
            'abstract': re.compile(r'\b(abstract|summary)\b', re.IGNORECASE),
            'introduction': re.compile(r'\b(introduction|background)\b', re.IGNORECASE),
            'methods': re.compile(r'\b(method|material|procedure|approach|experimental)\b', re.IGNORECASE),
            'results': re.compile(r'\b(result|finding|observation)\b', re.IGNORECASE),
            'discussion': re.compile(r'\b(discussion|conclusion|implication)\b', re.IGNORECASE),
            'references': re.compile(r'\b(reference|bibliography|citation)\b', re.IGNORECASE)
        }
    
    def preprocess_text(self, text: str) -> Dict[str, Any]:
        """Enhanced text preprocessing with validation."""
        
        # Check for truncation issues
        truncation_indicators = ['...', '…', '[truncated]', '[continued]']
        is_truncated = any(indicator in text for indicator in truncation_indicators)
        
        # Preserve paragraph structure
        paragraphs = text.split('\n\n')
        clean_paragraphs = []
        
        for para in paragraphs:
            # Clean but preserve structure
            clean_para = re.sub(r'\s+', ' ', para.strip())
            if clean_para and len(clean_para) > 10:  # Filter very short fragments
                clean_paragraphs.append(clean_para)
        
        processed_text = '\n\n'.join(clean_paragraphs)
        
        # Identify sections with character positions
        sections = self._identify_sections_with_positions(processed_text)
        
        return {
            'processed_text': processed_text,
            'sections': sections,
            'is_truncated': is_truncated,
            'original_length': len(text),
            'processed_length': len(processed_text),
            'paragraph_count': len(clean_paragraphs)
        }
    
    def _identify_sections_with_positions(self, text: str) -> Dict[str, Dict[str, Any]]:
        """Identify sections with character positions for precise extraction."""
        
        sections = {}
        lines = text.split('\n')
        current_pos = 0
        current_section = 'unknown'
        section_start = 0
        
        for line in lines:
            line_lower = line.lower().strip()
            
            # Check if this line matches any section pattern
            for section_name, pattern in self.section_patterns.items():
                if pattern.search(line_lower) and len(line_lower) < 100:  # Section headers are usually short
                    # Save previous section
                    if current_section != 'unknown':
                        section_text = text[section_start:current_pos].strip()
                        if section_text:
                            sections[current_section] = {
                                'text': section_text,
                                'start_pos': section_start,
                                'end_pos': current_pos,
                                'length': len(section_text)
                            }
                    
                    # Start new section
                    current_section = section_name
                    section_start = current_pos
                    break
            
            current_pos += len(line) + 1  # +1 for newline
        
        # Save last section
        if current_section != 'unknown':
            section_text = text[section_start:].strip()
            if section_text:
                sections[current_section] = {
                    'text': section_text,
                    'start_pos': section_start,
                    'end_pos': len(text),
                    'length': len(section_text)
                }
        
        return sections


class EnhancedRuleBasedExtractor:
    """Enhanced rule-based extractor with precise text tracking."""
    
    def __init__(self):
        self.preprocessor = TextPreprocessor()
        
        # More precise hypothesis patterns
        self.hypothesis_patterns = [
            (r"we hypothesize(?:d)? that (.+?)(?:\.|;|$)", "explicit_hypothesis"),
            (r"our hypothesis (?:is|was) that (.+?)(?:\.|;|$)", "explicit_hypothesis"),
            (r"we propose(?:d)? that (.+?)(?:\.|;|$)", "proposal"),
            (r"we predict(?:ed)? that (.+?)(?:\.|;|$)", "prediction"),
            (r"we expect(?:ed)? that (.+?)(?:\.|;|$)", "expectation"),
            (r"hypothesis: (.+?)(?:\.|;|$)", "labeled_hypothesis")
        ]
        
        # More precise evidence patterns
        self.evidence_patterns = [
            (r"(?:we|our) (?:found|observed|measured|detected|discovered) that (.+?)(?:\.|;|$)", "experimental_finding"),
            (r"results (?:show|indicate|demonstrate|reveal) that (.+?)(?:\.|;|$)", "result_statement"),
            (r"analysis (?:revealed|showed|indicated) that (.+?)(?:\.|;|$)", "analytical_finding"),
            (r"data (?:show|indicate|suggest) that (.+?)(?:\.|;|$)", "data_evidence"),
            (r"(?:significant|substantial) (?:increase|decrease|difference|effect) (.+?)(?:\.|;|$)", "statistical_evidence"),
            (r"p\s*[<>=]\s*0\.\d+", "statistical_significance")
        ]
    
    def extract_with_citations(self, paper_data: Dict[str, Any]) -> Dict[str, List[ExtractedItem]]:
        """Extract items with precise source citations."""
        
        # Preprocess text
        processed = self.preprocessor.preprocess_text(paper_data.get('full_text', ''))
        
        results = {
            'hypotheses': [],
            'evidence': [],
            'conclusions': [],
            'preprocessing_info': processed
        }
        
        # Extract from each section
        for section_name, section_data in processed['sections'].items():
            section_text = section_data['text']
            section_start = section_data['start_pos']
            
            # Extract hypotheses
            for pattern, pattern_type in self.hypothesis_patterns:
                for match in re.finditer(pattern, section_text, re.IGNORECASE | re.DOTALL):
                    hypothesis_text = match.group(1).strip()
                    
                    if len(hypothesis_text) > 15 and len(hypothesis_text) < 500:  # Reasonable length
                        item = ExtractedItem(
                            text=hypothesis_text,
                            section=section_name,
                            start_char=section_start + match.start(),
                            end_char=section_start + match.end(),
                            confidence=0.8,
                            item_type=pattern_type,
                            exact_quote=True
                        )
                        results['hypotheses'].append(item)
            
            # Extract evidence
            for pattern, pattern_type in self.evidence_patterns:
                for match in re.finditer(pattern, section_text, re.IGNORECASE | re.DOTALL):
                    if match.groups():
                        evidence_text = match.group(1).strip()
                    else:
                        evidence_text = match.group(0).strip()
                    
                    if len(evidence_text) > 10 and len(evidence_text) < 500:
                        item = ExtractedItem(
                            text=evidence_text,
                            section=section_name,
                            start_char=section_start + match.start(),
                            end_char=section_start + match.end(),
                            confidence=0.7,
                            item_type=pattern_type,
                            exact_quote=True
                        )
                        results['evidence'].append(item)
        
        return results


class EnhancedLLMExtractor:
    """Enhanced LLM extractor with citation-aware prompts."""
    
    def __init__(self):
        self.client = OpenRouterClient()
        self.preprocessor = TextPreprocessor()
    
    def extract_with_citations(self, paper_data: Dict[str, Any]) -> Dict[str, List[ExtractedItem]]:
        """Extract items with LLM using citation-aware prompts."""
        
        # Preprocess text
        processed = self.preprocessor.preprocess_text(paper_data.get('full_text', ''))
        
        # Create section-aware prompt
        sections_text = self._create_sections_prompt(processed['sections'])
        
        system_prompt = """You are an expert scientific paper analyst. Extract hypotheses and evidence with EXACT CITATIONS.

CRITICAL REQUIREMENTS:
1. Use EXACT quotes from the paper - do not paraphrase
2. Include section name where each item was found
3. Preserve original scientific terminology
4. Distinguish between hypotheses (predictions/expectations) and evidence (findings/results)

Return JSON with this EXACT structure:
{
    "hypotheses": [
        {
            "text": "EXACT quote from paper",
            "section": "introduction|methods|results|discussion|abstract",
            "type": "explicit_hypothesis|prediction|expectation|proposal",
            "confidence": 0.0-1.0
        }
    ],
    "evidence": [
        {
            "text": "EXACT quote from paper", 
            "section": "methods|results|discussion",
            "type": "experimental_finding|statistical_evidence|analytical_finding|data_evidence",
            "confidence": 0.0-1.0
        }
    ]
}

Focus on:
- EXACT quotations, not summaries
- Clear distinction: hypotheses = what authors predicted, evidence = what they found
- Section-specific extraction"""

        user_prompt = f"""Extract hypotheses and evidence from this scientific paper using EXACT quotes:

Title: {paper_data.get('title', '')}

{sections_text}

Return ONLY the JSON with exact quotes and section citations."""

        try:
            messages = [
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": user_prompt}
            ]
            
            response = self.client._make_sync_request(messages, max_tokens=2000, temperature=0.1)
            content = response["choices"][0]["message"]["content"]
            
            # Parse with robust method
            parsed_data = self.client._robust_json_parse(content, "object")
            
            if parsed_data:
                return self._convert_to_extracted_items(parsed_data, processed)
            else:
                logger.warning("LLM extraction failed - no valid JSON returned")
                return {'hypotheses': [], 'evidence': [], 'preprocessing_info': processed}
                
        except Exception as e:
            logger.error(f"LLM extraction error: {e}")
            return {'hypotheses': [], 'evidence': [], 'preprocessing_info': processed}
    
    def _create_sections_prompt(self, sections: Dict[str, Dict[str, Any]]) -> str:
        """Create section-aware prompt text."""
        
        sections_text = ""
        for section_name, section_data in sections.items():
            section_text = section_data['text']
            # Limit section length for LLM context
            if len(section_text) > 1000:
                section_text = section_text[:1000] + "..."
            
            sections_text += f"\n\n=== {section_name.upper()} SECTION ===\n{section_text}"
        
        return sections_text
    
    def _convert_to_extracted_items(self, parsed_data: Dict[str, Any], processed: Dict[str, Any]) -> Dict[str, List[ExtractedItem]]:
        """Convert LLM output to ExtractedItem objects."""
        
        results = {
            'hypotheses': [],
            'evidence': [],
            'preprocessing_info': processed
        }
        
        # Convert hypotheses
        for hyp in parsed_data.get('hypotheses', []):
            if isinstance(hyp, dict) and hyp.get('text'):
                # Try to find the exact position in the source text
                text = hyp['text'].strip()
                section = hyp.get('section', 'unknown')
                
                # Find position in source text
                start_pos, end_pos = self._find_text_position(text, processed['processed_text'])
                
                item = ExtractedItem(
                    text=text,
                    section=section,
                    start_char=start_pos,
                    end_char=end_pos,
                    confidence=float(hyp.get('confidence', 0.5)),
                    item_type=hyp.get('type', 'llm_hypothesis'),
                    exact_quote=False  # LLM may paraphrase despite instructions
                )
                results['hypotheses'].append(item)
        
        # Convert evidence
        for ev in parsed_data.get('evidence', []):
            if isinstance(ev, dict) and ev.get('text'):
                text = ev['text'].strip()
                section = ev.get('section', 'unknown')
                
                start_pos, end_pos = self._find_text_position(text, processed['processed_text'])
                
                item = ExtractedItem(
                    text=text,
                    section=section,
                    start_char=start_pos,
                    end_char=end_pos,
                    confidence=float(ev.get('confidence', 0.5)),
                    item_type=ev.get('type', 'llm_evidence'),
                    exact_quote=False
                )
                results['evidence'].append(item)
        
        return results
    
    def _find_text_position(self, text: str, source_text: str) -> Tuple[int, int]:
        """Find the position of text in source, handling approximate matches."""
        
        # Try exact match first
        pos = source_text.find(text)
        if pos != -1:
            return pos, pos + len(text)
        
        # Try case-insensitive match
        pos = source_text.lower().find(text.lower())
        if pos != -1:
            return pos, pos + len(text)
        
        # Try partial match (first 50 characters)
        if len(text) > 50:
            partial = text[:50]
            pos = source_text.find(partial)
            if pos != -1:
                return pos, pos + len(partial)
        
        # No match found
        return -1, -1


def create_side_by_side_comparison(paper_data: Dict[str, Any], output_file: str = "extraction_comparison.txt"):
    """Create detailed side-by-side comparison of extraction methods."""
    
    print("🔍 Creating side-by-side extraction comparison...")
    
    # Initialize extractors
    rule_extractor = EnhancedRuleBasedExtractor()
    llm_extractor = EnhancedLLMExtractor()
    
    # Extract with both methods
    rule_results = rule_extractor.extract_with_citations(paper_data)
    llm_results = llm_extractor.extract_with_citations(paper_data)
    
    # Create comparison report
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write("=" * 80 + "\n")
        f.write("SIDE-BY-SIDE EXTRACTION COMPARISON\n")
        f.write("=" * 80 + "\n\n")
        
        f.write(f"Paper: {paper_data.get('title', 'Unknown')}\n")
        f.write(f"Pages: {paper_data.get('pages', 'Unknown')}\n\n")
        
        # Preprocessing info
        rule_info = rule_results.get('preprocessing_info', {})
        llm_info = llm_results.get('preprocessing_info', {})
        
        f.write("TEXT PREPROCESSING COMPARISON:\n")
        f.write("-" * 40 + "\n")
        f.write(f"Original text length: {rule_info.get('original_length', 0)}\n")
        f.write(f"Rule-based processed length: {rule_info.get('processed_length', 0)}\n")
        f.write(f"LLM processed length: {llm_info.get('processed_length', 0)}\n")
        f.write(f"Text truncated: {rule_info.get('is_truncated', False)}\n\n")
        
        # Hypotheses comparison
        f.write("HYPOTHESES COMPARISON:\n")
        f.write("-" * 40 + "\n")
        f.write(f"Rule-based found: {len(rule_results['hypotheses'])}\n")
        f.write(f"LLM found: {len(llm_results['hypotheses'])}\n\n")
        
        for i, item in enumerate(rule_results['hypotheses']):
            f.write(f"RULE-BASED HYPOTHESIS {i+1}:\n")
            f.write(f"  Text: {item.text}\n")
            f.write(f"  Section: {item.section}\n")
            f.write(f"  Type: {item.item_type}\n")
            f.write(f"  Position: {item.start_char}-{item.end_char}\n\n")
        
        for i, item in enumerate(llm_results['hypotheses']):
            f.write(f"LLM HYPOTHESIS {i+1}:\n")
            f.write(f"  Text: {item.text}\n")
            f.write(f"  Section: {item.section}\n")
            f.write(f"  Type: {item.item_type}\n")
            f.write(f"  Confidence: {item.confidence}\n\n")
        
        # Evidence comparison
        f.write("EVIDENCE COMPARISON:\n")
        f.write("-" * 40 + "\n")
        f.write(f"Rule-based found: {len(rule_results['evidence'])}\n")
        f.write(f"LLM found: {len(llm_results['evidence'])}\n\n")
        
        for i, item in enumerate(rule_results['evidence'][:5]):  # First 5
            f.write(f"RULE-BASED EVIDENCE {i+1}:\n")
            f.write(f"  Text: {item.text}\n")
            f.write(f"  Section: {item.section}\n")
            f.write(f"  Type: {item.item_type}\n\n")
        
        for i, item in enumerate(llm_results['evidence'][:5]):  # First 5
            f.write(f"LLM EVIDENCE {i+1}:\n")
            f.write(f"  Text: {item.text}\n")
            f.write(f"  Section: {item.section}\n")
            f.write(f"  Type: {item.item_type}\n\n")
    
    print(f"✅ Comparison saved to: {output_file}")
    return rule_results, llm_results
