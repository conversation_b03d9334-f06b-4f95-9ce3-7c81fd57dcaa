# LLM-Enhanced Paper Analysis

This document describes the new LLM-enhanced features for extracting logic graphs, hypotheses, and evidence from scientific papers.

## Overview

The system now supports LLM-powered extraction using OpenRouter's API with the `mistralai/devstral-small` model. This provides more sophisticated analysis of scientific papers compared to rule-based extraction.

## Features

### 1. Logic Graph Inference
- **Hypotheses**: Identifies primary and secondary hypotheses with confidence scores
- **Evidence**: Extracts experimental, observational, computational, and statistical evidence
- **Conclusions**: Identifies conclusions and their supporting evidence
- **Relationships**: Maps logical connections between hypotheses, evidence, and conclusions

### 2. Enhanced Hypothesis Extraction
- Detects explicit and implicit hypothesis statements
- Classifies hypotheses as primary, secondary, or null
- Determines testability and confidence levels
- Identifies the paper section where each hypothesis appears

### 3. Lines of Evidence Analysis
- Categorizes evidence by type (experimental, observational, computational, statistical, literature)
- Assesses evidence strength (strong, moderate, weak)
- Identifies quantitative vs qualitative evidence
- Reports statistical significance when available

## Setup

### 1. Install Dependencies
```bash
pixi install
```

### 2. Configure OpenRouter API
1. Get an API key from [OpenRouter](https://openrouter.ai/keys)
2. Copy the environment template:
   ```bash
   cp .env.example .env
   ```
3. Edit `.env` and add your API key:
   ```
   OPENROUTER_API_KEY=your_api_key_here
   ```

### 3. Usage

#### Basic Usage with LLM Enhancement
```bash
python pdf_extractor.py --use-llm --verbose
```

#### Rule-based Only (no API key required)
```bash
python pdf_extractor.py --no-llm
```

#### Custom Input/Output
```bash
python pdf_extractor.py --input-dir my_papers --output enhanced_papers.json --use-llm
```

## Output Format

The enhanced extraction adds the following fields to each paper:

```json
{
  "title": "Paper title",
  "hypotheses": ["rule-based hypotheses"],
  "evidence": [{"text": "evidence", "type": "experimental"}],
  "conclusions": ["rule-based conclusions"],
  "logic_relationships": [],
  
  "llm_enhanced_hypotheses": [
    {
      "text": "hypothesis statement",
      "type": "primary|secondary|null",
      "section": "abstract|introduction|methods|results|discussion",
      "confidence": 0.85,
      "testable": true
    }
  ],
  
  "llm_enhanced_evidence": [
    {
      "text": "evidence statement",
      "type": "experimental|observational|computational|statistical|literature",
      "section": "methods|results|discussion",
      "strength": "strong|moderate|weak",
      "quantitative": true,
      "statistical_significance": "p<0.05|p<0.01|not_reported|not_applicable"
    }
  ],
  
  "llm_extraction_used": true
}
```

## Rate Limiting

The system implements rate limiting to respect API limits:
- Default: 30 requests per minute
- Configurable via environment variables
- Automatic fallback to rule-based extraction on API errors

## Error Handling

- **API Key Missing**: Falls back to rule-based extraction
- **API Errors**: Logs warnings and continues with rule-based methods
- **JSON Parsing Errors**: Attempts to extract valid JSON from LLM responses
- **Rate Limiting**: Automatically throttles requests

## Integration with Existing Pipeline

The LLM enhancement is fully compatible with the existing novelty graph pipeline:

```python
# Run full pipeline with LLM enhancement
python main.py --extract-first --threshold 0.3

# The extracted_papers.json will contain both rule-based and LLM-enhanced data
```

## Model Configuration

Current model: `mistralai/devstral-small`
- Optimized for code and technical text analysis
- Good balance of performance and cost
- Supports structured JSON output

To use a different model, update the `LLM_MODEL` environment variable:
```
LLM_MODEL=anthropic/claude-3-haiku
```

## Cost Considerations

- `mistralai/devstral-small`: ~$0.14 per 1M input tokens
- Average paper (~10k tokens): ~$0.0014 per paper
- 100 papers: ~$0.14 total cost

## Troubleshooting

### Common Issues

1. **"OpenRouter API key not found"**
   - Ensure `.env` file exists with `OPENROUTER_API_KEY`
   - Check that the API key is valid

2. **"LLM extraction failed"**
   - Check internet connection
   - Verify API key has sufficient credits
   - Review rate limiting settings

3. **"Could not parse JSON from LLM response"**
   - This is handled gracefully with fallback extraction
   - May indicate model output formatting issues

### Debug Mode
```bash
python pdf_extractor.py --verbose --use-llm
```

This provides detailed logging of:
- LLM API calls and responses
- JSON parsing attempts
- Fallback activations
- Rate limiting delays
