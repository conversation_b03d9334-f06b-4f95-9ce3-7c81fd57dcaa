#!/usr/bin/env python3
"""PDF text extraction system for scientific papers."""

import json
import os
from pathlib import Path
from typing import Dict, List, Optional
import fitz  # PyMuPDF
import re
from tqdm import tqdm
import click
import logging
from llm_client import OpenRouterClient, rate_limit


class PDFExtractor:
    """Extract text and metadata from scientific PDFs."""

    def __init__(self, use_llm: bool = True):
        self.section_patterns = {
            'abstract': re.compile(r'abstract', re.IGNORECASE),
            'introduction': re.compile(r'introduction|background', re.IGNORECASE),
            'methods': re.compile(r'method|material|procedure|approach', re.IGNORECASE),
            'results': re.compile(r'result|finding', re.IGNORECASE),
            'discussion': re.compile(r'discussion|conclusion', re.IGNORECASE),
            'references': re.compile(r'reference|bibliography', re.IGNORECASE)
        }

        # Initialize LLM client if requested
        self.use_llm = use_llm
        self.llm_client = None
        if use_llm:
            try:
                self.llm_client = OpenRouterClient()
                logging.info("LLM client initialized successfully")
            except Exception as e:
                logging.warning(f"Could not initialize LLM client: {e}")
                self.use_llm = False

    def extract_pdf_text(self, pdf_path: str) -> Dict[str, any]:
        """Extract text and metadata from a PDF file."""
        try:
            doc = fitz.open(pdf_path)

            # Extract metadata
            metadata = doc.metadata
            paper_data = {
                'file_path': pdf_path,
                'title': metadata.get('title', ''),
                'authors': metadata.get('author', '').split(';') if metadata.get('author') else [],
                'subject': metadata.get('subject', ''),
                'keywords': metadata.get('keywords', '').split(';') if metadata.get('keywords') else [],
                'pages': doc.page_count,
                'full_text': '',
                'sections': {}
            }

            # Extract text from all pages
            full_text = []
            for page_num in range(doc.page_count):
                page = doc[page_num]
                text = page.get_text()
                full_text.append(text)

            paper_data['full_text'] = '\n'.join(full_text)

            # Try to identify sections
            paper_data['sections'] = self._identify_sections(paper_data['full_text'])

            # Extract DOI if present
            doi_match = re.search(r'10\.\d{4,}/[-._;()/:\w]+', paper_data['full_text'])
            if doi_match:
                paper_data['doi'] = doi_match.group(0)

            # Extract title from first page if not in metadata
            if not paper_data['title'] and full_text:
                first_page_lines = full_text[0].split('\n')
                # Assume title is in the first few non-empty lines
                for line in first_page_lines[:10]:
                    if len(line.strip()) > 10 and not line.strip().isdigit():
                        paper_data['title'] = line.strip()
                        break

            doc.close()
            return paper_data

        except Exception as e:
            print(f"Error extracting {pdf_path}: {e}")
            return None

    def _identify_sections(self, text: str) -> Dict[str, str]:
        """Identify and extract sections from the full text."""
        sections = {}
        lines = text.split('\n')

        current_section = None
        section_text = []

        for line in lines:
            line_lower = line.lower().strip()

            # Check if this line matches any section pattern
            for section_name, pattern in self.section_patterns.items():
                if pattern.search(line_lower) and len(line_lower) < 50:
                    # Save previous section
                    if current_section:
                        sections[current_section] = '\n'.join(section_text)

                    # Start new section
                    current_section = section_name
                    section_text = []
                    break
            else:
                # Add line to current section
                if current_section:
                    section_text.append(line)

        # Save last section
        if current_section:
            sections[current_section] = '\n'.join(section_text)

        return sections

    def extract_hypotheses(self, text: str) -> List[str]:
        """Extract hypothesis statements from text."""
        hypotheses = []

        # Hypothesis indicators
        hypothesis_patterns = [
            r"we hypothesize[d]? that (.+?)(?:\.|;)",
            r"our hypothesis (?:is|was) that (.+?)(?:\.|;)",
            r"we propose[d]? that (.+?)(?:\.|;)",
            r"we predict[ed]? that (.+?)(?:\.|;)",
            r"we expect[ed]? that (.+?)(?:\.|;)",
            r"we suggest[ed]? that (.+?)(?:\.|;)",
            r"hypothesis: (.+?)(?:\.|;)"
        ]

        for pattern in hypothesis_patterns:
            matches = re.finditer(pattern, text, re.IGNORECASE | re.DOTALL)
            for match in matches:
                hypothesis = match.group(1).strip()
                if hypothesis and len(hypothesis) > 10:
                    hypotheses.append(hypothesis)

        return list(set(hypotheses))  # Remove duplicates

    def process_directory(self, directory: str, output_file: str = 'extracted_papers.json'):
        """Process all PDFs in a directory and save as JSON."""
        pdf_files = list(Path(directory).glob('*.pdf'))
        print(f"Found {len(pdf_files)} PDF files to process")

        extracted_papers = []

        for pdf_path in tqdm(pdf_files, desc="Extracting PDFs"):
            paper_data = self.extract_pdf_text(str(pdf_path))

            if paper_data:
                # Extract logic structure
                logic_structure = self.extract_logic_structure(paper_data)

                # Add to paper data
                paper_data['hypotheses'] = logic_structure['hypotheses']
                paper_data['evidence'] = logic_structure['evidence']
                paper_data['conclusions'] = logic_structure['conclusions']
                paper_data['logic_relationships'] = logic_structure['relationships']

                # Add LLM-enhanced data if available
                if self.use_llm and self.llm_client:
                    try:
                        enhanced_hypotheses = self.extract_enhanced_hypotheses(paper_data)
                        enhanced_evidence = self.extract_enhanced_evidence(paper_data)

                        paper_data['llm_enhanced_hypotheses'] = enhanced_hypotheses
                        paper_data['llm_enhanced_evidence'] = enhanced_evidence
                        paper_data['llm_extraction_used'] = True
                    except Exception as e:
                        logging.warning(f"LLM enhancement failed for {pdf_path}: {e}")
                        paper_data['llm_extraction_used'] = False
                else:
                    paper_data['llm_extraction_used'] = False

                extracted_papers.append(paper_data)

        # Save to JSON
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(extracted_papers, f, indent=2, ensure_ascii=False)

        print(f"Extracted {len(extracted_papers)} papers to {output_file}")
        return extracted_papers

    def extract_logic_structure(self, paper_data: Dict) -> Dict:
        """Extract logic structure including hypotheses, evidence, and conclusions."""
        logic_structure = {
            'hypotheses': [],
            'evidence': [],
            'conclusions': [],
            'relationships': []
        }

        # Try LLM-enhanced extraction first
        if self.use_llm and self.llm_client:
            try:
                llm_logic = self._extract_logic_structure_llm(paper_data)
                if llm_logic and any(llm_logic.values()):
                    logging.info("Using LLM-enhanced logic structure extraction")
                    return llm_logic
            except Exception as e:
                logging.warning(f"LLM extraction failed, falling back to rule-based: {e}")

        # Fallback to rule-based extraction
        logging.info("Using rule-based logic structure extraction")

        # Get relevant sections
        intro_text = paper_data.get('sections', {}).get('introduction', '')
        methods_text = paper_data.get('sections', {}).get('methods', '')
        results_text = paper_data.get('sections', {}).get('results', '')
        discussion_text = paper_data.get('sections', {}).get('discussion', '')

        # Extract hypotheses from introduction and abstract
        abstract_text = paper_data.get('sections', {}).get('abstract', '')
        hypotheses_text = abstract_text + ' ' + intro_text
        logic_structure['hypotheses'] = self.extract_hypotheses(hypotheses_text)

        # Extract evidence from methods and results
        evidence_text = methods_text + ' ' + results_text
        logic_structure['evidence'] = self.extract_evidence(evidence_text)

        # Extract conclusions from discussion/conclusion
        logic_structure['conclusions'] = self.extract_conclusions(discussion_text)

        # Try to link hypotheses with evidence and conclusions
        logic_structure['relationships'] = self.link_hypotheses_to_evidence(
            logic_structure['hypotheses'],
            logic_structure['evidence'],
            logic_structure['conclusions'],
            paper_data.get('full_text', '')
        )

        return logic_structure

    def extract_evidence(self, text: str) -> List[Dict]:
        """Extract evidence statements from methods and results sections."""
        evidence = []

        # Evidence indicators
        evidence_patterns = [
            r"((?:we|our) (?:found|observed|measured|detected|discovered) that .+?)(?:\.|;)",
            r"(results (?:show|indicate|demonstrate|reveal) that .+?)(?:\.|;)",
            r"(analysis (?:revealed|showed|indicated) that .+?)(?:\.|;)",
            r"(data (?:show|indicate|suggest) that .+?)(?:\.|;)",
            r"((?:significant|substantial) (?:increase|decrease|difference|effect) .+?)(?:\.|;)"
        ]

        for pattern in evidence_patterns:
            matches = re.finditer(pattern, text, re.IGNORECASE | re.DOTALL)
            for match in matches:
                evidence_text = match.group(1).strip()
                if evidence_text and len(evidence_text) > 15:
                    evidence.append({
                        'text': evidence_text,
                        'type': 'experimental' if 'measured' in evidence_text.lower() or 'observed' in evidence_text.lower() else 'analytical'
                    })

        # Look for statistical evidence
        stat_patterns = [
            r"(p\s*(?:<|=)\s*0\.0\d+)",
            r"(statistically significant (?:difference|increase|decrease|correlation))",
            r"(correlation coefficient (?:of|was|=) .+?)(?:\.|;)",
            r"(confidence interval .+?)(?:\.|;)"
        ]

        for pattern in stat_patterns:
            matches = re.finditer(pattern, text, re.IGNORECASE | re.DOTALL)
            for match in matches:
                context_start = max(0, match.start() - 100)
                context_end = min(len(text), match.end() + 100)
                context = text[context_start:context_end]

                evidence.append({
                    'text': context,
                    'type': 'statistical',
                    'stat_value': match.group(1)
                })

        return evidence

    def extract_conclusions(self, text: str) -> List[str]:
        """Extract conclusion statements from discussion section."""
        conclusions = []

        # Conclusion indicators
        conclusion_patterns = [
            r"((?:we|our) (?:conclude|concludes|concluded) that .+?)(?:\.|;)",
            r"(in conclusion,? .+?)(?:\.|;)",
            r"((?:these|this|our) (?:results|study|work|analysis) (?:demonstrate|demonstrates|shows|indicates|suggests) that .+?)(?:\.|;)",
            r"((?:overall|taken together),? (?:these|our) (?:findings|results|data) (?:suggest|indicate|show|demonstrate) that .+?)(?:\.|;)",
            r"((?:this|these) (?:findings|results) (?:highlight|support|confirm|validate) .+?)(?:\.|;)"
        ]

        for pattern in conclusion_patterns:
            matches = re.finditer(pattern, text, re.IGNORECASE | re.DOTALL)
            for match in matches:
                conclusion = match.group(1).strip()
                if conclusion and len(conclusion) > 15:
                    conclusions.append(conclusion)

        return list(set(conclusions))  # Remove duplicates

    def link_hypotheses_to_evidence(self, hypotheses: List[str], evidence: List[Dict],
                                  conclusions: List[str], full_text: str) -> List[Dict]:
        """Create relationships between hypotheses, evidence, and conclusions."""
        relationships = []

        # Simple approach: look for keyword overlap
        for hyp_idx, hypothesis in enumerate(hypotheses):
            hyp_keywords = self._extract_keywords(hypothesis)

            related_evidence = []
            for ev_idx, evidence_item in enumerate(evidence):
                ev_keywords = self._extract_keywords(evidence_item['text'])
                overlap = len(set(hyp_keywords) & set(ev_keywords))

                if overlap >= 2:  # At least 2 keywords in common
                    related_evidence.append(ev_idx)

            related_conclusions = []
            for conc_idx, conclusion in enumerate(conclusions):
                conc_keywords = self._extract_keywords(conclusion)
                overlap = len(set(hyp_keywords) & set(conc_keywords))

                if overlap >= 2:  # At least 2 keywords in common
                    related_conclusions.append(conc_idx)

            if related_evidence or related_conclusions:
                relationships.append({
                    'hypothesis_idx': hyp_idx,
                    'evidence_idx': related_evidence,
                    'conclusion_idx': related_conclusions
                })

        return relationships

    def _extract_keywords(self, text: str) -> List[str]:
        """Extract important keywords from text."""
        # Remove common words and keep only significant terms
        words = re.findall(r'\b[a-zA-Z]{4,}\b', text.lower())
        stopwords = {'that', 'this', 'with', 'from', 'were', 'have', 'they', 'their',
                    'what', 'when', 'where', 'which', 'while', 'would', 'could', 'should',
                    'about', 'these', 'those', 'there', 'been', 'being', 'other', 'another'}

        return [w for w in words if w not in stopwords]

    @rate_limit(calls_per_minute=30)
    def _extract_logic_structure_llm(self, paper_data: Dict) -> Dict:
        """Extract logic structure using LLM."""
        if not self.llm_client:
            return {}

        # Prepare text for LLM analysis
        title = paper_data.get('title', '')
        full_text = paper_data.get('full_text', '')

        # Use sections if available, otherwise use full text
        sections = paper_data.get('sections', {})
        if sections:
            analysis_text = ""
            for section_name in ['abstract', 'introduction', 'methods', 'results', 'discussion']:
                if section_name in sections:
                    analysis_text += f"\n\n{section_name.upper()}:\n{sections[section_name]}"
        else:
            analysis_text = full_text

        # Get comprehensive logic graph from LLM
        logic_graph = self.llm_client.extract_logic_graph(analysis_text, title)

        # Convert LLM format to our internal format
        converted_logic = {
            'hypotheses': [],
            'evidence': [],
            'conclusions': [],
            'relationships': []
        }

        # Convert hypotheses
        for hyp in logic_graph.get('hypotheses', []):
            if isinstance(hyp, dict):
                converted_logic['hypotheses'].append(hyp.get('text', ''))
            else:
                converted_logic['hypotheses'].append(str(hyp))

        # Convert evidence
        for ev in logic_graph.get('evidence', []):
            if isinstance(ev, dict):
                converted_logic['evidence'].append(ev)
            else:
                converted_logic['evidence'].append({
                    'text': str(ev),
                    'type': 'unknown'
                })

        # Convert conclusions
        for conc in logic_graph.get('conclusions', []):
            if isinstance(conc, dict):
                converted_logic['conclusions'].append(conc.get('text', ''))
            else:
                converted_logic['conclusions'].append(str(conc))

        # Store relationships as-is
        converted_logic['relationships'] = logic_graph.get('logic_relationships', [])

        return converted_logic

    @rate_limit(calls_per_minute=30)
    def extract_enhanced_hypotheses(self, paper_data: Dict) -> List[Dict]:
        """Extract enhanced hypotheses using LLM."""
        if not self.llm_client:
            return []

        title = paper_data.get('title', '')
        full_text = paper_data.get('full_text', '')

        return self.llm_client.extract_key_hypotheses(full_text, title)

    @rate_limit(calls_per_minute=30)
    def extract_enhanced_evidence(self, paper_data: Dict) -> List[Dict]:
        """Extract enhanced lines of evidence using LLM."""
        if not self.llm_client:
            return []

        title = paper_data.get('title', '')
        full_text = paper_data.get('full_text', '')

        return self.llm_client.extract_lines_of_evidence(full_text, title)


@click.command()
@click.option('--input-dir', '-i', default='nm_test', help='Directory containing PDFs')
@click.option('--output', '-o', default='extracted_papers.json', help='Output JSON file')
@click.option('--use-llm/--no-llm', default=True, help='Use LLM for enhanced extraction')
@click.option('--verbose', '-v', is_flag=True, help='Enable verbose logging')
def main(input_dir, output, use_llm, verbose):
    """Extract text from PDFs and save as JSON."""
    # Configure logging
    log_level = logging.DEBUG if verbose else logging.INFO
    logging.basicConfig(level=log_level, format='%(asctime)s - %(levelname)s - %(message)s')

    if use_llm:
        print("Using LLM-enhanced extraction (requires OPENROUTER_API_KEY)")
    else:
        print("Using rule-based extraction only")

    extractor = PDFExtractor(use_llm=use_llm)
    extractor.process_directory(input_dir, output)


if __name__ == '__main__':
    main()
