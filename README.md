# Novelty Impact Graphs

A system for analyzing scientific papers and building novelty graphs to visualize relationships and identify novel research.

## Quick Start

1. Install dependencies:
```bash
pixi install
```

2. Run the pipeline on PDFs:
```bash
pixi run python main.py --extract-first --threshold 0.3
```

## Generated Files

- `extracted_papers.json`: Extracted text and metadata from all PDFs
- `novelty_graph.html`: Interactive 2D network visualization
- `novelty_graph_3d.html`: Interactive 3D network visualization

## Results

The system successfully processed 100 Nature Microbiology papers:
- Created a graph with 100 nodes and 2,199 edges
- Graph density: 0.444 (moderately connected)
- Average clustering: 0.661 (papers form well-defined clusters)
- All papers are connected in a single component

Most novel papers identified:
1. 10.1038_nmicrobiol.2016.124 - Metabolic heterogeneity of Euglena gracilis
2. 10.1038_nmicrobiol.2016.154 - Essential H. influenzae UDP-diacylglucosamine structure
3. 10.1038_nmicrobiol.2016.144 - Cell-to-cell spread of microsporidia in C. elegans

## Usage

Extract PDFs only:
```bash
pixi run python pdf_extractor.py -i nm_test -o extracted_papers.json
```

Build graph from existing JSON:
```bash
pixi run python main.py -i extracted_papers.json -t 0.3
```

## Parameters

- `--threshold`: Similarity threshold for creating edges (0-1, default: 0.5)
- `--extract-first`: Extract PDFs before building graph
- `--input`: Input JSON file with extracted papers