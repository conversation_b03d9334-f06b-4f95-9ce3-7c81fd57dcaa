#!/usr/bin/env python3
"""Comprehensive diagnostic analysis for extraction method failures."""

import json
import logging
from pathlib import Path
from typing import Dict, List, Any
import pandas as pd
from pdf_extractor import PDFExtractor
from llm_client import OpenRouterClient
import os

# Configure detailed logging
logging.basicConfig(level=logging.DEBUG, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class ExtractionDiagnostics:
    """Comprehensive diagnostics for extraction method failures."""
    
    def __init__(self):
        self.results = {
            'api_status': {},
            'extraction_success': {},
            'content_analysis': {},
            'error_logs': []
        }
    
    def run_full_diagnostics(self, papers_file: str = "extracted_papers.json"):
        """Run comprehensive diagnostics on extraction methods."""
        
        print("🔍 EXTRACTION DIAGNOSTICS - INVESTIGATING POOR ALIGNMENT")
        print("=" * 70)
        
        # 1. Check API and environment
        self.check_api_status()
        
        # 2. Analyze existing data
        if Path(papers_file).exists():
            self.analyze_existing_extractions(papers_file)
        
        # 3. Test extraction methods on sample
        self.test_extraction_methods()
        
        # 4. Generate diagnostic report
        self.generate_diagnostic_report()
    
    def check_api_status(self):
        """Check LLM API status and configuration."""
        
        print("\n🌐 API STATUS CHECK")
        print("-" * 40)
        
        # Check environment variables
        api_key = os.getenv('OPENROUTER_API_KEY')
        if api_key:
            print(f"✅ OPENROUTER_API_KEY found (length: {len(api_key)})")
            self.results['api_status']['api_key_present'] = True
        else:
            print("❌ OPENROUTER_API_KEY not found")
            self.results['api_status']['api_key_present'] = False
            return
        
        # Test API connection
        try:
            client = OpenRouterClient()
            print("✅ LLM client initialized")
            
            # Test minimal API call
            test_messages = [
                {"role": "system", "content": "You are a helpful assistant."},
                {"role": "user", "content": "Say 'API working' in JSON format: {\"status\": \"working\"}"}
            ]
            
            response = client._make_sync_request(test_messages, max_tokens=50, temperature=0.1)
            
            if response and "choices" in response:
                content = response["choices"][0]["message"]["content"]
                print(f"✅ API call successful: {content[:100]}...")
                self.results['api_status']['api_working'] = True
                self.results['api_status']['sample_response'] = content
            else:
                print("❌ API call failed - unexpected response format")
                self.results['api_status']['api_working'] = False
                
        except Exception as e:
            print(f"❌ API test failed: {e}")
            self.results['api_status']['api_working'] = False
            self.results['error_logs'].append(f"API test error: {e}")
    
    def analyze_existing_extractions(self, papers_file: str):
        """Analyze existing extraction data for patterns."""
        
        print(f"\n📊 ANALYZING EXISTING DATA: {papers_file}")
        print("-" * 50)
        
        with open(papers_file, 'r') as f:
            papers = json.load(f)
        
        print(f"Total papers: {len(papers)}")
        
        # Analyze extraction success rates
        has_rule_hyp = sum(1 for p in papers if p.get('hypotheses'))
        has_rule_ev = sum(1 for p in papers if p.get('evidence'))
        has_llm_hyp = sum(1 for p in papers if p.get('llm_enhanced_hypotheses'))
        has_llm_ev = sum(1 for p in papers if p.get('llm_enhanced_evidence'))
        has_llm_flag = sum(1 for p in papers if p.get('llm_extraction_used'))
        
        print(f"Papers with rule-based hypotheses: {has_rule_hyp}/{len(papers)} ({has_rule_hyp/len(papers)*100:.1f}%)")
        print(f"Papers with rule-based evidence: {has_rule_ev}/{len(papers)} ({has_rule_ev/len(papers)*100:.1f}%)")
        print(f"Papers with LLM hypotheses: {has_llm_hyp}/{len(papers)} ({has_llm_hyp/len(papers)*100:.1f}%)")
        print(f"Papers with LLM evidence: {has_llm_ev}/{len(papers)} ({has_llm_ev/len(papers)*100:.1f}%)")
        print(f"Papers marked as LLM extracted: {has_llm_flag}/{len(papers)} ({has_llm_flag/len(papers)*100:.1f}%)")
        
        self.results['extraction_success'] = {
            'total_papers': len(papers),
            'rule_hypotheses_success_rate': has_rule_hyp / len(papers),
            'rule_evidence_success_rate': has_rule_ev / len(papers),
            'llm_hypotheses_success_rate': has_llm_hyp / len(papers),
            'llm_evidence_success_rate': has_llm_ev / len(papers),
            'llm_flag_rate': has_llm_flag / len(papers)
        }
        
        # Analyze content lengths and patterns
        self.analyze_content_patterns(papers)
        
        # Show sample extractions
        self.show_sample_extractions(papers[:3])
    
    def analyze_content_patterns(self, papers: List[Dict]):
        """Analyze content patterns in extractions."""
        
        print(f"\n📈 CONTENT PATTERN ANALYSIS")
        print("-" * 40)
        
        rule_hyp_counts = []
        llm_hyp_counts = []
        rule_ev_counts = []
        llm_ev_counts = []
        
        rule_hyp_lengths = []
        llm_hyp_lengths = []
        
        for paper in papers:
            # Count analysis
            rule_hyp = paper.get('hypotheses', [])
            llm_hyp = paper.get('llm_enhanced_hypotheses', [])
            rule_ev = paper.get('evidence', [])
            llm_ev = paper.get('llm_enhanced_evidence', [])
            
            rule_hyp_counts.append(len(rule_hyp))
            llm_hyp_counts.append(len(llm_hyp))
            rule_ev_counts.append(len(rule_ev))
            llm_ev_counts.append(len(llm_ev))
            
            # Length analysis
            rule_hyp_text = ' '.join([str(h) for h in rule_hyp])
            llm_hyp_text = ' '.join([
                h.get('text', '') if isinstance(h, dict) else str(h) 
                for h in llm_hyp
            ])
            
            rule_hyp_lengths.append(len(rule_hyp_text))
            llm_hyp_lengths.append(len(llm_hyp_text))
        
        # Statistics
        import numpy as np
        
        print(f"Rule-based hypothesis counts: mean={np.mean(rule_hyp_counts):.1f}, std={np.std(rule_hyp_counts):.1f}, max={max(rule_hyp_counts)}")
        print(f"LLM hypothesis counts: mean={np.mean(llm_hyp_counts):.1f}, std={np.std(llm_hyp_counts):.1f}, max={max(llm_hyp_counts)}")
        print(f"Rule-based evidence counts: mean={np.mean(rule_ev_counts):.1f}, std={np.std(rule_ev_counts):.1f}, max={max(rule_ev_counts)}")
        print(f"LLM evidence counts: mean={np.mean(llm_ev_counts):.1f}, std={np.std(llm_ev_counts):.1f}, max={max(llm_ev_counts)}")
        
        print(f"Rule-based hypothesis text lengths: mean={np.mean(rule_hyp_lengths):.0f}, max={max(rule_hyp_lengths)}")
        print(f"LLM hypothesis text lengths: mean={np.mean(llm_hyp_lengths):.0f}, max={max(llm_hyp_lengths)}")
        
        # Check for empty extractions
        empty_rule_hyp = sum(1 for c in rule_hyp_counts if c == 0)
        empty_llm_hyp = sum(1 for c in llm_hyp_counts if c == 0)
        empty_rule_ev = sum(1 for c in rule_ev_counts if c == 0)
        empty_llm_ev = sum(1 for c in llm_ev_counts if c == 0)
        
        print(f"\nEmpty extractions:")
        print(f"Rule-based hypotheses: {empty_rule_hyp}/{len(papers)} papers")
        print(f"LLM hypotheses: {empty_llm_hyp}/{len(papers)} papers")
        print(f"Rule-based evidence: {empty_rule_ev}/{len(papers)} papers")
        print(f"LLM evidence: {empty_llm_ev}/{len(papers)} papers")
        
        self.results['content_analysis'] = {
            'rule_hyp_stats': {'mean': np.mean(rule_hyp_counts), 'std': np.std(rule_hyp_counts), 'empty': empty_rule_hyp},
            'llm_hyp_stats': {'mean': np.mean(llm_hyp_counts), 'std': np.std(llm_hyp_counts), 'empty': empty_llm_hyp},
            'rule_ev_stats': {'mean': np.mean(rule_ev_counts), 'std': np.std(rule_ev_counts), 'empty': empty_rule_ev},
            'llm_ev_stats': {'mean': np.mean(llm_ev_counts), 'std': np.std(llm_ev_counts), 'empty': empty_llm_ev}
        }
    
    def show_sample_extractions(self, papers: List[Dict]):
        """Show detailed sample extractions for comparison."""
        
        print(f"\n🔍 SAMPLE EXTRACTION COMPARISON")
        print("-" * 60)
        
        for i, paper in enumerate(papers):
            print(f"\n📄 PAPER {i+1}: {paper.get('title', 'No title')[:50]}...")
            print("=" * 60)
            
            # Rule-based extraction
            rule_hyp = paper.get('hypotheses', [])
            rule_ev = paper.get('evidence', [])
            
            print(f"📝 RULE-BASED EXTRACTION:")
            print(f"   Hypotheses ({len(rule_hyp)}):")
            for j, hyp in enumerate(rule_hyp[:3]):  # Show first 3
                print(f"     {j+1}. {str(hyp)[:100]}...")
            
            print(f"   Evidence ({len(rule_ev)}):")
            for j, ev in enumerate(rule_ev[:3]):  # Show first 3
                if isinstance(ev, dict):
                    print(f"     {j+1}. {ev.get('text', str(ev))[:100]}...")
                else:
                    print(f"     {j+1}. {str(ev)[:100]}...")
            
            # LLM extraction
            llm_hyp = paper.get('llm_enhanced_hypotheses', [])
            llm_ev = paper.get('llm_enhanced_evidence', [])
            llm_used = paper.get('llm_extraction_used', False)
            
            print(f"\n🤖 LLM EXTRACTION (Used: {llm_used}):")
            print(f"   Hypotheses ({len(llm_hyp)}):")
            for j, hyp in enumerate(llm_hyp[:3]):  # Show first 3
                if isinstance(hyp, dict):
                    print(f"     {j+1}. {hyp.get('text', '')[:100]}... (type: {hyp.get('type', 'unknown')}, conf: {hyp.get('confidence', 'N/A')})")
                else:
                    print(f"     {j+1}. {str(hyp)[:100]}...")
            
            print(f"   Evidence ({len(llm_ev)}):")
            for j, ev in enumerate(llm_ev[:3]):  # Show first 3
                if isinstance(ev, dict):
                    print(f"     {j+1}. {ev.get('text', '')[:100]}... (type: {ev.get('type', 'unknown')}, strength: {ev.get('strength', 'unknown')})")
                else:
                    print(f"     {j+1}. {str(ev)[:100]}...")
            
            print("-" * 60)
    
    def test_extraction_methods(self):
        """Test extraction methods on a sample paper."""
        
        print(f"\n🧪 TESTING EXTRACTION METHODS ON SAMPLE")
        print("-" * 50)
        
        # Check for sample PDFs
        pdf_files = list(Path("nm_test").glob("*.pdf"))
        if not pdf_files:
            print("❌ No PDF files found for testing")
            return
        
        sample_pdf = pdf_files[0]
        print(f"Testing with: {sample_pdf.name}")
        
        try:
            # Test rule-based extraction
            print("\n📝 Testing rule-based extraction...")
            rule_extractor = PDFExtractor(use_llm=False)
            rule_data = rule_extractor.extract_pdf_text(str(sample_pdf))
            
            if rule_data:
                rule_logic = rule_extractor.extract_logic_structure(rule_data)
                print(f"✅ Rule-based success: {len(rule_logic['hypotheses'])}H, {len(rule_logic['evidence'])}E")
            else:
                print("❌ Rule-based extraction failed")
            
            # Test LLM extraction
            print("\n🤖 Testing LLM extraction...")
            llm_extractor = PDFExtractor(use_llm=True)
            
            if llm_extractor.use_llm and llm_extractor.llm_client:
                llm_data = llm_extractor.extract_pdf_text(str(sample_pdf))
                
                if llm_data:
                    # Test enhanced extraction
                    enhanced_hyp = llm_extractor.extract_enhanced_hypotheses(llm_data)
                    enhanced_ev = llm_extractor.extract_enhanced_evidence(llm_data)
                    
                    print(f"✅ LLM success: {len(enhanced_hyp)}H, {len(enhanced_ev)}E")
                    
                    # Show sample LLM output
                    if enhanced_hyp:
                        print(f"Sample LLM hypothesis: {enhanced_hyp[0]}")
                    if enhanced_ev:
                        print(f"Sample LLM evidence: {enhanced_ev[0]}")
                else:
                    print("❌ LLM text extraction failed")
            else:
                print("❌ LLM client not available")
                
        except Exception as e:
            print(f"❌ Testing failed: {e}")
            self.results['error_logs'].append(f"Testing error: {e}")
    
    def generate_diagnostic_report(self):
        """Generate comprehensive diagnostic report."""
        
        print(f"\n📋 DIAGNOSTIC REPORT")
        print("=" * 60)
        
        # API Status
        api_working = self.results['api_status'].get('api_working', False)
        api_key_present = self.results['api_status'].get('api_key_present', False)
        
        print(f"API Status: {'✅ Working' if api_working else '❌ Failed'}")
        print(f"API Key: {'✅ Present' if api_key_present else '❌ Missing'}")
        
        # Extraction Success
        if 'extraction_success' in self.results:
            success = self.results['extraction_success']
            print(f"\nExtraction Success Rates:")
            print(f"  Rule-based hypotheses: {success['rule_hypotheses_success_rate']*100:.1f}%")
            print(f"  LLM hypotheses: {success['llm_hypotheses_success_rate']*100:.1f}%")
            print(f"  Rule-based evidence: {success['rule_evidence_success_rate']*100:.1f}%")
            print(f"  LLM evidence: {success['llm_evidence_success_rate']*100:.1f}%")
        
        # Content Analysis
        if 'content_analysis' in self.results:
            content = self.results['content_analysis']
            print(f"\nContent Analysis:")
            print(f"  Rule-based hyp empty rate: {content['rule_hyp_stats']['empty']}/{self.results['extraction_success']['total_papers']}")
            print(f"  LLM hyp empty rate: {content['llm_hyp_stats']['empty']}/{self.results['extraction_success']['total_papers']}")
        
        # Recommendations
        print(f"\n🎯 RECOMMENDATIONS:")
        print("-" * 30)
        
        if not api_key_present:
            print("1. ❌ Set OPENROUTER_API_KEY environment variable")
        elif not api_working:
            print("1. ❌ Fix API connection issues")
        else:
            print("1. ✅ API is working")
        
        if 'content_analysis' in self.results:
            content = self.results['content_analysis']
            if content['llm_hyp_stats']['empty'] > content['rule_hyp_stats']['empty']:
                print("2. ❌ LLM extraction is failing more than rule-based")
                print("   - Check LLM prompts and parsing logic")
                print("   - Verify JSON parsing is working correctly")
            
            if content['llm_hyp_stats']['mean'] == 0:
                print("3. ❌ LLM is returning no hypotheses")
                print("   - Check raw LLM responses with test_raw_llm_output.py")
                print("   - Verify prompt engineering")
        
        # Error logs
        if self.results['error_logs']:
            print(f"\n❌ ERRORS ENCOUNTERED:")
            for error in self.results['error_logs']:
                print(f"   - {error}")


def main():
    """Run comprehensive diagnostics."""
    
    diagnostics = ExtractionDiagnostics()
    diagnostics.run_full_diagnostics()
    
    print(f"\n🔧 NEXT STEPS:")
    print("1. Fix any API issues identified above")
    print("2. Run: python test_raw_llm_output.py (to see raw LLM responses)")
    print("3. Check LLM prompts and parsing logic")
    print("4. Re-run extraction after fixes")


if __name__ == '__main__':
    main()
