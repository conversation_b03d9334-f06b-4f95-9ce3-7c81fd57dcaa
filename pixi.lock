version: 6
environments:
  default:
    channels:
    - url: https://conda.anaconda.org/conda-forge/
    - url: https://conda.anaconda.org/pytorch/
    - url: https://conda.anaconda.org/pyg/
    indexes:
    - https://pypi.org/simple
    packages:
      linux-64:
      - conda: https://conda.anaconda.org/conda-forge/linux-64/_openmp_mutex-4.5-3_kmp_llvm.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/adwaita-icon-theme-48.0-unix_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/aiohappyeyeballs-2.6.1-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/aiohttp-3.11.18-py310h89163eb_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/aiosignal-1.3.2-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/alsa-lib-1.2.14-hb9d3cd8_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/annotated-types-0.7.0-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/anyio-4.9.0-pyh29332c3_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/aom-3.9.1-hac33072_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/argon2-cffi-23.1.0-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/argon2-cffi-bindings-21.2.0-py310ha75aee5_5.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/arrow-1.3.0-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/asttokens-3.0.0-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/async-lru-2.0.5-pyh29332c3_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/async-timeout-5.0.1-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/at-spi2-atk-2.38.0-h0630a04_3.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/linux-64/at-spi2-core-2.40.3-h0630a04_0.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/linux-64/atk-1.0-2.38.0-h04ea711_2.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/attrs-25.3.0-pyh71513ae_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/aws-c-auth-0.9.0-h59ae206_7.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/aws-c-cal-0.9.0-h5e3027f_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/aws-c-common-0.12.3-hb9d3cd8_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/aws-c-compression-0.3.1-hafb2847_5.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/aws-c-event-stream-0.5.4-h2dcaabb_9.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/aws-c-http-0.10.1-hb50fa74_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/aws-c-io-0.19.0-h7962f60_2.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/aws-c-mqtt-0.13.0-h35de22e_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/aws-c-s3-0.7.17-h50d7d24_2.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/aws-c-sdkutils-0.2.3-hafb2847_5.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/aws-checksums-0.2.7-hafb2847_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/aws-crt-cpp-0.32.5-h2811929_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/aws-sdk-cpp-1.11.510-hffe9a0f_8.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/azure-core-cpp-1.14.0-h5cfcd09_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/azure-identity-cpp-1.10.0-h113e628_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/azure-storage-blobs-cpp-12.13.0-h3cf044e_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/azure-storage-common-cpp-12.8.0-h736e048_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/azure-storage-files-datalake-cpp-12.12.0-ha633028_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/babel-2.17.0-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/beautifulsoup4-4.13.4-pyha770c72_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/bleach-6.2.0-pyh29332c3_4.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/bleach-with-css-6.2.0-h82add2a_4.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/brotli-1.1.0-hb9d3cd8_2.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/brotli-bin-1.1.0-hb9d3cd8_2.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/brotli-python-1.1.0-py310hf71b8c6_2.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/bzip2-1.0.8-h4bc722e_7.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/c-ares-1.34.5-hb9d3cd8_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/ca-certificates-2025.4.26-hbd8a1cb_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/cached-property-1.5.2-hd8ed1ab_1.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/noarch/cached_property-1.5.2-pyha770c72_1.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/linux-64/cairo-1.18.4-h3394656_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/catalogue-2.0.10-py310hff52083_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/certifi-2025.4.26-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/cffi-1.17.1-py310h8deb56e_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/charset-normalizer-3.4.2-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/click-8.2.1-pyh707e725_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/cloudpathlib-0.21.1-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/cloudpickle-3.1.1-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/colorama-0.4.6-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/comm-0.2.2-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/confection-0.1.5-pyhecae5ae_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/contourpy-1.3.2-py310h3788b33_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/cpython-3.10.17-py310hd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/cryptography-45.0.2-py310h6c63255_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/curl-8.13.0-h332b0f4_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/cycler-0.12.1-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/cymem-2.0.11-py310hf71b8c6_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/cyrus-sasl-2.1.27-h54b06d7_7.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/cython-blis-1.0.1-py310hf462985_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/datasets-2.14.4-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/dav1d-1.2.1-hd590300_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/dbus-1.13.6-h5008d03_3.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/linux-64/debugpy-1.8.14-py310hf71b8c6_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/decorator-5.2.1-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/defusedxml-0.7.1-pyhd8ed1ab_0.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/noarch/dill-0.3.7-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/double-conversion-3.3.1-h5888daf_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/epoxy-1.5.10-h166bdaf_1.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/noarch/exceptiongroup-1.3.0-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/executing-2.2.0-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/expat-2.7.0-h5888daf_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/fftw-3.3.10-nompi_hf1063bd_110.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/filelock-3.18.0-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/font-ttf-dejavu-sans-mono-2.37-hab24e00_0.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/noarch/font-ttf-inconsolata-3.000-h77eed37_0.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/noarch/font-ttf-source-code-pro-2.038-h77eed37_0.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/noarch/font-ttf-ubuntu-0.83-h77eed37_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/fontconfig-2.15.0-h7e30c49_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/fonts-conda-ecosystem-1-0.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/noarch/fonts-conda-forge-1-0.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/linux-64/fonttools-4.58.0-py310h89163eb_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/fqdn-1.5.1-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/freeglut-3.2.2-ha6d2627_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/freetype-2.13.3-ha770c72_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/fribidi-1.0.10-h36c2ea0_0.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/linux-64/frozenlist-1.6.0-py310h0a1d07c_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/fsspec-2025.5.0-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/gdk-pixbuf-2.42.12-hb9ae30d_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/gensim-4.3.3-py310h27b3328_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/gflags-2.2.2-h5888daf_1005.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/ghostscript-10.04.0-h5888daf_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/giflib-5.2.2-hd590300_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/glib-tools-2.84.1-h4833e2c_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/glog-0.7.1-hbabe93e_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/gmp-6.3.0-hac33072_2.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/gmpy2-2.2.1-py310he8512ff_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/graphite2-1.3.13-h59595ed_1003.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/graphviz-12.2.1-h5ae0cbf_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/gtk3-3.24.43-h021d004_4.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/gts-0.7.6-h977cf35_4.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/h11-0.16.0-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/h2-4.2.0-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/harfbuzz-10.4.0-h76408a6_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/hicolor-icon-theme-0.17-ha770c72_2.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/noarch/hpack-4.1.0-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/httpcore-1.0.9-pyh29332c3_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/httpx-0.28.1-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/huggingface_hub-0.31.4-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/hyperframe-6.1.0-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/icu-75.1-he02047a_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/idna-3.10-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/imagemagick-7.1.1_47-imagemagick_hf2058d9_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/importlib-metadata-8.7.0-pyhe01879c_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/importlib_resources-6.5.2-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/iniconfig-2.0.0-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/ipykernel-6.29.5-pyh3099207_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/ipython-8.36.0-pyh907856f_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/ipywidgets-8.1.7-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/isoduration-20.11.0-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/jbig-2.1-h7f98852_2003.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/linux-64/jbig2dec-0.18-h267a509_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/jedi-0.19.2-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/jinja2-3.1.6-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/joblib-1.5.1-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/json5-0.12.0-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/jsonpickle-4.1.0-pyhe01879c_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/jsonpointer-3.0.0-py310hff52083_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/jsonschema-4.23.0-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/jsonschema-specifications-2025.4.1-pyh29332c3_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/jsonschema-with-format-nongpl-4.23.0-hd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/jupyter-1.1.1-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/jupyter-lsp-2.2.5-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/jupyter_client-8.6.3-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/jupyter_console-6.6.3-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/jupyter_core-5.7.2-pyh31011fe_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/jupyter_events-0.12.0-pyh29332c3_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/jupyter_server-2.16.0-pyhe01879c_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/jupyter_server_terminals-0.5.3-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/jupyterlab-4.4.2-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/jupyterlab_pygments-0.3.0-pyhd8ed1ab_2.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/jupyterlab_server-2.27.3-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/jupyterlab_widgets-3.0.15-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/keyutils-1.6.1-h166bdaf_0.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/linux-64/kiwisolver-1.4.7-py310h3788b33_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/krb5-1.21.3-h659f571_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/langcodes-3.4.1-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/language-data-1.3.0-pyhff2d567_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/lcms2-2.17-h717163a_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/ld_impl_linux-64-2.43-h712a8e2_4.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/leptonica-1.83.1-hb768ceb_6.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/lerc-4.0.0-h0aef613_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libabseil-20250127.1-cxx17_hbbce691_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libarchive-3.7.7-h75ea233_4.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libarrow-20.0.0-hebdba27_3_cpu.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libarrow-acero-20.0.0-hcb10f89_3_cpu.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libarrow-dataset-20.0.0-hcb10f89_3_cpu.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libarrow-substrait-20.0.0-h1bed206_3_cpu.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libavif16-1.3.0-h766b0b6_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libblas-3.9.0-31_hfdb39a5_mkl.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libbrotlicommon-1.1.0-hb9d3cd8_2.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libbrotlidec-1.1.0-hb9d3cd8_2.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libbrotlienc-1.1.0-hb9d3cd8_2.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libcblas-3.9.0-31_h372d94f_mkl.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libclang-cpp20.1-20.1.5-default_h1df26ce_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libclang13-20.1.5-default_he06ed0a_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libcrc32c-1.1.2-h9c3ff4c_0.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libcups-2.3.3-h4637d8d_4.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libcurl-8.13.0-h332b0f4_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libde265-1.0.15-h00ab1b0_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libdeflate-1.24-h86f0d12_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libdrm-2.4.124-hb9d3cd8_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libedit-3.1.20250104-pl5321h7949ede_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libegl-1.7.0-ha4b6fd6_2.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libev-4.33-hd590300_2.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libevent-2.1.12-hf998b51_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libexpat-2.7.0-h5888daf_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libffi-3.4.6-h2dba641_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libfreetype-2.13.3-ha770c72_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libfreetype6-2.13.3-h48d6fc4_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libgcc-15.1.0-h767d61c_2.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libgcc-ng-15.1.0-h69a702a_2.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libgd-2.3.3-h6f5c62b_11.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libgfortran-15.1.0-h69a702a_2.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libgfortran-ng-15.1.0-h69a702a_2.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libgfortran5-15.1.0-hcea5267_2.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libgl-1.7.0-ha4b6fd6_2.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libglib-2.84.1-h2ff4ddf_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libglvnd-1.7.0-ha4b6fd6_2.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libglx-1.7.0-ha4b6fd6_2.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libgoogle-cloud-2.36.0-hc4361e1_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libgoogle-cloud-storage-2.36.0-h0121fbd_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libgrpc-1.71.0-h8e591d7_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libgumbo-1.0.0-h3f2d84a_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libheif-1.19.7-gpl_hc18d805_100.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libhwloc-2.11.2-default_h0d58e46_1001.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libiconv-1.18-h4ce23a2_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libjpeg-turbo-3.1.0-hb9d3cd8_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/liblapack-3.9.0-31_hc41d3b0_mkl.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libllvm20-20.1.5-he9d0ab4_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/liblzma-5.8.1-hb9d3cd8_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/liblzma-devel-5.8.1-hb9d3cd8_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libnghttp2-1.64.0-h161d5f1_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libnsl-2.0.1-hd590300_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libntlm-1.8-hb9d3cd8_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libopengl-1.7.0-ha4b6fd6_2.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libopentelemetry-cpp-1.20.0-hd1b1c89_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libopentelemetry-cpp-headers-1.20.0-ha770c72_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libparquet-20.0.0-h081d1f1_3_cpu.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libpciaccess-0.18-hd590300_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libpng-1.6.47-h943b412_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libpq-17.5-h27ae623_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libprotobuf-5.29.3-h501fc15_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libre2-11-2024.07.02-hba17884_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/librsvg-2.58.4-h49af25d_2.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libsodium-1.0.20-h4ab18f5_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libsqlite-3.49.2-hee588c1_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libssh2-1.11.1-hcf80075_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libstdcxx-15.1.0-h8f9b012_2.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libstdcxx-ng-15.1.0-h4852527_2.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libthrift-0.21.0-h0e7cc3e_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libtiff-4.7.0-hf01ce69_5.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libtorch-2.7.0-cpu_mkl_hf6ddc5a_100.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libutf8proc-2.10.0-h4c51ac1_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libuuid-2.38.1-h0b41bf4_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libuv-1.50.0-hb9d3cd8_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libwebp-1.5.0-hae8dbeb_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libwebp-base-1.5.0-h851e524_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libxcb-1.17.0-h8a09558_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libxcrypt-4.4.36-hd590300_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libxkbcommon-1.10.0-h65c71a3_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libxml2-2.13.8-h4bc477f_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libxslt-1.1.39-h76b75d6_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libzlib-1.3.1-hb9d3cd8_2.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/llvm-openmp-20.1.5-h024ca30_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/lz4-c-1.10.0-h5888daf_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/lzo-2.10-hd590300_1001.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/marisa-trie-1.2.1-py310hf71b8c6_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/markdown-it-py-3.0.0-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/markupsafe-3.0.2-py310h89163eb_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/matplotlib-3.10.3-py310hff52083_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/matplotlib-base-3.10.3-py310h68603db_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/matplotlib-inline-0.1.7-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/mdurl-0.1.2-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/mistune-3.1.3-pyh29332c3_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/mkl-2024.2.2-ha957f24_16.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/mpc-1.3.1-h24ddda3_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/mpfr-4.2.1-h90cbb55_3.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/mpmath-1.3.0-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/multidict-6.4.4-py310h89163eb_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/multiprocess-0.70.15-py310h2372a71_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/munkres-1.1.4-pyh9f0ad1d_0.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/linux-64/mupdf-1.25.2-py310ha3268d5_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/murmurhash-1.0.10-py310hf71b8c6_2.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/mysql-common-9.0.1-h266115a_6.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/mysql-libs-9.0.1-he0572af_6.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/narwhals-1.40.0-pyhe01879c_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/nbclient-0.10.2-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/nbconvert-core-7.16.6-pyh29332c3_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/nbformat-5.10.4-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/ncurses-6.5-h2d0b736_3.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/nest-asyncio-1.6.0-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/networkx-3.4.2-pyh267e887_2.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/nlohmann_json-3.12.0-h3f2d84a_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/nltk-3.9.1-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/notebook-7.4.2-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/notebook-shim-0.2.4-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/numpy-1.26.4-py310hb13e2d6_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/openjpeg-2.5.3-h5fbd93e_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/openldap-2.6.10-he970967_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/openssl-3.5.0-h7b32b05_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/optree-0.15.0-py310h3788b33_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/orc-2.1.2-h17f744e_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/overrides-7.7.0-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/packaging-25.0-pyh29332c3_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/pandas-2.2.3-py310h5eaa309_3.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/pandocfilters-1.5.0-pyhd8ed1ab_0.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/linux-64/pango-1.56.3-h861ebed_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/parso-0.8.4-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/pcre2-10.44-hc749103_2.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/pdfminer.six-20231228-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/pdfplumber-0.11.5-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/pexpect-4.9.0-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/pickleshare-0.7.5-pyhd8ed1ab_1004.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/pillow-11.2.1-py310h7e6dc6c_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/pixman-0.46.0-h29eaf8c_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/pkg-config-0.29.2-h4bc722e_1009.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/pkgutil-resolve-name-1.3.10-pyhd8ed1ab_2.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/platformdirs-4.3.8-pyhe01879c_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/plotly-6.1.1-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/pluggy-1.6.0-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/preshed-3.0.9-py310hf71b8c6_2.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/prometheus-cpp-1.3.0-ha5d0236_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/prometheus_client-0.22.0-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/prompt-toolkit-3.0.51-pyha770c72_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/prompt_toolkit-3.0.51-hd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/propcache-0.3.1-py310h89163eb_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/psutil-7.0.0-py310ha75aee5_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/pthread-stubs-0.4-hb9d3cd8_1002.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/ptyprocess-0.7.0-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/pure_eval-0.2.3-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/pyarrow-20.0.0-py310hff52083_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/pyarrow-core-20.0.0-py310hac404ae_0_cpu.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/pybind11-2.13.6-pyhc790b64_3.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/pybind11-global-2.13.6-pyh217bc35_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/pycairo-1.28.0-py310h516fd05_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/pycparser-2.22-pyh29332c3_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/pydantic-2.11.4-pyh3cfb1c2_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/pydantic-core-2.33.2-py310hbcd0ec0_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/pygments-2.19.1-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/pymupdf-1.26.0-py310h5f8b001_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/pyparsing-3.2.3-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/pypdfium2-4.30.1-py310h4cff291_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/pyside6-6.8.3-py310hfd10a26_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/pysocks-1.7.1-pyha55dd90_7.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/pytest-8.3.5-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/python-3.10.17-hd6af730_0_cpython.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/python-dateutil-2.9.0.post0-pyhff2d567_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/python-dotenv-1.1.0-pyh29332c3_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/python-fastjsonschema-2.21.1-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/python-json-logger-2.0.7-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/python-tzdata-2025.2-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/python-xxhash-3.5.0-py310ha75aee5_2.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/python_abi-3.10-7_cp310.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/pytorch-2.7.0-cpu_mkl_py310_h8ec2884_100.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/pytz-2025.2-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/pyvis-0.3.2-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/pyyaml-6.0.2-py310h89163eb_2.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/pyzmq-26.4.0-py310h71f11fc_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/qhull-2020.2-h434a139_5.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/qt6-main-6.8.3-h588cce1_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/rav1e-0.7.1-h8fae777_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/re2-2024.07.02-h9925aae_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/readline-8.2-h8c095d6_2.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/referencing-0.36.2-pyh29332c3_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/regex-2024.11.6-py310ha75aee5_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/requests-2.32.3-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/rfc3339-validator-0.1.4-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/rfc3986-validator-0.1.1-pyh9f0ad1d_0.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/noarch/rich-14.0.0-pyh29332c3_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/rpds-py-0.25.1-py310hbcd0ec0_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/s2n-1.5.18-h763c568_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/safetensors-0.5.3-py310h505e2c1_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/scikit-learn-1.6.1-py310h27f47ee_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/scipy-1.15.2-py310h1d65ade_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/send2trash-1.8.3-pyh0d859eb_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/sentence-transformers-4.1.0-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/setuptools-75.8.2-pyhff2d567_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/shellingham-1.5.4-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/six-1.17.0-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/sleef-3.8-h1b44611_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/smart-open-7.1.0-hd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/smart_open-7.1.0-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/snappy-1.2.1-h8bd8927_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/sniffio-1.3.1-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/soupsieve-2.7-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/spacy-3.8.6-py310ha2bacc8_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/spacy-legacy-3.0.12-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/spacy-loggers-1.0.5-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/srsly-2.5.1-py310hf71b8c6_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/stack_data-0.6.3-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/svt-av1-3.0.2-h5888daf_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/sympy-1.14.0-pyh2585a3b_105.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/tbb-2021.13.0-hceb3a55_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/terminado-0.18.1-pyh0d859eb_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/tesseract-5.5.0-ha6794fa_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/thinc-8.3.2-py310ha2bacc8_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/threadpoolctl-3.6.0-pyhecae5ae_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/tinycss2-1.4.0-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/tk-8.6.13-noxft_h4845f30_101.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/tokenizers-0.21.1-py310hb1c6e2c_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/tomli-2.2.1-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/tornado-6.5.1-py310ha75aee5_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/tqdm-4.67.1-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/traitlets-5.14.3-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/transformers-4.52.3-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/typer-0.15.3-pyhf21524f_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/typer-slim-0.15.3-pyh29332c3_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/typer-slim-standard-0.15.3-h1a15894_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/types-python-dateutil-2.9.0.20250516-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/typing-extensions-4.13.2-h0e9735f_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/typing-inspection-0.4.1-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/typing_extensions-4.13.2-pyh29332c3_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/typing_utils-0.1.0-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/tzdata-2025b-h78e105d_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/ujson-5.10.0-py310hf71b8c6_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/unicodedata2-16.0.0-py310ha75aee5_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/uri-template-1.3.0-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/urllib3-2.4.0-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/wand-0.6.10-pyhd8ed1ab_0.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/noarch/wasabi-1.1.3-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/wayland-1.23.1-h3e06ad9_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/wcwidth-0.2.13-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/weasel-0.4.1-pyhd8ed1ab_2.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/webcolors-24.11.1-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/webencodings-0.5.1-pyhd8ed1ab_3.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/websocket-client-1.8.0-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/widgetsnbextension-4.0.14-pyhd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/wrapt-1.17.2-py310ha75aee5_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/x265-3.5-h924138e_3.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/linux-64/xcb-util-0.4.1-hb711507_2.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/xcb-util-cursor-0.1.5-hb9d3cd8_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/xcb-util-image-0.4.0-hb711507_2.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/xcb-util-keysyms-0.4.1-hb711507_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/xcb-util-renderutil-0.3.10-hb711507_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/xcb-util-wm-0.4.2-hb711507_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/xkeyboard-config-2.44-hb9d3cd8_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/xorg-libice-1.1.2-hb9d3cd8_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/xorg-libsm-1.2.6-he73a12e_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/xorg-libx11-1.8.12-h4f16b4b_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/xorg-libxau-1.0.12-hb9d3cd8_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/xorg-libxcomposite-0.4.6-hb9d3cd8_2.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/xorg-libxcursor-1.2.3-hb9d3cd8_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/xorg-libxdamage-1.1.6-hb9d3cd8_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/xorg-libxdmcp-1.1.5-hb9d3cd8_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/xorg-libxext-1.3.6-hb9d3cd8_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/xorg-libxfixes-6.0.1-hb9d3cd8_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/xorg-libxi-1.8.2-hb9d3cd8_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/xorg-libxinerama-1.1.5-h5888daf_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/xorg-libxrandr-1.5.4-hb9d3cd8_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/xorg-libxrender-0.9.12-hb9d3cd8_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/xorg-libxt-1.3.1-hb9d3cd8_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/xorg-libxtst-1.2.5-hb9d3cd8_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/xorg-libxxf86vm-1.1.6-hb9d3cd8_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/xxhash-0.8.3-hb47aa4a_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/yaml-0.2.5-h7f98852_2.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/linux-64/yarl-1.20.0-py310h89163eb_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/zeromq-4.3.5-h3b0a872_7.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/zipp-3.21.0-pyhd8ed1ab_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/zlib-1.3.1-hb9d3cd8_2.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/zstandard-0.23.0-py310ha75aee5_2.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/zstd-1.5.7-hb8e6e7a_2.conda
      - pypi: https://files.pythonhosted.org/packages/0d/10/b1e39c877ea0d06a7305129b738ed7be6074ad6d90c7858c65d845fc8b45/awscli-1.40.22-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/ab/dd/e047894efa3a39509f8fcc103dd096999aa52907c969d195af6b0d8e282f/botocore-1.38.23-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/18/64/8f26d84f18c4d421cc7ca8f4b1dfd080ae14ba15a627277fbd63c11d652e/conllu-6.0.0-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/93/69/e391bd51bc08ed9141ecd899a0ddb61ab6465309f1eb470905c0c8868081/docutils-0.19-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/c2/53/01e0bccd619fa19e287e340688e4c2f47cd0d160b32073dfc75d12130931/grobid_client_python-0.0.9-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/31/b4/b9b800c45527aadd64d5b442f9b932b00648617eb5d63d2c7a6587b7cafc/jmespath-1.0.1-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/b9/54/dd730b32ea14ea797530a4479b2ed46a6fb250f682a9cfb997e968bf0261/networkx-3.4.2-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/ed/9e/ba2d0b47b1c170270e67ed6a9761f5580cb59615388593e287c623a36e2d/nmslib-2.0.6.tar.gz
      - pypi: https://files.pythonhosted.org/packages/c8/f1/d6a797abb14f6283c0ddff96bbdd46937f64122b8c925cab503dd37f8214/pyasn1-0.6.1-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/48/0a/c99fb7d7e176f8b176ef19704a32e6a9c6aafdf19ef75a187f701fc15801/pysbd-0.3.4-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/7c/0d/8787b021d52eb8764c0bb18ab95f720cf554902044c6a5cb1865daf45763/python-louvain-0.16.tar.gz
      - pypi: https://files.pythonhosted.org/packages/e9/93/0c0f002031f18b53af7a6166103c02b9c0667be528944137cc954ec921b3/rsa-4.7.2-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/18/17/22bf8155aa0ea2305eefa3a6402e040df7ebe512d1310165eda1e233c3f8/s3transfer-0.13.0-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/eb/50/95cd574c3ccf4a268b334ea3c4c3cf9f95d1f24d6c0be82024d51c3e460b/scispacy-0.2.4.tar.gz
packages:
- conda: https://conda.anaconda.org/conda-forge/linux-64/_openmp_mutex-4.5-3_kmp_llvm.conda
  build_number: 3
  sha256: cec7343e76c9da6a42c7e7cba53391daa6b46155054ef61a5ef522ea27c5a058
  md5: ee5c2118262e30b972bc0b4db8ef0ba5
  depends:
  - llvm-openmp >=9.0.1
  license: BSD-3-Clause
  license_family: BSD
  purls: []
  size: 7649
  timestamp: 1741390353130
- conda: https://conda.anaconda.org/conda-forge/noarch/adwaita-icon-theme-48.0-unix_0.conda
  sha256: 63e532087119112c81d81c067e00d1fd49ff1b842ffea4469b78b505be63c042
  md5: 11539f9e49efaa281da735ded100b152
  depends:
  - __unix
  - hicolor-icon-theme
  - librsvg
  license: LGPL-3.0-or-later OR CC-BY-SA-3.0
  license_family: LGPL
  purls: []
  size: 610380
  timestamp: 1741999835753
- conda: https://conda.anaconda.org/conda-forge/noarch/aiohappyeyeballs-2.6.1-pyhd8ed1ab_0.conda
  sha256: 7842ddc678e77868ba7b92a726b437575b23aaec293bca0d40826f1026d90e27
  md5: 18fd895e0e775622906cdabfc3cf0fb4
  depends:
  - python >=3.9
  license: PSF-2.0
  license_family: PSF
  purls:
  - pkg:pypi/aiohappyeyeballs?source=hash-mapping
  size: 19750
  timestamp: 1741775303303
- conda: https://conda.anaconda.org/conda-forge/linux-64/aiohttp-3.11.18-py310h89163eb_0.conda
  sha256: 99e9119d6c92d371d48ff261f1ce974b4efadd7727520df9b2a3969bc0db9c69
  md5: b697b7466251282adc176c161a9ad8fc
  depends:
  - __glibc >=2.17,<3.0.a0
  - aiohappyeyeballs >=2.3.0
  - aiosignal >=1.1.2
  - async-timeout >=4.0,<6.0
  - attrs >=17.3.0
  - frozenlist >=1.1.1
  - libgcc >=13
  - multidict >=4.5,<7.0
  - propcache >=0.2.0
  - python >=3.10,<3.11.0a0
  - python_abi 3.10.* *_cp310
  - yarl >=1.17.0,<2.0
  license: MIT AND Apache-2.0
  license_family: Apache
  purls:
  - pkg:pypi/aiohttp?source=hash-mapping
  size: 808375
  timestamp: 1745256143419
- conda: https://conda.anaconda.org/conda-forge/noarch/aiosignal-1.3.2-pyhd8ed1ab_0.conda
  sha256: 7de8ced1918bbdadecf8e1c1c68237fe5709c097bd9e0d254f4cad118f4345d0
  md5: 1a3981115a398535dbe3f6d5faae3d36
  depends:
  - frozenlist >=1.1.0
  - python >=3.9
  license: Apache-2.0
  license_family: APACHE
  purls:
  - pkg:pypi/aiosignal?source=hash-mapping
  size: 13229
  timestamp: 1734342253061
- conda: https://conda.anaconda.org/conda-forge/linux-64/alsa-lib-1.2.14-hb9d3cd8_0.conda
  sha256: b9214bc17e89bf2b691fad50d952b7f029f6148f4ac4fe7c60c08f093efdf745
  md5: 76df83c2a9035c54df5d04ff81bcc02d
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  license: LGPL-2.1-or-later
  license_family: GPL
  purls: []
  size: 566531
  timestamp: 1744668655747
- conda: https://conda.anaconda.org/conda-forge/noarch/annotated-types-0.7.0-pyhd8ed1ab_1.conda
  sha256: e0ea1ba78fbb64f17062601edda82097fcf815012cf52bb704150a2668110d48
  md5: 2934f256a8acfe48f6ebb4fce6cde29c
  depends:
  - python >=3.9
  - typing-extensions >=4.0.0
  license: MIT
  license_family: MIT
  purls:
  - pkg:pypi/annotated-types?source=hash-mapping
  size: 18074
  timestamp: 1733247158254
- conda: https://conda.anaconda.org/conda-forge/noarch/anyio-4.9.0-pyh29332c3_0.conda
  sha256: b28e0f78bb0c7962630001e63af25a89224ff504e135a02e50d4d80b6155d386
  md5: 9749a2c77a7c40d432ea0927662d7e52
  depends:
  - exceptiongroup >=1.0.2
  - idna >=2.8
  - python >=3.9
  - sniffio >=1.1
  - typing_extensions >=4.5
  - python
  constrains:
  - trio >=0.26.1
  - uvloop >=0.21
  license: MIT
  license_family: MIT
  purls:
  - pkg:pypi/anyio?source=hash-mapping
  size: 126346
  timestamp: 1742243108743
- conda: https://conda.anaconda.org/conda-forge/linux-64/aom-3.9.1-hac33072_0.conda
  sha256: b08ef033817b5f9f76ce62dfcac7694e7b6b4006420372de22494503decac855
  md5: 346722a0be40f6edc53f12640d301338
  depends:
  - libgcc-ng >=12
  - libstdcxx-ng >=12
  license: BSD-2-Clause
  license_family: BSD
  purls: []
  size: 2706396
  timestamp: 1718551242397
- conda: https://conda.anaconda.org/conda-forge/noarch/argon2-cffi-23.1.0-pyhd8ed1ab_1.conda
  sha256: 7af62339394986bc470a7a231c7f37ad0173ffb41f6bc0e8e31b0be9e3b9d20f
  md5: a7ee488b71c30ada51c48468337b85ba
  depends:
  - argon2-cffi-bindings
  - python >=3.9
  - typing-extensions
  constrains:
  - argon2_cffi ==999
  license: MIT
  license_family: MIT
  purls:
  - pkg:pypi/argon2-cffi?source=hash-mapping
  size: 18594
  timestamp: 1733311166338
- conda: https://conda.anaconda.org/conda-forge/linux-64/argon2-cffi-bindings-21.2.0-py310ha75aee5_5.conda
  sha256: 1050f55294476b4d9b36ca3cf22b47f2f23d6e143ad6a177025bc5e5984d5409
  md5: a2da54f3a705d518c95a5b6de8ad8af6
  depends:
  - __glibc >=2.17,<3.0.a0
  - cffi >=1.0.1
  - libgcc >=13
  - python >=3.10,<3.11.0a0
  - python_abi 3.10.* *_cp310
  license: MIT
  license_family: MIT
  purls:
  - pkg:pypi/argon2-cffi-bindings?source=hash-mapping
  size: 34425
  timestamp: 1725356664523
- conda: https://conda.anaconda.org/conda-forge/noarch/arrow-1.3.0-pyhd8ed1ab_1.conda
  sha256: c4b0bdb3d5dee50b60db92f99da3e4c524d5240aafc0a5fcc15e45ae2d1a3cd1
  md5: 46b53236fdd990271b03c3978d4218a9
  depends:
  - python >=3.9
  - python-dateutil >=2.7.0
  - types-python-dateutil >=2.8.10
  license: Apache-2.0
  license_family: Apache
  purls:
  - pkg:pypi/arrow?source=hash-mapping
  size: 99951
  timestamp: 1733584345583
- conda: https://conda.anaconda.org/conda-forge/noarch/asttokens-3.0.0-pyhd8ed1ab_1.conda
  sha256: 93b14414b3b3ed91e286e1cbe4e7a60c4e1b1c730b0814d1e452a8ac4b9af593
  md5: 8f587de4bcf981e26228f268df374a9b
  depends:
  - python >=3.9
  constrains:
  - astroid >=2,<4
  license: Apache-2.0
  license_family: Apache
  purls:
  - pkg:pypi/asttokens?source=hash-mapping
  size: 28206
  timestamp: 1733250564754
- conda: https://conda.anaconda.org/conda-forge/noarch/async-lru-2.0.5-pyh29332c3_0.conda
  sha256: 3b7233041e462d9eeb93ea1dfe7b18aca9c358832517072054bb8761df0c324b
  md5: d9d0f99095a9bb7e3641bca8c6ad2ac7
  depends:
  - python >=3.9
  - typing_extensions >=4.0.0
  - python
  license: MIT
  license_family: MIT
  purls:
  - pkg:pypi/async-lru?source=hash-mapping
  size: 17335
  timestamp: 1742153708859
- conda: https://conda.anaconda.org/conda-forge/noarch/async-timeout-5.0.1-pyhd8ed1ab_1.conda
  sha256: 33d12250c870e06c9a313c6663cfbf1c50380b73dfbbb6006688c3134b29b45a
  md5: 5d842988b11a8c3ab57fb70840c83d24
  depends:
  - python >=3.9
  license: Apache-2.0
  license_family: Apache
  purls:
  - pkg:pypi/async-timeout?source=hash-mapping
  size: 11763
  timestamp: 1733235428203
- conda: https://conda.anaconda.org/conda-forge/linux-64/at-spi2-atk-2.38.0-h0630a04_3.tar.bz2
  sha256: 26ab9386e80bf196e51ebe005da77d57decf6d989b4f34d96130560bc133479c
  md5: 6b889f174df1e0f816276ae69281af4d
  depends:
  - at-spi2-core >=2.40.0,<2.41.0a0
  - atk-1.0 >=2.36.0
  - dbus >=1.13.6,<2.0a0
  - libgcc-ng >=9.3.0
  - libglib >=2.68.1,<3.0a0
  license: LGPL-2.1-or-later
  license_family: LGPL
  purls: []
  size: 339899
  timestamp: 1619122953439
- conda: https://conda.anaconda.org/conda-forge/linux-64/at-spi2-core-2.40.3-h0630a04_0.tar.bz2
  sha256: c4f9b66bd94c40d8f1ce1fad2d8b46534bdefda0c86e3337b28f6c25779f258d
  md5: 8cb2fc4cd6cc63f1369cfa318f581cc3
  depends:
  - dbus >=1.13.6,<2.0a0
  - libgcc-ng >=9.3.0
  - libglib >=2.68.3,<3.0a0
  - xorg-libx11
  - xorg-libxi
  - xorg-libxtst
  license: LGPL-2.1-or-later
  license_family: LGPL
  purls: []
  size: 658390
  timestamp: 1625848454791
- conda: https://conda.anaconda.org/conda-forge/linux-64/atk-1.0-2.38.0-h04ea711_2.conda
  sha256: df682395d05050cd1222740a42a551281210726a67447e5258968dd55854302e
  md5: f730d54ba9cd543666d7220c9f7ed563
  depends:
  - libgcc-ng >=12
  - libglib >=2.80.0,<3.0a0
  - libstdcxx-ng >=12
  constrains:
  - atk-1.0 2.38.0
  license: LGPL-2.0-or-later
  license_family: LGPL
  purls: []
  size: 355900
  timestamp: 1713896169874
- conda: https://conda.anaconda.org/conda-forge/noarch/attrs-25.3.0-pyh71513ae_0.conda
  sha256: 99c53ffbcb5dc58084faf18587b215f9ac8ced36bbfb55fa807c00967e419019
  md5: a10d11958cadc13fdb43df75f8b1903f
  depends:
  - python >=3.9
  license: MIT
  license_family: MIT
  purls:
  - pkg:pypi/attrs?source=hash-mapping
  size: 57181
  timestamp: 1741918625732
- conda: https://conda.anaconda.org/conda-forge/linux-64/aws-c-auth-0.9.0-h59ae206_7.conda
  sha256: 796f0fd63c4f05e5784dca0edc838ab6288bdb8c4c12ebd45bde93fdbd683495
  md5: ca157ee18f02c33646d975995631b39e
  depends:
  - __glibc >=2.17,<3.0.a0
  - aws-c-cal >=0.9.0,<0.9.1.0a0
  - aws-c-common >=0.12.3,<0.12.4.0a0
  - aws-c-http >=0.10.1,<0.10.2.0a0
  - aws-c-io >=0.19.0,<0.19.1.0a0
  - aws-c-sdkutils >=0.2.3,<0.2.4.0a0
  - libgcc >=13
  license: Apache-2.0
  license_family: Apache
  purls: []
  size: 111152
  timestamp: 1747190463145
- conda: https://conda.anaconda.org/conda-forge/linux-64/aws-c-cal-0.9.0-h5e3027f_1.conda
  sha256: da8e6d0fa83a80e6f0f9c59ae0ac157915fb0b684020cc16c9915d4d7171fe20
  md5: 220588a5c6c9341a39d9e399848e5554
  depends:
  - __glibc >=2.17,<3.0.a0
  - aws-c-common >=0.12.3,<0.12.4.0a0
  - libgcc >=13
  - openssl >=3.5.0,<4.0a0
  license: Apache-2.0
  license_family: Apache
  purls: []
  size: 50521
  timestamp: 1747127810932
- conda: https://conda.anaconda.org/conda-forge/linux-64/aws-c-common-0.12.3-hb9d3cd8_0.conda
  sha256: 251883d45fbc3bc88a8290da073f54eb9d17e8b9edfa464d80cff1b948c571ec
  md5: 8448031a22c697fac3ed98d69e8a9160
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  license: Apache-2.0
  license_family: Apache
  purls: []
  size: 236494
  timestamp: 1747101172537
- conda: https://conda.anaconda.org/conda-forge/linux-64/aws-c-compression-0.3.1-hafb2847_5.conda
  sha256: 68e7ec0ab4f5973343de089ac71c7b9b9387c35640c61e0236ad45fc3dbfaaaa
  md5: e96cc668c0f9478f5771b37d57f90386
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - aws-c-common >=0.12.3,<0.12.4.0a0
  license: Apache-2.0
  license_family: APACHE
  purls: []
  size: 21817
  timestamp: 1747144982788
- conda: https://conda.anaconda.org/conda-forge/linux-64/aws-c-event-stream-0.5.4-h2dcaabb_9.conda
  sha256: 5df00b73c5b6fa27769a18f6d3172f45f2fbe2b1e440e320199702a2231306f4
  md5: 2f2ffcdfeabac698297fce1259e51a2a
  depends:
  - libgcc >=13
  - libstdcxx >=13
  - libgcc >=13
  - __glibc >=2.17,<3.0.a0
  - aws-c-io >=0.19.0,<0.19.1.0a0
  - aws-checksums >=0.2.7,<0.2.8.0a0
  - aws-c-common >=0.12.3,<0.12.4.0a0
  license: Apache-2.0
  license_family: APACHE
  purls: []
  size: 57205
  timestamp: 1747185871709
- conda: https://conda.anaconda.org/conda-forge/linux-64/aws-c-http-0.10.1-hb50fa74_1.conda
  sha256: d811159f8ec3f3578dbf27a4b3d2756cd4cbc70e42f5e6e71972b6b50ddc8161
  md5: 2bb746bfe603e4949d99404b25c639ea
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - aws-c-common >=0.12.3,<0.12.4.0a0
  - aws-c-io >=0.19.0,<0.19.1.0a0
  - aws-c-compression >=0.3.1,<0.3.2.0a0
  - aws-c-cal >=0.9.0,<0.9.1.0a0
  license: Apache-2.0
  license_family: APACHE
  purls: []
  size: 223036
  timestamp: 1747186878815
- conda: https://conda.anaconda.org/conda-forge/linux-64/aws-c-io-0.19.0-h7962f60_2.conda
  sha256: a2c6d887fb682d7128703a1b6069aaad02dcfc455f03fcb9d8269da6fa9cfed7
  md5: 7a4be9867bab106d87febec673094a9e
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - s2n >=1.5.18,<1.5.19.0a0
  - aws-c-cal >=0.9.0,<0.9.1.0a0
  - aws-c-common >=0.12.3,<0.12.4.0a0
  license: Apache-2.0
  license_family: APACHE
  purls: []
  size: 179077
  timestamp: 1747159979745
- conda: https://conda.anaconda.org/conda-forge/linux-64/aws-c-mqtt-0.13.0-h35de22e_3.conda
  sha256: 7275a7ca192306ff3b43cedc63bb854ce6279617f8d4799af4837ef05383c35c
  md5: df3ea458761b3fdf9e6eb7d8a38c121a
  depends:
  - libgcc >=13
  - __glibc >=2.17,<3.0.a0
  - aws-c-http >=0.10.1,<0.10.2.0a0
  - aws-c-io >=0.19.0,<0.19.1.0a0
  - aws-c-common >=0.12.3,<0.12.4.0a0
  license: Apache-2.0
  license_family: APACHE
  purls: []
  size: 215707
  timestamp: 1747215213079
- conda: https://conda.anaconda.org/conda-forge/linux-64/aws-c-s3-0.7.17-h50d7d24_2.conda
  sha256: 9d952875d665b55a1a92d1b534a72eeffed6618d5e8131aca6be4a895705fa56
  md5: 701bf42db0ec5de1e56b66ae0638d20b
  depends:
  - libgcc >=13
  - __glibc >=2.17,<3.0.a0
  - openssl >=3.5.0,<4.0a0
  - aws-checksums >=0.2.7,<0.2.8.0a0
  - aws-c-cal >=0.9.0,<0.9.1.0a0
  - aws-c-auth >=0.9.0,<0.9.1.0a0
  - aws-c-http >=0.10.1,<0.10.2.0a0
  - aws-c-common >=0.12.3,<0.12.4.0a0
  - aws-c-io >=0.19.0,<0.19.1.0a0
  license: Apache-2.0
  license_family: APACHE
  purls: []
  size: 129742
  timestamp: 1747215633728
- conda: https://conda.anaconda.org/conda-forge/linux-64/aws-c-sdkutils-0.2.3-hafb2847_5.conda
  sha256: 49bcc575df6f31de392fcfbeb8f5cae80c45b77021818bb4b6d41e138d7f3205
  md5: 51ffa5a303e8256dcb176f14d78887b4
  depends:
  - libgcc >=13
  - __glibc >=2.17,<3.0.a0
  - aws-c-common >=0.12.3,<0.12.4.0a0
  license: Apache-2.0
  license_family: APACHE
  purls: []
  size: 58967
  timestamp: 1747138537291
- conda: https://conda.anaconda.org/conda-forge/linux-64/aws-checksums-0.2.7-hafb2847_1.conda
  sha256: 03a5e4b3dcda35696133632273043d0b81e55129ff0f9e6d75483aa8eb96371b
  md5: 6d28d50637fac4f081a0903b4b33d56d
  depends:
  - libgcc >=13
  - __glibc >=2.17,<3.0.a0
  - aws-c-common >=0.12.3,<0.12.4.0a0
  license: Apache-2.0
  license_family: APACHE
  purls: []
  size: 76627
  timestamp: 1747141741534
- conda: https://conda.anaconda.org/conda-forge/linux-64/aws-crt-cpp-0.32.5-h2811929_3.conda
  sha256: 0da65b4e3afecf205323f8fdfd2fa5d2a26d295d393d3548360d2de68d266c49
  md5: c38733af13b256b8893a6af0d2a1d346
  depends:
  - libstdcxx >=13
  - libgcc >=13
  - __glibc >=2.17,<3.0.a0
  - aws-c-mqtt >=0.13.0,<0.13.1.0a0
  - aws-c-common >=0.12.3,<0.12.4.0a0
  - aws-c-event-stream >=0.5.4,<0.5.5.0a0
  - aws-c-sdkutils >=0.2.3,<0.2.4.0a0
  - aws-c-io >=0.19.0,<0.19.1.0a0
  - aws-c-http >=0.10.1,<0.10.2.0a0
  - aws-c-s3 >=0.7.17,<0.7.18.0a0
  - aws-c-cal >=0.9.0,<0.9.1.0a0
  - aws-c-auth >=0.9.0,<0.9.1.0a0
  license: Apache-2.0
  license_family: APACHE
  purls: []
  size: 394536
  timestamp: 1747232223388
- conda: https://conda.anaconda.org/conda-forge/linux-64/aws-sdk-cpp-1.11.510-hffe9a0f_8.conda
  sha256: 2f5d05c90ac9c3dd7acecb2c4215545d75a05e79d8a55be6570a5a301a8fba33
  md5: 4cd13ac60fb622ab49dfe949f2cd3051
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - libstdcxx >=13
  - libgcc >=13
  - aws-c-common >=0.12.3,<0.12.4.0a0
  - aws-c-event-stream >=0.5.4,<0.5.5.0a0
  - libcurl >=8.13.0,<9.0a0
  - aws-crt-cpp >=0.32.5,<0.32.6.0a0
  - libzlib >=1.3.1,<2.0a0
  license: Apache-2.0
  license_family: APACHE
  purls: []
  size: 3401491
  timestamp: 1747246390329
- pypi: https://files.pythonhosted.org/packages/0d/10/b1e39c877ea0d06a7305129b738ed7be6074ad6d90c7858c65d845fc8b45/awscli-1.40.22-py3-none-any.whl
  name: awscli
  version: 1.40.22
  sha256: ad8c9afa0dd6e3f0044aa26954f23ba9ee69d5991a9358c0c46c940b150abd07
  requires_dist:
  - botocore==1.38.23
  - docutils>=0.18.1,<=0.19
  - s3transfer>=0.13.0,<0.14.0
  - pyyaml>=3.10,<6.1
  - colorama>=0.2.5,<0.4.7
  - rsa>=3.1.2,<4.8
  requires_python: '>=3.9'
- conda: https://conda.anaconda.org/conda-forge/linux-64/azure-core-cpp-1.14.0-h5cfcd09_0.conda
  sha256: fe07debdb089a3db17f40a7f20d283d75284bb4fc269ef727b8ba6fc93f7cb5a
  md5: 0a8838771cc2e985cd295e01ae83baf1
  depends:
  - __glibc >=2.17,<3.0.a0
  - libcurl >=8.10.1,<9.0a0
  - libgcc >=13
  - libstdcxx >=13
  - openssl >=3.3.2,<4.0a0
  license: MIT
  license_family: MIT
  purls: []
  size: 345117
  timestamp: 1728053909574
- conda: https://conda.anaconda.org/conda-forge/linux-64/azure-identity-cpp-1.10.0-h113e628_0.conda
  sha256: 286b31616c191486626cb49e9ceb5920d29394b9e913c23adb7eb637629ba4de
  md5: 73f73f60854f325a55f1d31459f2ab73
  depends:
  - __glibc >=2.17,<3.0.a0
  - azure-core-cpp >=1.14.0,<1.14.1.0a0
  - libgcc >=13
  - libstdcxx >=13
  - openssl >=3.3.2,<4.0a0
  license: MIT
  license_family: MIT
  purls: []
  size: 232351
  timestamp: 1728486729511
- conda: https://conda.anaconda.org/conda-forge/linux-64/azure-storage-blobs-cpp-12.13.0-h3cf044e_1.conda
  sha256: 2606260e5379eed255bcdc6adc39b93fb31477337bcd911c121fc43cd29bf394
  md5: 7eb66060455c7a47d9dcdbfa9f46579b
  depends:
  - __glibc >=2.17,<3.0.a0
  - azure-core-cpp >=1.14.0,<1.14.1.0a0
  - azure-storage-common-cpp >=12.8.0,<12.8.1.0a0
  - libgcc >=13
  - libstdcxx >=13
  license: MIT
  license_family: MIT
  purls: []
  size: 549342
  timestamp: 1728578123088
- conda: https://conda.anaconda.org/conda-forge/linux-64/azure-storage-common-cpp-12.8.0-h736e048_1.conda
  sha256: 273475f002b091b66ce7366da04bf164c3732c03f8692ab2ee2d23335b6a82ba
  md5: 13de36be8de3ae3f05ba127631599213
  depends:
  - __glibc >=2.17,<3.0.a0
  - azure-core-cpp >=1.14.0,<1.14.1.0a0
  - libgcc >=13
  - libstdcxx >=13
  - libxml2 >=2.12.7,<2.14.0a0
  - openssl >=3.3.2,<4.0a0
  license: MIT
  license_family: MIT
  purls: []
  size: 149312
  timestamp: 1728563338704
- conda: https://conda.anaconda.org/conda-forge/linux-64/azure-storage-files-datalake-cpp-12.12.0-ha633028_1.conda
  sha256: 5371e4f3f920933bb89b926a85a67f24388227419abd6e99f6086481e5e8d5f2
  md5: 7c1980f89dd41b097549782121a73490
  depends:
  - __glibc >=2.17,<3.0.a0
  - azure-core-cpp >=1.14.0,<1.14.1.0a0
  - azure-storage-blobs-cpp >=12.13.0,<12.13.1.0a0
  - azure-storage-common-cpp >=12.8.0,<12.8.1.0a0
  - libgcc >=13
  - libstdcxx >=13
  license: MIT
  license_family: MIT
  purls: []
  size: 287366
  timestamp: 1728729530295
- conda: https://conda.anaconda.org/conda-forge/noarch/babel-2.17.0-pyhd8ed1ab_0.conda
  sha256: 1c656a35800b7f57f7371605bc6507c8d3ad60fbaaec65876fce7f73df1fc8ac
  md5: 0a01c169f0ab0f91b26e77a3301fbfe4
  depends:
  - python >=3.9
  - pytz >=2015.7
  license: BSD-3-Clause
  license_family: BSD
  purls:
  - pkg:pypi/babel?source=compressed-mapping
  size: 6938256
  timestamp: 1738490268466
- conda: https://conda.anaconda.org/conda-forge/noarch/beautifulsoup4-4.13.4-pyha770c72_0.conda
  sha256: ddb0df12fd30b2d36272f5daf6b6251c7625d6a99414d7ea930005bbaecad06d
  md5: 9f07c4fc992adb2d6c30da7fab3959a7
  depends:
  - python >=3.9
  - soupsieve >=1.2
  - typing-extensions
  license: MIT
  license_family: MIT
  purls:
  - pkg:pypi/beautifulsoup4?source=compressed-mapping
  size: 146613
  timestamp: 1744783307123
- conda: https://conda.anaconda.org/conda-forge/noarch/bleach-6.2.0-pyh29332c3_4.conda
  sha256: a05971bb80cca50ce9977aad3f7fc053e54ea7d5321523efc7b9a6e12901d3cd
  md5: f0b4c8e370446ef89797608d60a564b3
  depends:
  - python >=3.9
  - webencodings
  - python
  constrains:
  - tinycss >=1.1.0,<1.5
  license: Apache-2.0 AND MIT
  purls:
  - pkg:pypi/bleach?source=hash-mapping
  size: 141405
  timestamp: 1737382993425
- conda: https://conda.anaconda.org/conda-forge/noarch/bleach-with-css-6.2.0-h82add2a_4.conda
  sha256: 0aba699344275b3972bd751f9403316edea2ceb942db12f9f493b63c74774a46
  md5: a30e9406c873940383555af4c873220d
  depends:
  - bleach ==6.2.0 pyh29332c3_4
  - tinycss2
  license: Apache-2.0 AND MIT
  purls: []
  size: 4213
  timestamp: 1737382993425
- pypi: https://files.pythonhosted.org/packages/ab/dd/e047894efa3a39509f8fcc103dd096999aa52907c969d195af6b0d8e282f/botocore-1.38.23-py3-none-any.whl
  name: botocore
  version: 1.38.23
  sha256: a7f818672f10d7a080c2c4558428011c3e0abc1039a047d27ac76ec846158457
  requires_dist:
  - jmespath>=0.7.1,<2.0.0
  - python-dateutil>=2.1,<3.0.0
  - urllib3>=1.25.4,<1.27 ; python_full_version < '3.10'
  - urllib3>=1.25.4,!=2.2.0,<3 ; python_full_version >= '3.10'
  - awscrt==0.23.8 ; extra == 'crt'
  requires_python: '>=3.9'
- conda: https://conda.anaconda.org/conda-forge/linux-64/brotli-1.1.0-hb9d3cd8_2.conda
  sha256: fcb0b5b28ba7492093e54f3184435144e074dfceab27ac8e6a9457e736565b0b
  md5: 98514fe74548d768907ce7a13f680e8f
  depends:
  - __glibc >=2.17,<3.0.a0
  - brotli-bin 1.1.0 hb9d3cd8_2
  - libbrotlidec 1.1.0 hb9d3cd8_2
  - libbrotlienc 1.1.0 hb9d3cd8_2
  - libgcc >=13
  license: MIT
  license_family: MIT
  purls: []
  size: 19264
  timestamp: 1725267697072
- conda: https://conda.anaconda.org/conda-forge/linux-64/brotli-bin-1.1.0-hb9d3cd8_2.conda
  sha256: 261364d7445513b9a4debc345650fad13c627029bfc800655a266bf1e375bc65
  md5: c63b5e52939e795ba8d26e35d767a843
  depends:
  - __glibc >=2.17,<3.0.a0
  - libbrotlidec 1.1.0 hb9d3cd8_2
  - libbrotlienc 1.1.0 hb9d3cd8_2
  - libgcc >=13
  license: MIT
  license_family: MIT
  purls: []
  size: 18881
  timestamp: 1725267688731
- conda: https://conda.anaconda.org/conda-forge/linux-64/brotli-python-1.1.0-py310hf71b8c6_2.conda
  sha256: 14f1e89d3888d560a553f40ac5ba83e4435a107552fa5b2b2029a7472554c1ef
  md5: bf502c169c71e3c6ac0d6175addfacc2
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - libstdcxx >=13
  - python >=3.10,<3.11.0a0
  - python_abi 3.10.* *_cp310
  constrains:
  - libbrotlicommon 1.1.0 hb9d3cd8_2
  license: MIT
  license_family: MIT
  purls:
  - pkg:pypi/brotli?source=hash-mapping
  size: 349668
  timestamp: 1725267875087
- conda: https://conda.anaconda.org/conda-forge/linux-64/bzip2-1.0.8-h4bc722e_7.conda
  sha256: 5ced96500d945fb286c9c838e54fa759aa04a7129c59800f0846b4335cee770d
  md5: 62ee74e96c5ebb0af99386de58cf9553
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc-ng >=12
  license: bzip2-1.0.6
  license_family: BSD
  purls: []
  size: 252783
  timestamp: 1720974456583
- conda: https://conda.anaconda.org/conda-forge/linux-64/c-ares-1.34.5-hb9d3cd8_0.conda
  sha256: f8003bef369f57396593ccd03d08a8e21966157269426f71e943f96e4b579aeb
  md5: f7f0d6cc2dc986d42ac2689ec88192be
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  license: MIT
  license_family: MIT
  purls: []
  size: 206884
  timestamp: 1744127994291
- conda: https://conda.anaconda.org/conda-forge/noarch/ca-certificates-2025.4.26-hbd8a1cb_0.conda
  sha256: 2a70ed95ace8a3f8a29e6cd1476a943df294a7111dfb3e152e3478c4c889b7ac
  md5: 95db94f75ba080a22eb623590993167b
  depends:
  - __unix
  license: ISC
  purls: []
  size: 152283
  timestamp: 1745653616541
- conda: https://conda.anaconda.org/conda-forge/noarch/cached-property-1.5.2-hd8ed1ab_1.tar.bz2
  noarch: python
  sha256: 561e6660f26c35d137ee150187d89767c988413c978e1b712d53f27ddf70ea17
  md5: 9b347a7ec10940d3f7941ff6c460b551
  depends:
  - cached_property >=1.5.2,<1.5.3.0a0
  license: BSD-3-Clause
  license_family: BSD
  purls: []
  size: 4134
  timestamp: 1615209571450
- conda: https://conda.anaconda.org/conda-forge/noarch/cached_property-1.5.2-pyha770c72_1.tar.bz2
  sha256: 6dbf7a5070cc43d90a1e4c2ec0c541c69d8e30a0e25f50ce9f6e4a432e42c5d7
  md5: 576d629e47797577ab0f1b351297ef4a
  depends:
  - python >=3.6
  license: BSD-3-Clause
  license_family: BSD
  purls:
  - pkg:pypi/cached-property?source=hash-mapping
  size: 11065
  timestamp: 1615209567874
- conda: https://conda.anaconda.org/conda-forge/linux-64/cairo-1.18.4-h3394656_0.conda
  sha256: 3bd6a391ad60e471de76c0e9db34986c4b5058587fbf2efa5a7f54645e28c2c7
  md5: 09262e66b19567aff4f592fb53b28760
  depends:
  - __glibc >=2.17,<3.0.a0
  - fontconfig >=2.15.0,<3.0a0
  - fonts-conda-ecosystem
  - freetype >=2.12.1,<3.0a0
  - icu >=75.1,<76.0a0
  - libexpat >=2.6.4,<3.0a0
  - libgcc >=13
  - libglib >=2.82.2,<3.0a0
  - libpng >=1.6.47,<1.7.0a0
  - libstdcxx >=13
  - libxcb >=1.17.0,<2.0a0
  - libzlib >=1.3.1,<2.0a0
  - pixman >=0.44.2,<1.0a0
  - xorg-libice >=1.1.2,<2.0a0
  - xorg-libsm >=1.2.5,<2.0a0
  - xorg-libx11 >=1.8.11,<2.0a0
  - xorg-libxext >=1.3.6,<2.0a0
  - xorg-libxrender >=0.9.12,<0.10.0a0
  license: LGPL-2.1-only or MPL-1.1
  purls: []
  size: 978114
  timestamp: 1741554591855
- conda: https://conda.anaconda.org/conda-forge/linux-64/catalogue-2.0.10-py310hff52083_1.conda
  sha256: 020363cc863afdc33e1908114799b075fef3421ffad1f66acaef5f1c047363a6
  md5: e65d6c9a6d3e17bb7c115eab9efef5b2
  depends:
  - python >=3.10,<3.11.0a0
  - python_abi 3.10.* *_cp310
  license: MIT
  license_family: MIT
  purls:
  - pkg:pypi/catalogue?source=hash-mapping
  size: 35260
  timestamp: 1736092498807
- conda: https://conda.anaconda.org/conda-forge/noarch/certifi-2025.4.26-pyhd8ed1ab_0.conda
  sha256: 52aa837642fd851b3f7ad3b1f66afc5366d133c1d452323f786b0378a391915c
  md5: c33eeaaa33f45031be34cda513df39b6
  depends:
  - python >=3.9
  license: ISC
  purls:
  - pkg:pypi/certifi?source=hash-mapping
  size: 157200
  timestamp: 1746569627830
- conda: https://conda.anaconda.org/conda-forge/linux-64/cffi-1.17.1-py310h8deb56e_0.conda
  sha256: 1b389293670268ab80c3b8735bc61bc71366862953e000efbb82204d00e41b6c
  md5: 1fc24a3196ad5ede2a68148be61894f4
  depends:
  - __glibc >=2.17,<3.0.a0
  - libffi >=3.4,<4.0a0
  - libgcc >=13
  - pycparser
  - python >=3.10,<3.11.0a0
  - python_abi 3.10.* *_cp310
  license: MIT
  license_family: MIT
  purls:
  - pkg:pypi/cffi?source=hash-mapping
  size: 243532
  timestamp: 1725560630552
- conda: https://conda.anaconda.org/conda-forge/noarch/charset-normalizer-3.4.2-pyhd8ed1ab_0.conda
  sha256: 535ae5dcda8022e31c6dc063eb344c80804c537a5a04afba43a845fa6fa130f5
  md5: 40fe4284b8b5835a9073a645139f35af
  depends:
  - python >=3.9
  license: MIT
  license_family: MIT
  purls:
  - pkg:pypi/charset-normalizer?source=compressed-mapping
  size: 50481
  timestamp: 1746214981991
- conda: https://conda.anaconda.org/conda-forge/noarch/click-8.2.1-pyh707e725_0.conda
  sha256: 8aee789c82d8fdd997840c952a586db63c6890b00e88c4fb6e80a38edd5f51c0
  md5: 94b550b8d3a614dbd326af798c7dfb40
  depends:
  - __unix
  - python >=3.10
  license: BSD-3-Clause
  license_family: BSD
  purls:
  - pkg:pypi/click?source=hash-mapping
  size: 87749
  timestamp: 1747811451319
- conda: https://conda.anaconda.org/conda-forge/noarch/cloudpathlib-0.21.1-pyhd8ed1ab_0.conda
  sha256: 74da6f68f637627a5b53b1bf962fad6e34d2d529f0f32e4ec56bbf27dcc63624
  md5: d401b7d72e2cf55444b12110ed953c9d
  depends:
  - python >=3.9
  - typing_extensions
  license: MIT
  license_family: MIT
  purls:
  - pkg:pypi/cloudpathlib?source=hash-mapping
  size: 44459
  timestamp: 1747332027476
- conda: https://conda.anaconda.org/conda-forge/noarch/cloudpickle-3.1.1-pyhd8ed1ab_0.conda
  sha256: 21ecead7268241007bf65691610cd7314da68c1f88113092af690203b5780db5
  md5: 364ba6c9fb03886ac979b482f39ebb92
  depends:
  - python >=3.9
  license: BSD-3-Clause
  license_family: BSD
  purls:
  - pkg:pypi/cloudpickle?source=hash-mapping
  size: 25870
  timestamp: 1736947650712
- conda: https://conda.anaconda.org/conda-forge/noarch/colorama-0.4.6-pyhd8ed1ab_1.conda
  sha256: ab29d57dc70786c1269633ba3dff20288b81664d3ff8d21af995742e2bb03287
  md5: 962b9857ee8e7018c22f2776ffa0b2d7
  depends:
  - python >=3.9
  license: BSD-3-Clause
  license_family: BSD
  purls:
  - pkg:pypi/colorama?source=hash-mapping
  size: 27011
  timestamp: 1733218222191
- conda: https://conda.anaconda.org/conda-forge/noarch/comm-0.2.2-pyhd8ed1ab_1.conda
  sha256: 7e87ef7c91574d9fac19faedaaee328a70f718c9b4ddadfdc0ba9ac021bd64af
  md5: 74673132601ec2b7fc592755605f4c1b
  depends:
  - python >=3.9
  - traitlets >=5.3
  license: BSD-3-Clause
  license_family: BSD
  purls:
  - pkg:pypi/comm?source=hash-mapping
  size: 12103
  timestamp: 1733503053903
- conda: https://conda.anaconda.org/conda-forge/noarch/confection-0.1.5-pyhecae5ae_0.conda
  sha256: caeecf2eeb268b64830156d2ab58eb6419514e1f1ab4250d6995a6eccdf8ec7c
  md5: cb7c903ea9e763e1e026d8633ae81964
  depends:
  - pydantic >=1.7.4,!=1.8,!=1.8.1,<3.0.0
  - python >=3.9
  - srsly >=2.4.0,<3.0.0
  - typing_extensions >=*******,<5.0.0
  license: MIT
  license_family: MIT
  purls:
  - pkg:pypi/confection?source=hash-mapping
  size: 37405
  timestamp: 1740630763363
- pypi: https://files.pythonhosted.org/packages/18/64/8f26d84f18c4d421cc7ca8f4b1dfd080ae14ba15a627277fbd63c11d652e/conllu-6.0.0-py3-none-any.whl
  name: conllu
  version: 6.0.0
  sha256: c47206a0912f768bfae429d3d3c2c7f5ed068babd2502663e865cfb21532cbcc
  requires_dist:
  - tox ; extra == 'test'
  requires_python: '>=3.8'
- conda: https://conda.anaconda.org/conda-forge/linux-64/contourpy-1.3.2-py310h3788b33_0.conda
  sha256: 5231c1b68e01a9bc9debabc077a6fb48c4395206d59f40a4598d1d5e353e11d8
  md5: b6420d29123c7c823de168f49ccdfe6a
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - libstdcxx >=13
  - numpy >=1.23
  - python >=3.10,<3.11.0a0
  - python_abi 3.10.* *_cp310
  license: BSD-3-Clause
  license_family: BSD
  purls:
  - pkg:pypi/contourpy?source=compressed-mapping
  size: 261280
  timestamp: 1744743236964
- conda: https://conda.anaconda.org/conda-forge/noarch/cpython-3.10.17-py310hd8ed1ab_0.conda
  noarch: generic
  sha256: 6944d47f2bf3c443d5af855ee0c77156da1b90c6f0e79cedc3b934bcd2794d64
  md5: e2b81369f0473107784f8b7da8e6a8e9
  depends:
  - python >=3.10,<3.11.0a0
  - python_abi * *_cp310
  license: Python-2.0
  purls: []
  size: 50554
  timestamp: 1744323109983
- conda: https://conda.anaconda.org/conda-forge/linux-64/cryptography-45.0.2-py310h6c63255_0.conda
  sha256: 352727f663909dec9ac9226e926a3f70d7293960f1a128dadb615cbcef71069a
  md5: 17533018e8d421e7b5d7eab6a1590c56
  depends:
  - __glibc >=2.17,<3.0.a0
  - cffi >=1.12
  - libgcc >=13
  - openssl >=3.5.0,<4.0a0
  - python >=3.10,<3.11.0a0
  - python_abi 3.10.* *_cp310
  constrains:
  - __glibc >=2.17
  license: Apache-2.0 AND BSD-3-Clause AND PSF-2.0 AND MIT
  license_family: BSD
  purls:
  - pkg:pypi/cryptography?source=hash-mapping
  size: 1610838
  timestamp: 1747571885303
- conda: https://conda.anaconda.org/conda-forge/linux-64/curl-8.13.0-h332b0f4_0.conda
  sha256: e01eab0947009ac3bd9f45b565ad7d821d2c7621d9394694a49e296c63ef680d
  md5: d50b765d509a4fe2e723b069266e17eb
  depends:
  - __glibc >=2.17,<3.0.a0
  - krb5 >=1.21.3,<1.22.0a0
  - libcurl 8.13.0 h332b0f4_0
  - libgcc >=13
  - libssh2 >=1.11.1,<2.0a0
  - libzlib >=1.3.1,<2.0a0
  - openssl >=3.4.1,<4.0a0
  - zstd >=1.5.7,<1.6.0a0
  license: curl
  license_family: MIT
  purls: []
  size: 182690
  timestamp: 1743601704972
- conda: https://conda.anaconda.org/conda-forge/noarch/cycler-0.12.1-pyhd8ed1ab_1.conda
  sha256: 9827efa891e507a91a8a2acf64e210d2aff394e1cde432ad08e1f8c66b12293c
  md5: 44600c4667a319d67dbe0681fc0bc833
  depends:
  - python >=3.9
  license: BSD-3-Clause
  license_family: BSD
  purls:
  - pkg:pypi/cycler?source=hash-mapping
  size: 13399
  timestamp: 1733332563512
- conda: https://conda.anaconda.org/conda-forge/linux-64/cymem-2.0.11-py310hf71b8c6_0.conda
  sha256: 1b5e56522b62aedb4f07ed15d540b3ca2c039a861244a2a82659b84d1722ddc3
  md5: 54b3f603a32130c44eba960fcc375a99
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - libstdcxx >=13
  - python >=3.10,<3.11.0a0
  - python_abi 3.10.* *_cp310
  license: MIT
  license_family: MIT
  purls:
  - pkg:pypi/cymem?source=hash-mapping
  size: 51716
  timestamp: 1737126050744
- conda: https://conda.anaconda.org/conda-forge/linux-64/cyrus-sasl-2.1.27-h54b06d7_7.conda
  sha256: d2ea5e52da745c4249e1a818095a28f9c57bd4df22cbfc645352defa468e86c2
  md5: dce22f70b4e5a407ce88f2be046f4ceb
  depends:
  - krb5 >=1.21.1,<1.22.0a0
  - libgcc-ng >=12
  - libntlm
  - libstdcxx-ng >=12
  - openssl >=3.1.1,<4.0a0
  license: BSD-3-Clause-Attribution
  license_family: BSD
  purls: []
  size: 219527
  timestamp: 1690061203707
- conda: https://conda.anaconda.org/conda-forge/linux-64/cython-blis-1.0.1-py310hf462985_0.conda
  sha256: 1c67ea3ff32be83208527e6db5a644eb54a3c46eef1cc4aeeabe4e788a72fe62
  md5: 0542053410ecf3d8ebb0d75a01e88e0f
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - numpy >=1.19,<3
  - python >=3.10,<3.11.0a0
  - python_abi 3.10.* *_cp310
  license: BSD-3-Clause
  license_family: BSD
  purls:
  - pkg:pypi/blis?source=hash-mapping
  size: 5867726
  timestamp: 1729664540713
- conda: https://conda.anaconda.org/conda-forge/noarch/datasets-2.14.4-pyhd8ed1ab_0.conda
  sha256: 7e09bd083a609138b780fcc4535924cb96814d2c908a36d4c64a2ba9ee3efe7f
  md5: 3e087f072ce03c43a9b60522f5d0ca2f
  depends:
  - aiohttp
  - dill >=0.3.0,<0.3.8
  - fsspec >=2021.11.1
  - huggingface_hub >=0.14.0,<1.0.0
  - importlib-metadata
  - multiprocess
  - numpy >=1.17
  - packaging
  - pandas
  - pyarrow >=8.0.0
  - python >=3.8.0
  - python-xxhash
  - pyyaml >=5.1
  - requests >=2.19.0
  - tqdm >=4.62.1
  license: Apache-2.0
  license_family: Apache
  purls:
  - pkg:pypi/datasets?source=hash-mapping
  size: 347303
  timestamp: 1691593908658
- conda: https://conda.anaconda.org/conda-forge/linux-64/dav1d-1.2.1-hd590300_0.conda
  sha256: 22053a5842ca8ee1cf8e1a817138cdb5e647eb2c46979f84153f6ad7bde73020
  md5: 418c6ca5929a611cbd69204907a83995
  depends:
  - libgcc-ng >=12
  license: BSD-2-Clause
  license_family: BSD
  purls: []
  size: 760229
  timestamp: 1685695754230
- conda: https://conda.anaconda.org/conda-forge/linux-64/dbus-1.13.6-h5008d03_3.tar.bz2
  sha256: 8f5f995699a2d9dbdd62c61385bfeeb57c82a681a7c8c5313c395aa0ccab68a5
  md5: ecfff944ba3960ecb334b9a2663d708d
  depends:
  - expat >=2.4.2,<3.0a0
  - libgcc-ng >=9.4.0
  - libglib >=2.70.2,<3.0a0
  license: GPL-2.0-or-later
  license_family: GPL
  purls: []
  size: 618596
  timestamp: 1640112124844
- conda: https://conda.anaconda.org/conda-forge/linux-64/debugpy-1.8.14-py310hf71b8c6_0.conda
  sha256: 532e0ec65d575b1f2b77febff5e357759e4e463118c0b4c01596d954f491bacc
  md5: f684f79f834ebff4917f1fef366e2ca4
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - libstdcxx >=13
  - python >=3.10,<3.11.0a0
  - python_abi 3.10.* *_cp310
  license: MIT
  license_family: MIT
  purls:
  - pkg:pypi/debugpy?source=hash-mapping
  size: 2136145
  timestamp: 1744321455868
- conda: https://conda.anaconda.org/conda-forge/noarch/decorator-5.2.1-pyhd8ed1ab_0.conda
  sha256: c17c6b9937c08ad63cb20a26f403a3234088e57d4455600974a0ce865cb14017
  md5: 9ce473d1d1be1cc3810856a48b3fab32
  depends:
  - python >=3.9
  license: BSD-2-Clause
  license_family: BSD
  purls:
  - pkg:pypi/decorator?source=compressed-mapping
  size: 14129
  timestamp: 1740385067843
- conda: https://conda.anaconda.org/conda-forge/noarch/defusedxml-0.7.1-pyhd8ed1ab_0.tar.bz2
  sha256: 9717a059677553562a8f38ff07f3b9f61727bd614f505658b0a5ecbcf8df89be
  md5: 961b3a227b437d82ad7054484cfa71b2
  depends:
  - python >=3.6
  license: PSF-2.0
  license_family: PSF
  purls:
  - pkg:pypi/defusedxml?source=hash-mapping
  size: 24062
  timestamp: 1615232388757
- conda: https://conda.anaconda.org/conda-forge/noarch/dill-0.3.7-pyhd8ed1ab_0.conda
  sha256: 4ff20c6be028be2825235631c45d9e4a75bca1de65f8840c02dfb28ea0137c45
  md5: 5e4f3466526c52bc9af2d2353a1460bd
  depends:
  - python >=3.7
  license: BSD-3-Clause
  license_family: BSD
  purls:
  - pkg:pypi/dill?source=hash-mapping
  size: 87553
  timestamp: 1690101185422
- pypi: https://files.pythonhosted.org/packages/93/69/e391bd51bc08ed9141ecd899a0ddb61ab6465309f1eb470905c0c8868081/docutils-0.19-py3-none-any.whl
  name: docutils
  version: '0.19'
  sha256: 5e1de4d849fee02c63b040a4a3fd567f4ab104defd8a5511fbbc24a8a017efbc
  requires_python: '>=3.7'
- conda: https://conda.anaconda.org/conda-forge/linux-64/double-conversion-3.3.1-h5888daf_0.conda
  sha256: 1bcc132fbcc13f9ad69da7aa87f60ea41de7ed4d09f3a00ff6e0e70e1c690bc2
  md5: bfd56492d8346d669010eccafe0ba058
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - libstdcxx >=13
  license: BSD-3-Clause
  license_family: BSD
  purls: []
  size: 69544
  timestamp: 1739569648873
- conda: https://conda.anaconda.org/conda-forge/linux-64/epoxy-1.5.10-h166bdaf_1.tar.bz2
  sha256: 1e58ee2ed0f4699be202f23d49b9644b499836230da7dd5b2f63e6766acff89e
  md5: a089d06164afd2d511347d3f87214e0b
  depends:
  - libgcc-ng >=10.3.0
  license: MIT
  license_family: MIT
  purls: []
  size: 1440699
  timestamp: 1648505042260
- conda: https://conda.anaconda.org/conda-forge/noarch/exceptiongroup-1.3.0-pyhd8ed1ab_0.conda
  sha256: ce61f4f99401a4bd455b89909153b40b9c823276aefcbb06f2044618696009ca
  md5: 72e42d28960d875c7654614f8b50939a
  depends:
  - python >=3.9
  - typing_extensions >=4.6.0
  license: MIT and PSF-2.0
  purls:
  - pkg:pypi/exceptiongroup?source=compressed-mapping
  size: 21284
  timestamp: 1746947398083
- conda: https://conda.anaconda.org/conda-forge/noarch/executing-2.2.0-pyhd8ed1ab_0.conda
  sha256: 7510dd93b9848c6257c43fdf9ad22adf62e7aa6da5f12a6a757aed83bcfedf05
  md5: 81d30c08f9a3e556e8ca9e124b044d14
  depends:
  - python >=3.9
  license: MIT
  license_family: MIT
  purls:
  - pkg:pypi/executing?source=hash-mapping
  size: 29652
  timestamp: 1745502200340
- conda: https://conda.anaconda.org/conda-forge/linux-64/expat-2.7.0-h5888daf_0.conda
  sha256: dd5530ddddca93b17318838b97a2c9d7694fa4d57fc676cf0d06da649085e57a
  md5: d6845ae4dea52a2f90178bf1829a21f8
  depends:
  - __glibc >=2.17,<3.0.a0
  - libexpat 2.7.0 h5888daf_0
  - libgcc >=13
  license: MIT
  license_family: MIT
  purls: []
  size: 140050
  timestamp: 1743431809745
- conda: https://conda.anaconda.org/conda-forge/linux-64/fftw-3.3.10-nompi_hf1063bd_110.conda
  sha256: 3cc58c9d9a8cc0089e3839ae5ff7ba4ddfc6df99d5f6a147fe90ea963bc6fe45
  md5: ee3e687b78b778db7b304e5b00a4dca6
  depends:
  - libgcc-ng >=12
  - libgfortran-ng
  - libgfortran5 >=12.3.0
  - libstdcxx-ng >=12
  license: GPL-2.0-or-later
  license_family: GPL
  purls: []
  size: 2075171
  timestamp: 1717757963922
- conda: https://conda.anaconda.org/conda-forge/noarch/filelock-3.18.0-pyhd8ed1ab_0.conda
  sha256: de7b6d4c4f865609ae88db6fa03c8b7544c2452a1aa5451eb7700aad16824570
  md5: 4547b39256e296bb758166893e909a7c
  depends:
  - python >=3.9
  license: Unlicense
  purls:
  - pkg:pypi/filelock?source=hash-mapping
  size: 17887
  timestamp: 1741969612334
- conda: https://conda.anaconda.org/conda-forge/noarch/font-ttf-dejavu-sans-mono-2.37-hab24e00_0.tar.bz2
  sha256: 58d7f40d2940dd0a8aa28651239adbf5613254df0f75789919c4e6762054403b
  md5: 0c96522c6bdaed4b1566d11387caaf45
  license: BSD-3-Clause
  license_family: BSD
  purls: []
  size: 397370
  timestamp: 1566932522327
- conda: https://conda.anaconda.org/conda-forge/noarch/font-ttf-inconsolata-3.000-h77eed37_0.tar.bz2
  sha256: c52a29fdac682c20d252facc50f01e7c2e7ceac52aa9817aaf0bb83f7559ec5c
  md5: 34893075a5c9e55cdafac56607368fc6
  license: OFL-1.1
  license_family: Other
  purls: []
  size: 96530
  timestamp: 1620479909603
- conda: https://conda.anaconda.org/conda-forge/noarch/font-ttf-source-code-pro-2.038-h77eed37_0.tar.bz2
  sha256: 00925c8c055a2275614b4d983e1df637245e19058d79fc7dd1a93b8d9fb4b139
  md5: 4d59c254e01d9cde7957100457e2d5fb
  license: OFL-1.1
  license_family: Other
  purls: []
  size: 700814
  timestamp: 1620479612257
- conda: https://conda.anaconda.org/conda-forge/noarch/font-ttf-ubuntu-0.83-h77eed37_3.conda
  sha256: 2821ec1dc454bd8b9a31d0ed22a7ce22422c0aef163c59f49dfdf915d0f0ca14
  md5: 49023d73832ef61042f6a237cb2687e7
  license: LicenseRef-Ubuntu-Font-Licence-Version-1.0
  license_family: Other
  purls: []
  size: 1620504
  timestamp: 1727511233259
- conda: https://conda.anaconda.org/conda-forge/linux-64/fontconfig-2.15.0-h7e30c49_1.conda
  sha256: 7093aa19d6df5ccb6ca50329ef8510c6acb6b0d8001191909397368b65b02113
  md5: 8f5b0b297b59e1ac160ad4beec99dbee
  depends:
  - __glibc >=2.17,<3.0.a0
  - freetype >=2.12.1,<3.0a0
  - libexpat >=2.6.3,<3.0a0
  - libgcc >=13
  - libuuid >=2.38.1,<3.0a0
  - libzlib >=1.3.1,<2.0a0
  license: MIT
  license_family: MIT
  purls: []
  size: 265599
  timestamp: 1730283881107
- conda: https://conda.anaconda.org/conda-forge/noarch/fonts-conda-ecosystem-1-0.tar.bz2
  sha256: a997f2f1921bb9c9d76e6fa2f6b408b7fa549edd349a77639c9fe7a23ea93e61
  md5: fee5683a3f04bd15cbd8318b096a27ab
  depends:
  - fonts-conda-forge
  license: BSD-3-Clause
  license_family: BSD
  purls: []
  size: 3667
  timestamp: 1566974674465
- conda: https://conda.anaconda.org/conda-forge/noarch/fonts-conda-forge-1-0.tar.bz2
  sha256: 53f23a3319466053818540bcdf2091f253cbdbab1e0e9ae7b9e509dcaa2a5e38
  md5: f766549260d6815b0c52253f1fb1bb29
  depends:
  - font-ttf-dejavu-sans-mono
  - font-ttf-inconsolata
  - font-ttf-source-code-pro
  - font-ttf-ubuntu
  license: BSD-3-Clause
  license_family: BSD
  purls: []
  size: 4102
  timestamp: 1566932280397
- conda: https://conda.anaconda.org/conda-forge/linux-64/fonttools-4.58.0-py310h89163eb_0.conda
  sha256: 35df95fab1d44133d8e041a7ce8324e18187fa5d95fa3c2a6074253df8e3aeec
  md5: 4532df8a45ab4e37b2cc71186304dd5a
  depends:
  - __glibc >=2.17,<3.0.a0
  - brotli
  - libgcc >=13
  - munkres
  - python >=3.10,<3.11.0a0
  - python_abi 3.10.* *_cp310
  - unicodedata2 >=15.1.0
  license: MIT
  license_family: MIT
  purls:
  - pkg:pypi/fonttools?source=hash-mapping
  size: 2345597
  timestamp: 1746913894737
- conda: https://conda.anaconda.org/conda-forge/noarch/fqdn-1.5.1-pyhd8ed1ab_1.conda
  sha256: 2509992ec2fd38ab27c7cdb42cf6cadc566a1cc0d1021a2673475d9fa87c6276
  md5: d3549fd50d450b6d9e7dddff25dd2110
  depends:
  - cached-property >=1.3.0
  - python >=3.9,<4
  license: MPL-2.0
  license_family: MOZILLA
  purls:
  - pkg:pypi/fqdn?source=hash-mapping
  size: 16705
  timestamp: 1733327494780
- conda: https://conda.anaconda.org/conda-forge/linux-64/freeglut-3.2.2-ha6d2627_3.conda
  sha256: 676540a8e7f73a894cb1fcb870e7bec623ec1c0a2d277094fd713261a02d8d56
  md5: 84ec3f5b46f3076be49f2cf3f1cfbf02
  depends:
  - libgcc-ng >=12
  - libstdcxx-ng >=12
  - libxcb >=1.16,<2.0.0a0
  - xorg-libx11 >=1.8.9,<2.0a0
  - xorg-libxau >=1.0.11,<2.0a0
  - xorg-libxext >=1.3.4,<2.0a0
  - xorg-libxfixes
  - xorg-libxi
  license: MIT
  license_family: MIT
  purls: []
  size: 144010
  timestamp: 1719014356708
- conda: https://conda.anaconda.org/conda-forge/linux-64/freetype-2.13.3-ha770c72_1.conda
  sha256: 7ef7d477c43c12a5b4cddcf048a83277414512d1116aba62ebadfa7056a7d84f
  md5: 9ccd736d31e0c6e41f54e704e5312811
  depends:
  - libfreetype 2.13.3 ha770c72_1
  - libfreetype6 2.13.3 h48d6fc4_1
  license: GPL-2.0-only OR FTL
  purls: []
  size: 172450
  timestamp: 1745369996765
- conda: https://conda.anaconda.org/conda-forge/linux-64/fribidi-1.0.10-h36c2ea0_0.tar.bz2
  sha256: 5d7b6c0ee7743ba41399e9e05a58ccc1cfc903942e49ff6f677f6e423ea7a627
  md5: ac7bc6a654f8f41b352b38f4051135f8
  depends:
  - libgcc-ng >=7.5.0
  license: LGPL-2.1
  purls: []
  size: 114383
  timestamp: 1604416621168
- conda: https://conda.anaconda.org/conda-forge/linux-64/frozenlist-1.6.0-py310h0a1d07c_0.conda
  sha256: 9a400018d56e6a715fc7bd2a9c8ae5a9e2d2de3dc44670d142fa7b83c195e9a4
  md5: 114981520070707ed653ff7889acf344
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - libstdcxx >=13
  - python >=3.10,<3.11.0a0
  - python_abi 3.10.* *_cp310
  license: Apache-2.0
  license_family: APACHE
  purls:
  - pkg:pypi/frozenlist?source=hash-mapping
  size: 113049
  timestamp: 1746635456258
- conda: https://conda.anaconda.org/conda-forge/noarch/fsspec-2025.5.0-pyhd8ed1ab_0.conda
  sha256: 6c907128b6464b8f4d5cba3160c7ec1505d10a86c188b1356ecddfd662285fcd
  md5: 7ac28047cd73cf02a294a64f036b2b02
  depends:
  - python >=3.9
  license: BSD-3-Clause
  license_family: BSD
  purls:
  - pkg:pypi/fsspec?source=compressed-mapping
  size: 143834
  timestamp: 1747831490695
- conda: https://conda.anaconda.org/conda-forge/linux-64/gdk-pixbuf-2.42.12-hb9ae30d_0.conda
  sha256: d5283b95a8d49dcd88d29b360d8b38694aaa905d968d156d72ab71d32b38facb
  md5: 201db6c2d9a3c5e46573ac4cb2e92f4f
  depends:
  - libgcc-ng >=12
  - libglib >=2.80.2,<3.0a0
  - libjpeg-turbo >=3.0.0,<4.0a0
  - libpng >=1.6.43,<1.7.0a0
  - libtiff >=4.6.0,<4.8.0a0
  license: LGPL-2.1-or-later
  license_family: LGPL
  purls: []
  size: 528149
  timestamp: 1715782983957
- conda: https://conda.anaconda.org/conda-forge/linux-64/gensim-4.3.3-py310h27b3328_0.conda
  sha256: 8e834844d9298bb2d761cb6d25758c4e0c9d9d9e3799b35fc7ed6a13d643d4ed
  md5: 1a8bc3fcb09ae028104c81519f71af2f
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=12
  - libstdcxx >=12
  - numpy >=1.22.4,<2.0a0
  - python >=3.10,<3.11.0a0
  - python_abi 3.10.* *_cp310
  - scipy >=0.18.1
  - six >=1.5.0
  - smart_open >=1.8.1
  license: LGPL-2.1-only
  purls:
  - pkg:pypi/gensim?source=hash-mapping
  size: 22668796
  timestamp: 1725378852675
- conda: https://conda.anaconda.org/conda-forge/linux-64/gflags-2.2.2-h5888daf_1005.conda
  sha256: 6c33bf0c4d8f418546ba9c250db4e4221040936aef8956353bc764d4877bc39a
  md5: d411fc29e338efb48c5fd4576d71d881
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - libstdcxx >=13
  license: BSD-3-Clause
  license_family: BSD
  purls: []
  size: 119654
  timestamp: 1726600001928
- conda: https://conda.anaconda.org/conda-forge/linux-64/ghostscript-10.04.0-h5888daf_0.conda
  sha256: 22b8a28f8590f29c53f78dec12ab9998cc8f83e4df8465d21a70157af921f82d
  md5: 3b8d7a2df810ad5109a51472b23dbd8e
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - libstdcxx >=13
  license: AGPL-3.0-only
  license_family: AGPL
  purls: []
  size: 61199016
  timestamp: 1726698984507
- conda: https://conda.anaconda.org/conda-forge/linux-64/giflib-5.2.2-hd590300_0.conda
  sha256: aac402a8298f0c0cc528664249170372ef6b37ac39fdc92b40601a6aed1e32ff
  md5: 3bf7b9fd5a7136126e0234db4b87c8b6
  depends:
  - libgcc-ng >=12
  license: MIT
  license_family: MIT
  purls: []
  size: 77248
  timestamp: 1712692454246
- conda: https://conda.anaconda.org/conda-forge/linux-64/glib-tools-2.84.1-h4833e2c_0.conda
  sha256: 0358e0471a7c41875490abb87faa44c38298899b625744c6618b32cfb6595b4c
  md5: ddc06964296eee2b4070e65415b332fd
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - libglib 2.84.1 h2ff4ddf_0
  license: LGPL-2.1-or-later
  purls: []
  size: 116281
  timestamp: 1743773813311
- conda: https://conda.anaconda.org/conda-forge/linux-64/glog-0.7.1-hbabe93e_0.conda
  sha256: dc824dc1d0aa358e28da2ecbbb9f03d932d976c8dca11214aa1dcdfcbd054ba2
  md5: ff862eebdfeb2fd048ae9dc92510baca
  depends:
  - gflags >=2.2.2,<2.3.0a0
  - libgcc-ng >=12
  - libstdcxx-ng >=12
  license: BSD-3-Clause
  license_family: BSD
  purls: []
  size: 143452
  timestamp: 1718284177264
- conda: https://conda.anaconda.org/conda-forge/linux-64/gmp-6.3.0-hac33072_2.conda
  sha256: 309cf4f04fec0c31b6771a5809a1909b4b3154a2208f52351e1ada006f4c750c
  md5: c94a5994ef49749880a8139cf9afcbe1
  depends:
  - libgcc-ng >=12
  - libstdcxx-ng >=12
  license: GPL-2.0-or-later OR LGPL-3.0-or-later
  purls: []
  size: 460055
  timestamp: 1718980856608
- conda: https://conda.anaconda.org/conda-forge/linux-64/gmpy2-2.2.1-py310he8512ff_0.conda
  sha256: ea27ef97976eb0d709e4ef296f8ce83d7775ea56833cdbef107b42ef39867276
  md5: 2086c92c9e98a12acfc287412c18f2e8
  depends:
  - __glibc >=2.17,<3.0.a0
  - gmp >=6.3.0,<7.0a0
  - libgcc >=13
  - mpc >=1.3.1,<2.0a0
  - mpfr >=4.2.1,<5.0a0
  - python >=3.10,<3.11.0a0
  - python_abi 3.10.* *_cp310
  license: LGPL-3.0-or-later
  license_family: LGPL
  purls:
  - pkg:pypi/gmpy2?source=hash-mapping
  size: 203183
  timestamp: 1745509500381
- conda: https://conda.anaconda.org/conda-forge/linux-64/graphite2-1.3.13-h59595ed_1003.conda
  sha256: 0595b009f20f8f60f13a6398e7cdcbd2acea5f986633adcf85f5a2283c992add
  md5: f87c7b7c2cb45f323ffbce941c78ab7c
  depends:
  - libgcc-ng >=12
  - libstdcxx-ng >=12
  license: LGPL-2.0-or-later
  license_family: LGPL
  purls: []
  size: 96855
  timestamp: 1711634169756
- conda: https://conda.anaconda.org/conda-forge/linux-64/graphviz-12.2.1-h5ae0cbf_1.conda
  sha256: e6866409ba03df392ac5ec6f0d6ff9751a685ed917bfbcd8a73f550c5fe83c2b
  md5: df7835d2c73cd1889d377cfd6694ada4
  depends:
  - __glibc >=2.17,<3.0.a0
  - adwaita-icon-theme
  - cairo >=1.18.2,<2.0a0
  - fonts-conda-ecosystem
  - gdk-pixbuf >=2.42.12,<3.0a0
  - gtk3 >=3.24.43,<4.0a0
  - gts >=0.7.6,<0.8.0a0
  - libexpat >=2.6.4,<3.0a0
  - libgcc >=13
  - libgd >=2.3.3,<2.4.0a0
  - libglib >=2.82.2,<3.0a0
  - librsvg >=2.58.4,<3.0a0
  - libstdcxx >=13
  - libwebp-base >=1.5.0,<2.0a0
  - libzlib >=1.3.1,<2.0a0
  - pango >=1.56.1,<2.0a0
  license: EPL-1.0
  license_family: Other
  purls: []
  size: 2413095
  timestamp: 1738602910851
- pypi: https://files.pythonhosted.org/packages/c2/53/01e0bccd619fa19e287e340688e4c2f47cd0d160b32073dfc75d12130931/grobid_client_python-0.0.9-py3-none-any.whl
  name: grobid-client-python
  version: 0.0.9
  sha256: abcb7c712c513ba6c593a6e6624613d049642a13521b623b962ec88d01806103
  requires_dist:
  - requests
- conda: https://conda.anaconda.org/conda-forge/linux-64/gtk3-3.24.43-h021d004_4.conda
  sha256: fc8abccb4b0d454891847bdd8163332ff8607aa33ea9cf1e43b3828fc88c42ce
  md5: a891e341072432fafb853b3762957cbf
  depends:
  - __glibc >=2.17,<3.0.a0
  - at-spi2-atk >=2.38.0,<3.0a0
  - atk-1.0 >=2.38.0
  - cairo >=1.18.4,<2.0a0
  - epoxy >=1.5.10,<1.6.0a0
  - fontconfig >=2.15.0,<3.0a0
  - fonts-conda-ecosystem
  - fribidi >=1.0.10,<2.0a0
  - gdk-pixbuf >=2.42.12,<3.0a0
  - glib-tools
  - harfbuzz >=10.4.0,<11.0a0
  - hicolor-icon-theme
  - libcups >=2.3.3,<2.4.0a0
  - libcups >=2.3.3,<3.0a0
  - libexpat >=2.6.4,<3.0a0
  - libgcc >=13
  - libglib >=2.82.2,<3.0a0
  - liblzma >=5.6.4,<6.0a0
  - libxkbcommon >=1.8.0,<2.0a0
  - libzlib >=1.3.1,<2.0a0
  - pango >=1.56.1,<2.0a0
  - wayland >=1.23.1,<2.0a0
  - xorg-libx11 >=1.8.11,<2.0a0
  - xorg-libxcomposite >=0.4.6,<1.0a0
  - xorg-libxcursor >=1.2.3,<2.0a0
  - xorg-libxdamage >=1.1.6,<2.0a0
  - xorg-libxext >=1.3.6,<2.0a0
  - xorg-libxfixes >=6.0.1,<7.0a0
  - xorg-libxi >=1.8.2,<2.0a0
  - xorg-libxinerama >=1.1.5,<1.2.0a0
  - xorg-libxrandr >=1.5.4,<2.0a0
  - xorg-libxrender >=0.9.12,<0.10.0a0
  license: LGPL-2.0-or-later
  license_family: LGPL
  purls: []
  size: 5563940
  timestamp: 1741694746664
- conda: https://conda.anaconda.org/conda-forge/linux-64/gts-0.7.6-h977cf35_4.conda
  sha256: b5cd16262fefb836f69dc26d879b6508d29f8a5c5948a966c47fe99e2e19c99b
  md5: 4d8df0b0db060d33c9a702ada998a8fe
  depends:
  - libgcc-ng >=12
  - libglib >=2.76.3,<3.0a0
  - libstdcxx-ng >=12
  license: LGPL-2.0-or-later
  license_family: LGPL
  purls: []
  size: 318312
  timestamp: 1686545244763
- conda: https://conda.anaconda.org/conda-forge/noarch/h11-0.16.0-pyhd8ed1ab_0.conda
  sha256: f64b68148c478c3bfc8f8d519541de7d2616bf59d44485a5271041d40c061887
  md5: 4b69232755285701bc86a5afe4d9933a
  depends:
  - python >=3.9
  - typing_extensions
  license: MIT
  license_family: MIT
  purls:
  - pkg:pypi/h11?source=hash-mapping
  size: 37697
  timestamp: 1745526482242
- conda: https://conda.anaconda.org/conda-forge/noarch/h2-4.2.0-pyhd8ed1ab_0.conda
  sha256: 0aa1cdc67a9fe75ea95b5644b734a756200d6ec9d0dff66530aec3d1c1e9df75
  md5: b4754fb1bdcb70c8fd54f918301582c6
  depends:
  - hpack >=4.1,<5
  - hyperframe >=6.1,<7
  - python >=3.9
  license: MIT
  license_family: MIT
  purls:
  - pkg:pypi/h2?source=hash-mapping
  size: 53888
  timestamp: 1738578623567
- conda: https://conda.anaconda.org/conda-forge/linux-64/harfbuzz-10.4.0-h76408a6_0.conda
  sha256: 3b4ccabf170e1bf98c593f724cc4defe286d64cb19288751a50c63809ca32d5f
  md5: 81f137b4153cf111ff8e3188b6fb8e73
  depends:
  - __glibc >=2.17,<3.0.a0
  - cairo >=1.18.2,<2.0a0
  - freetype >=2.12.1,<3.0a0
  - graphite2
  - icu >=75.1,<76.0a0
  - libexpat >=2.6.4,<3.0a0
  - libgcc >=13
  - libglib >=2.82.2,<3.0a0
  - libstdcxx >=13
  - libzlib >=1.3.1,<2.0a0
  license: MIT
  license_family: MIT
  purls: []
  size: 1694183
  timestamp: 1741016164622
- conda: https://conda.anaconda.org/conda-forge/linux-64/hicolor-icon-theme-0.17-ha770c72_2.tar.bz2
  sha256: 336f29ceea9594f15cc8ec4c45fdc29e10796573c697ee0d57ebb7edd7e92043
  md5: bbf6f174dcd3254e19a2f5d2295ce808
  license: GPL-2.0-or-later
  license_family: GPL
  purls: []
  size: 13841
  timestamp: 1605162808667
- conda: https://conda.anaconda.org/conda-forge/noarch/hpack-4.1.0-pyhd8ed1ab_0.conda
  sha256: 6ad78a180576c706aabeb5b4c8ceb97c0cb25f1e112d76495bff23e3779948ba
  md5: 0a802cb9888dd14eeefc611f05c40b6e
  depends:
  - python >=3.9
  license: MIT
  license_family: MIT
  purls:
  - pkg:pypi/hpack?source=hash-mapping
  size: 30731
  timestamp: 1737618390337
- conda: https://conda.anaconda.org/conda-forge/noarch/httpcore-1.0.9-pyh29332c3_0.conda
  sha256: 04d49cb3c42714ce533a8553986e1642d0549a05dc5cc48e0d43ff5be6679a5b
  md5: 4f14640d58e2cc0aa0819d9d8ba125bb
  depends:
  - python >=3.9
  - h11 >=0.16
  - h2 >=3,<5
  - sniffio 1.*
  - anyio >=4.0,<5.0
  - certifi
  - python
  license: BSD-3-Clause
  license_family: BSD
  purls:
  - pkg:pypi/httpcore?source=hash-mapping
  size: 49483
  timestamp: 1745602916758
- conda: https://conda.anaconda.org/conda-forge/noarch/httpx-0.28.1-pyhd8ed1ab_0.conda
  sha256: cd0f1de3697b252df95f98383e9edb1d00386bfdd03fdf607fa42fe5fcb09950
  md5: d6989ead454181f4f9bc987d3dc4e285
  depends:
  - anyio
  - certifi
  - httpcore 1.*
  - idna
  - python >=3.9
  license: BSD-3-Clause
  license_family: BSD
  purls:
  - pkg:pypi/httpx?source=hash-mapping
  size: 63082
  timestamp: 1733663449209
- conda: https://conda.anaconda.org/conda-forge/noarch/huggingface_hub-0.31.4-pyhd8ed1ab_0.conda
  sha256: c472d3f3607a313e75ee45501455b69d1124ac9f00b2bdb4983048da19c6d375
  md5: d92d502ff13c190010694d64375959b9
  depends:
  - filelock
  - fsspec >=2023.5.0
  - packaging >=20.9
  - python >=3.9
  - pyyaml >=5.1
  - requests
  - tqdm >=4.42.1
  - typing-extensions >=3.7.4.3
  - typing_extensions >=3.7.4.3
  license: Apache-2.0
  license_family: APACHE
  purls:
  - pkg:pypi/huggingface-hub?source=hash-mapping
  size: 302452
  timestamp: 1747670941134
- conda: https://conda.anaconda.org/conda-forge/noarch/hyperframe-6.1.0-pyhd8ed1ab_0.conda
  sha256: 77af6f5fe8b62ca07d09ac60127a30d9069fdc3c68d6b256754d0ffb1f7779f8
  md5: 8e6923fc12f1fe8f8c4e5c9f343256ac
  depends:
  - python >=3.9
  license: MIT
  license_family: MIT
  purls:
  - pkg:pypi/hyperframe?source=hash-mapping
  size: 17397
  timestamp: 1737618427549
- conda: https://conda.anaconda.org/conda-forge/linux-64/icu-75.1-he02047a_0.conda
  sha256: 71e750d509f5fa3421087ba88ef9a7b9be11c53174af3aa4d06aff4c18b38e8e
  md5: 8b189310083baabfb622af68fd9d3ae3
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc-ng >=12
  - libstdcxx-ng >=12
  license: MIT
  license_family: MIT
  purls: []
  size: 12129203
  timestamp: 1720853576813
- conda: https://conda.anaconda.org/conda-forge/noarch/idna-3.10-pyhd8ed1ab_1.conda
  sha256: d7a472c9fd479e2e8dcb83fb8d433fce971ea369d704ece380e876f9c3494e87
  md5: 39a4f67be3286c86d696df570b1201b7
  depends:
  - python >=3.9
  license: BSD-3-Clause
  license_family: BSD
  purls:
  - pkg:pypi/idna?source=hash-mapping
  size: 49765
  timestamp: 1733211921194
- conda: https://conda.anaconda.org/conda-forge/linux-64/imagemagick-7.1.1_47-imagemagick_hf2058d9_0.conda
  sha256: 6c6d46c8290a4cf22f39219213ac8893a905932fab07dd88e3dbdc32bc1311be
  md5: b15eadaef9c401c5c93f8dca5c893f0b
  depends:
  - __glibc >=2.17,<3.0.a0
  - bzip2 >=1.0.8,<2.0a0
  - fftw >=3.3.10,<4.0a0
  - fontconfig >=2.15.0,<3.0a0
  - fonts-conda-ecosystem
  - fonts-conda-forge
  - freetype >=2.13.3,<3.0a0
  - ghostscript
  - giflib >=5.2.2,<5.3.0a0
  - graphviz >=12.2.1,<13.0a0
  - jbig
  - libgcc >=13
  - libglib >=2.84.0,<3.0a0
  - libheif >=1.19.7,<1.20.0a0
  - libjpeg-turbo >=3.0.0,<4.0a0
  - liblzma >=5.6.4,<6.0a0
  - liblzma-devel
  - libpng >=1.6.47,<1.7.0a0
  - librsvg >=2.58.4,<3.0a0
  - libstdcxx >=13
  - libtiff >=4.7.0,<4.8.0a0
  - libwebp
  - libwebp-base >=1.5.0,<2.0a0
  - libxml2 >=2.13.7,<2.14.0a0
  - libzlib >=1.3.1,<2.0a0
  - openjpeg >=2.5.3,<3.0a0
  - pango >=1.56.3,<2.0a0
  - pkg-config
  - xorg-libx11 >=1.8.12,<2.0a0
  - xorg-libxext >=1.3.6,<2.0a0
  - xorg-libxrender >=0.9.12,<0.10.0a0
  - xorg-libxt >=1.3.1,<2.0a0
  - zlib
  license: ImageMagick
  license_family: Apache
  purls: []
  size: 2298007
  timestamp: 1743332583794
- conda: https://conda.anaconda.org/conda-forge/noarch/importlib-metadata-8.7.0-pyhe01879c_1.conda
  sha256: c18ab120a0613ada4391b15981d86ff777b5690ca461ea7e9e49531e8f374745
  md5: 63ccfdc3a3ce25b027b8767eb722fca8
  depends:
  - python >=3.9
  - zipp >=3.20
  - python
  license: Apache-2.0
  purls:
  - pkg:pypi/importlib-metadata?source=compressed-mapping
  size: 34641
  timestamp: 1747934053147
- conda: https://conda.anaconda.org/conda-forge/noarch/importlib_resources-6.5.2-pyhd8ed1ab_0.conda
  sha256: acc1d991837c0afb67c75b77fdc72b4bf022aac71fedd8b9ea45918ac9b08a80
  md5: c85c76dc67d75619a92f51dfbce06992
  depends:
  - python >=3.9
  - zipp >=3.1.0
  constrains:
  - importlib-resources >=6.5.2,<6.5.3.0a0
  license: Apache-2.0
  license_family: APACHE
  purls:
  - pkg:pypi/importlib-resources?source=hash-mapping
  size: 33781
  timestamp: 1736252433366
- conda: https://conda.anaconda.org/conda-forge/noarch/iniconfig-2.0.0-pyhd8ed1ab_1.conda
  sha256: 0ec8f4d02053cd03b0f3e63168316530949484f80e16f5e2fb199a1d117a89ca
  md5: 6837f3eff7dcea42ecd714ce1ac2b108
  depends:
  - python >=3.9
  license: MIT
  license_family: MIT
  purls:
  - pkg:pypi/iniconfig?source=hash-mapping
  size: 11474
  timestamp: 1733223232820
- conda: https://conda.anaconda.org/conda-forge/noarch/ipykernel-6.29.5-pyh3099207_0.conda
  sha256: 33cfd339bb4efac56edf93474b37ddc049e08b1b4930cf036c893cc1f5a1f32a
  md5: b40131ab6a36ac2c09b7c57d4d3fbf99
  depends:
  - __linux
  - comm >=0.1.1
  - debugpy >=1.6.5
  - ipython >=7.23.1
  - jupyter_client >=6.1.12
  - jupyter_core >=4.12,!=5.0.*
  - matplotlib-inline >=0.1
  - nest-asyncio
  - packaging
  - psutil
  - python >=3.8
  - pyzmq >=24
  - tornado >=6.1
  - traitlets >=5.4.0
  license: BSD-3-Clause
  license_family: BSD
  purls:
  - pkg:pypi/ipykernel?source=hash-mapping
  size: 119084
  timestamp: 1719845605084
- conda: https://conda.anaconda.org/conda-forge/noarch/ipython-8.36.0-pyh907856f_0.conda
  sha256: 21e33e5c779227df52d443bf17e3f470c295a5b2ede5501e5e8eb90b9747f82e
  md5: 886e40ae1c3851b6d348d4cd41e5de39
  depends:
  - __unix
  - pexpect >4.3
  - decorator
  - exceptiongroup
  - jedi >=0.16
  - matplotlib-inline
  - pickleshare
  - prompt-toolkit >=3.0.41,<3.1.0
  - pygments >=2.4.0
  - python >=3.10
  - stack_data
  - traitlets >=5.13.0
  - typing_extensions >=4.6
  - python
  license: BSD-3-Clause
  license_family: BSD
  purls:
  - pkg:pypi/ipython?source=hash-mapping
  size: 638204
  timestamp: 1745673194404
- conda: https://conda.anaconda.org/conda-forge/noarch/ipywidgets-8.1.7-pyhd8ed1ab_0.conda
  sha256: fd496e7d48403246f534c5eec09fc1e63ac7beb1fa06541d6ba71f56b30cf29b
  md5: 7c9449eac5975ef2d7753da262a72707
  depends:
  - comm >=0.1.3
  - ipython >=6.1.0
  - jupyterlab_widgets >=3.0.15,<3.1.0
  - python >=3.9
  - traitlets >=4.3.1
  - widgetsnbextension >=4.0.14,<4.1.0
  license: BSD-3-Clause
  license_family: BSD
  purls:
  - pkg:pypi/ipywidgets?source=compressed-mapping
  size: 114557
  timestamp: 1746454722402
- conda: https://conda.anaconda.org/conda-forge/noarch/isoduration-20.11.0-pyhd8ed1ab_1.conda
  sha256: 08e838d29c134a7684bca0468401d26840f41c92267c4126d7b43a6b533b0aed
  md5: 0b0154421989637d424ccf0f104be51a
  depends:
  - arrow >=0.15.0
  - python >=3.9
  license: MIT
  license_family: MIT
  purls:
  - pkg:pypi/isoduration?source=hash-mapping
  size: 19832
  timestamp: 1733493720346
- conda: https://conda.anaconda.org/conda-forge/linux-64/jbig-2.1-h7f98852_2003.tar.bz2
  sha256: 5b188856e0fc31c516729f4a33fed112ab59c37f6a168ccc6e74b791df828996
  md5: 1aa0cee79792fa97b7ff4545110b60bf
  depends:
  - libgcc-ng >=9.3.0
  license: GPL-2.0-or-later
  license_family: GPL
  purls: []
  size: 44443
  timestamp: 1621514853343
- conda: https://conda.anaconda.org/conda-forge/linux-64/jbig2dec-0.18-h267a509_0.conda
  sha256: 8c102210fd48207cc85d34873947f4c103461f4ca1fa0e0b055a89dadfda0b58
  md5: 737b91508b88a4bf30644c72258ca76d
  depends:
  - libgcc-ng >=12
  - libpng >=1.6.43,<1.7.0a0
  - libzlib >=1.2.13,<2.0.0a0
  license: AGPL-3.0-only
  license_family: AGPL
  purls: []
  size: 101640
  timestamp: 1712756083787
- conda: https://conda.anaconda.org/conda-forge/noarch/jedi-0.19.2-pyhd8ed1ab_1.conda
  sha256: 92c4d217e2dc68983f724aa983cca5464dcb929c566627b26a2511159667dba8
  md5: a4f4c5dc9b80bc50e0d3dc4e6e8f1bd9
  depends:
  - parso >=0.8.3,<0.9.0
  - python >=3.9
  license: Apache-2.0 AND MIT
  purls:
  - pkg:pypi/jedi?source=hash-mapping
  size: 843646
  timestamp: 1733300981994
- conda: https://conda.anaconda.org/conda-forge/noarch/jinja2-3.1.6-pyhd8ed1ab_0.conda
  sha256: f1ac18b11637ddadc05642e8185a851c7fab5998c6f5470d716812fae943b2af
  md5: 446bd6c8cb26050d528881df495ce646
  depends:
  - markupsafe >=2.0
  - python >=3.9
  license: BSD-3-Clause
  license_family: BSD
  purls:
  - pkg:pypi/jinja2?source=compressed-mapping
  size: 112714
  timestamp: 1741263433881
- pypi: https://files.pythonhosted.org/packages/31/b4/b9b800c45527aadd64d5b442f9b932b00648617eb5d63d2c7a6587b7cafc/jmespath-1.0.1-py3-none-any.whl
  name: jmespath
  version: 1.0.1
  sha256: 02e2e4cc71b5bcab88332eebf907519190dd9e6e82107fa7f83b1003a6252980
  requires_python: '>=3.7'
- conda: https://conda.anaconda.org/conda-forge/noarch/joblib-1.5.1-pyhd8ed1ab_0.conda
  sha256: e5a4eca9a5d8adfaa3d51e24eefd1a6d560cb3b33a7e1eee13e410bec457b7ed
  md5: fb1c14694de51a476ce8636d92b6f42c
  depends:
  - python >=3.9
  - setuptools
  license: BSD-3-Clause
  purls:
  - pkg:pypi/joblib?source=compressed-mapping
  size: 224437
  timestamp: 1748019237972
- conda: https://conda.anaconda.org/conda-forge/noarch/json5-0.12.0-pyhd8ed1ab_0.conda
  sha256: 889e2a49de796475b5a4bc57d0ba7f4606b368ee2098e353a6d9a14b0e2c6393
  md5: 56275442557b3b45752c10980abfe2db
  depends:
  - python >=3.9
  license: Apache-2.0
  license_family: APACHE
  purls:
  - pkg:pypi/json5?source=hash-mapping
  size: 34114
  timestamp: 1743722170015
- conda: https://conda.anaconda.org/conda-forge/noarch/jsonpickle-4.1.0-pyhe01879c_0.conda
  sha256: 562a3fe134cd140148ef95fbf96f70d262e6416687e55be312a1159df9b83f56
  md5: b1a3d8f03c62f9d1fde80d80408d884a
  depends:
  - python >=3.9
  - python
  license: BSD-3-Clause
  license_family: BSD
  purls:
  - pkg:pypi/jsonpickle?source=hash-mapping
  size: 46186
  timestamp: 1747914109963
- conda: https://conda.anaconda.org/conda-forge/linux-64/jsonpointer-3.0.0-py310hff52083_1.conda
  sha256: ac8e92806a5017740b9a1113f0cab8559cd33884867ec7e99b556eb2fa847690
  md5: ce614a01b0aee1b29cee13d606bcb5d5
  depends:
  - python >=3.10,<3.11.0a0
  - python_abi 3.10.* *_cp310
  license: BSD-3-Clause
  license_family: BSD
  purls:
  - pkg:pypi/jsonpointer?source=hash-mapping
  size: 15658
  timestamp: 1725302992487
- conda: https://conda.anaconda.org/conda-forge/noarch/jsonschema-4.23.0-pyhd8ed1ab_1.conda
  sha256: be992a99e589146f229c58fe5083e0b60551d774511c494f91fe011931bd7893
  md5: a3cead9264b331b32fe8f0aabc967522
  depends:
  - attrs >=22.2.0
  - importlib_resources >=1.4.0
  - jsonschema-specifications >=2023.03.6
  - pkgutil-resolve-name >=1.3.10
  - python >=3.9
  - referencing >=0.28.4
  - rpds-py >=0.7.1
  license: MIT
  license_family: MIT
  purls:
  - pkg:pypi/jsonschema?source=hash-mapping
  size: 74256
  timestamp: 1733472818764
- conda: https://conda.anaconda.org/conda-forge/noarch/jsonschema-specifications-2025.4.1-pyh29332c3_0.conda
  sha256: 66fbad7480f163509deec8bd028cd3ea68e58022982c838683586829f63f3efa
  md5: 41ff526b1083fde51fbdc93f29282e0e
  depends:
  - python >=3.9
  - referencing >=0.31.0
  - python
  license: MIT
  license_family: MIT
  purls:
  - pkg:pypi/jsonschema-specifications?source=hash-mapping
  size: 19168
  timestamp: 1745424244298
- conda: https://conda.anaconda.org/conda-forge/noarch/jsonschema-with-format-nongpl-4.23.0-hd8ed1ab_1.conda
  sha256: 6e0184530011961a0802fda100ecdfd4b0eca634ed94c37e553b72e21c26627d
  md5: a5b1a8065857cc4bd8b7a38d063bb728
  depends:
  - fqdn
  - idna
  - isoduration
  - jsonpointer >1.13
  - jsonschema >=4.23.0,<4.23.1.0a0
  - rfc3339-validator
  - rfc3986-validator >0.1.0
  - uri-template
  - webcolors >=24.6.0
  license: MIT
  license_family: MIT
  purls: []
  size: 7135
  timestamp: 1733472820035
- conda: https://conda.anaconda.org/conda-forge/noarch/jupyter-1.1.1-pyhd8ed1ab_1.conda
  sha256: b538e15067d05768d1c0532a6d9b0625922a1cce751dd6a2af04f7233a1a70e9
  md5: 9453512288d20847de4356327d0e1282
  depends:
  - ipykernel
  - ipywidgets
  - jupyter_console
  - jupyterlab
  - nbconvert-core
  - notebook
  - python >=3.9
  license: BSD-3-Clause
  license_family: BSD
  purls:
  - pkg:pypi/jupyter?source=hash-mapping
  size: 8891
  timestamp: 1733818677113
- conda: https://conda.anaconda.org/conda-forge/noarch/jupyter-lsp-2.2.5-pyhd8ed1ab_1.conda
  sha256: 1565c8b1423a37fca00fe0ab2a17cd8992c2ecf23e7867a1c9f6f86a9831c196
  md5: 0b4c3908e5a38ea22ebb98ee5888c768
  depends:
  - importlib-metadata >=4.8.3
  - jupyter_server >=1.1.2
  - python >=3.9
  license: BSD-3-Clause
  license_family: BSD
  purls:
  - pkg:pypi/jupyter-lsp?source=hash-mapping
  size: 55221
  timestamp: 1733493006611
- conda: https://conda.anaconda.org/conda-forge/noarch/jupyter_client-8.6.3-pyhd8ed1ab_1.conda
  sha256: 19d8bd5bb2fde910ec59e081eeb59529491995ce0d653a5209366611023a0b3a
  md5: 4ebae00eae9705b0c3d6d1018a81d047
  depends:
  - importlib-metadata >=4.8.3
  - jupyter_core >=4.12,!=5.0.*
  - python >=3.9
  - python-dateutil >=2.8.2
  - pyzmq >=23.0
  - tornado >=6.2
  - traitlets >=5.3
  license: BSD-3-Clause
  license_family: BSD
  purls:
  - pkg:pypi/jupyter-client?source=hash-mapping
  size: 106342
  timestamp: 1733441040958
- conda: https://conda.anaconda.org/conda-forge/noarch/jupyter_console-6.6.3-pyhd8ed1ab_1.conda
  sha256: aee0cdd0cb2b9321d28450aec4e0fd43566efcd79e862d70ce49a68bf0539bcd
  md5: 801dbf535ec26508fac6d4b24adfb76e
  depends:
  - ipykernel >=6.14
  - ipython
  - jupyter_client >=7.0.0
  - jupyter_core >=4.12,!=5.0.*
  - prompt_toolkit >=3.0.30
  - pygments
  - python >=3.9
  - pyzmq >=17
  - traitlets >=5.4
  license: BSD-3-Clause
  license_family: BSD
  purls:
  - pkg:pypi/jupyter-console?source=hash-mapping
  size: 26874
  timestamp: 1733818130068
- conda: https://conda.anaconda.org/conda-forge/noarch/jupyter_core-5.7.2-pyh31011fe_1.conda
  sha256: 732b1e8536bc22a5a174baa79842d79db2f4956d90293dd82dc1b3f6099bcccd
  md5: 0a2980dada0dd7fd0998f0342308b1b1
  depends:
  - __unix
  - platformdirs >=2.5
  - python >=3.8
  - traitlets >=5.3
  license: BSD-3-Clause
  license_family: BSD
  purls:
  - pkg:pypi/jupyter-core?source=hash-mapping
  size: 57671
  timestamp: 1727163547058
- conda: https://conda.anaconda.org/conda-forge/noarch/jupyter_events-0.12.0-pyh29332c3_0.conda
  sha256: 37e6ac3ccf7afcc730c3b93cb91a13b9ae827fd306f35dd28f958a74a14878b5
  md5: f56000b36f09ab7533877e695e4e8cb0
  depends:
  - jsonschema-with-format-nongpl >=4.18.0
  - packaging
  - python >=3.9
  - python-json-logger >=2.0.4
  - pyyaml >=5.3
  - referencing
  - rfc3339-validator
  - rfc3986-validator >=0.1.1
  - traitlets >=5.3
  - python
  license: BSD-3-Clause
  license_family: BSD
  purls:
  - pkg:pypi/jupyter-events?source=compressed-mapping
  size: 23647
  timestamp: 1738765986736
- conda: https://conda.anaconda.org/conda-forge/noarch/jupyter_server-2.16.0-pyhe01879c_0.conda
  sha256: 0082fb6f0afaf872affee4cde3b210f7f7497a5fb47f2944ab638fef0f0e2e77
  md5: f062e04d7cd585c937acbf194dceec36
  depends:
  - anyio >=3.1.0
  - argon2-cffi >=21.1
  - jinja2 >=3.0.3
  - jupyter_client >=7.4.4
  - jupyter_core >=4.12,!=5.0.*
  - jupyter_events >=0.11.0
  - jupyter_server_terminals >=0.4.4
  - nbconvert-core >=6.4.4
  - nbformat >=5.3.0
  - overrides >=5.0
  - packaging >=22.0
  - prometheus_client >=0.9
  - python >=3.9
  - pyzmq >=24
  - send2trash >=1.8.2
  - terminado >=0.8.3
  - tornado >=6.2.0
  - traitlets >=5.6.0
  - websocket-client >=1.7
  - python
  license: BSD-3-Clause
  license_family: BSD
  purls:
  - pkg:pypi/jupyter-server?source=hash-mapping
  size: 344376
  timestamp: 1747083217715
- conda: https://conda.anaconda.org/conda-forge/noarch/jupyter_server_terminals-0.5.3-pyhd8ed1ab_1.conda
  sha256: 0890fc79422191bc29edf17d7b42cff44ba254aa225d31eb30819f8772b775b8
  md5: 2d983ff1b82a1ccb6f2e9d8784bdd6bd
  depends:
  - python >=3.9
  - terminado >=0.8.3
  license: BSD-3-Clause
  license_family: BSD
  purls:
  - pkg:pypi/jupyter-server-terminals?source=hash-mapping
  size: 19711
  timestamp: 1733428049134
- conda: https://conda.anaconda.org/conda-forge/noarch/jupyterlab-4.4.2-pyhd8ed1ab_0.conda
  sha256: 8bc522991031ce528c650a7287159dd866b977593bdba33daa3ec37c40d99443
  md5: 1f5f3b0fcff308d8fbaa73c43af08e2f
  depends:
  - async-lru >=1.0.0
  - httpx >=0.25.0
  - importlib-metadata >=4.8.3
  - ipykernel >=6.5.0
  - jinja2 >=3.0.3
  - jupyter-lsp >=2.0.0
  - jupyter_core
  - jupyter_server >=2.4.0,<3
  - jupyterlab_server >=2.27.1,<3
  - notebook-shim >=0.2
  - packaging
  - python >=3.9
  - setuptools >=41.1.0
  - tomli >=1.2.2
  - tornado >=6.2.0
  - traitlets
  license: BSD-3-Clause
  license_family: BSD
  purls:
  - pkg:pypi/jupyterlab?source=hash-mapping
  size: 8593072
  timestamp: 1746536121732
- conda: https://conda.anaconda.org/conda-forge/noarch/jupyterlab_pygments-0.3.0-pyhd8ed1ab_2.conda
  sha256: dc24b900742fdaf1e077d9a3458fd865711de80bca95fe3c6d46610c532c6ef0
  md5: fd312693df06da3578383232528c468d
  depends:
  - pygments >=2.4.1,<3
  - python >=3.9
  constrains:
  - jupyterlab >=4.0.8,<5.0.0
  license: BSD-3-Clause
  license_family: BSD
  purls:
  - pkg:pypi/jupyterlab-pygments?source=hash-mapping
  size: 18711
  timestamp: 1733328194037
- conda: https://conda.anaconda.org/conda-forge/noarch/jupyterlab_server-2.27.3-pyhd8ed1ab_1.conda
  sha256: d03d0b7e23fa56d322993bc9786b3a43b88ccc26e58b77c756619a921ab30e86
  md5: 9dc4b2b0f41f0de41d27f3293e319357
  depends:
  - babel >=2.10
  - importlib-metadata >=4.8.3
  - jinja2 >=3.0.3
  - json5 >=0.9.0
  - jsonschema >=4.18
  - jupyter_server >=1.21,<3
  - packaging >=21.3
  - python >=3.9
  - requests >=2.31
  constrains:
  - openapi-core >=0.18.0,<0.19.0
  license: BSD-3-Clause
  license_family: BSD
  purls:
  - pkg:pypi/jupyterlab-server?source=hash-mapping
  size: 49449
  timestamp: 1733599666357
- conda: https://conda.anaconda.org/conda-forge/noarch/jupyterlab_widgets-3.0.15-pyhd8ed1ab_0.conda
  sha256: 6214d345861b106076e7cb38b59761b24cd340c09e3f787e4e4992036ca3cd7e
  md5: ad100d215fad890ab0ee10418f36876f
  depends:
  - python >=3.9
  constrains:
  - jupyterlab >=3,<5
  license: BSD-3-Clause
  license_family: BSD
  purls:
  - pkg:pypi/jupyterlab-widgets?source=hash-mapping
  size: 189133
  timestamp: 1746450926999
- conda: https://conda.anaconda.org/conda-forge/linux-64/keyutils-1.6.1-h166bdaf_0.tar.bz2
  sha256: 150c05a6e538610ca7c43beb3a40d65c90537497a4f6a5f4d15ec0451b6f5ebb
  md5: 30186d27e2c9fa62b45fb1476b7200e3
  depends:
  - libgcc-ng >=10.3.0
  license: LGPL-2.1-or-later
  purls: []
  size: 117831
  timestamp: 1646151697040
- conda: https://conda.anaconda.org/conda-forge/linux-64/kiwisolver-1.4.7-py310h3788b33_0.conda
  sha256: d97a9894803674e4f8155a5e98a49337d28bdee77dfd87e1614a824d190cd086
  md5: 4186d9b4d004b0fe0de6aa62496fb48a
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - libstdcxx >=13
  - python >=3.10,<3.11.0a0
  - python_abi 3.10.* *_cp310
  license: BSD-3-Clause
  license_family: BSD
  purls:
  - pkg:pypi/kiwisolver?source=hash-mapping
  size: 71864
  timestamp: 1725459334634
- conda: https://conda.anaconda.org/conda-forge/linux-64/krb5-1.21.3-h659f571_0.conda
  sha256: 99df692f7a8a5c27cd14b5fb1374ee55e756631b9c3d659ed3ee60830249b238
  md5: 3f43953b7d3fb3aaa1d0d0723d91e368
  depends:
  - keyutils >=1.6.1,<2.0a0
  - libedit >=3.1.20191231,<3.2.0a0
  - libedit >=3.1.20191231,<4.0a0
  - libgcc-ng >=12
  - libstdcxx-ng >=12
  - openssl >=3.3.1,<4.0a0
  license: MIT
  license_family: MIT
  purls: []
  size: 1370023
  timestamp: 1719463201255
- conda: https://conda.anaconda.org/conda-forge/noarch/langcodes-3.4.1-pyhd8ed1ab_1.conda
  sha256: 09dabbe71c333b2a89dd31eb167afc75d23019414fbaf8f24a011072ac5e97ab
  md5: 50727f5554dc6436399c9c1760263acc
  depends:
  - language-data >=1.2
  - python >=3.9
  license: MIT
  license_family: MIT
  purls:
  - pkg:pypi/langcodes?source=hash-mapping
  size: 155393
  timestamp: 1734376571010
- conda: https://conda.anaconda.org/conda-forge/noarch/language-data-1.3.0-pyhff2d567_0.conda
  sha256: 69ee9a45ab26c0a52eebe18201a4863c3129b82022fe1803626e345bf098386f
  md5: 03791f1242445cf08900118f5bea86e6
  depends:
  - marisa-trie >=0.7.7
  - python >=3.9
  license: MIT
  license_family: MIT
  purls:
  - pkg:pypi/language-data?source=hash-mapping
  size: 4234469
  timestamp: 1732032510329
- conda: https://conda.anaconda.org/conda-forge/linux-64/lcms2-2.17-h717163a_0.conda
  sha256: d6a61830a354da022eae93fa896d0991385a875c6bba53c82263a289deda9db8
  md5: 000e85703f0fd9594c81710dd5066471
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - libjpeg-turbo >=3.0.0,<4.0a0
  - libtiff >=4.7.0,<4.8.0a0
  license: MIT
  license_family: MIT
  purls: []
  size: 248046
  timestamp: 1739160907615
- conda: https://conda.anaconda.org/conda-forge/linux-64/ld_impl_linux-64-2.43-h712a8e2_4.conda
  sha256: db73f38155d901a610b2320525b9dd3b31e4949215c870685fd92ea61b5ce472
  md5: 01f8d123c96816249efd255a31ad7712
  depends:
  - __glibc >=2.17,<3.0.a0
  constrains:
  - binutils_impl_linux-64 2.43
  license: GPL-3.0-only
  license_family: GPL
  purls: []
  size: 671240
  timestamp: 1740155456116
- conda: https://conda.anaconda.org/conda-forge/linux-64/leptonica-1.83.1-hb768ceb_6.conda
  sha256: df328692de74d87af929d2e05add47858ad6a7637008c8abced7dca6ade22a24
  md5: d78e664e0c7cb013796fa3886faae444
  depends:
  - __glibc >=2.17,<3.0.a0
  - giflib >=5.2.2,<5.3.0a0
  - libgcc >=13
  - libjpeg-turbo >=3.0.0,<4.0a0
  - liblzma >=5.6.4,<6.0a0
  - libpng >=1.6.47,<1.7.0a0
  - libtiff >=4.7.0,<4.8.0a0
  - libwebp-base >=1.5.0,<2.0a0
  - libzlib >=1.3.1,<2.0a0
  - openjpeg >=2.5.3,<3.0a0
  license: BSD-2-Clause
  license_family: BSD
  purls: []
  size: 1867297
  timestamp: 1742503579196
- conda: https://conda.anaconda.org/conda-forge/linux-64/lerc-4.0.0-h0aef613_1.conda
  sha256: 412381a43d5ff9bbed82cd52a0bbca5b90623f62e41007c9c42d3870c60945ff
  md5: 9344155d33912347b37f0ae6c410a835
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - libstdcxx >=13
  license: Apache-2.0
  license_family: Apache
  purls: []
  size: 264243
  timestamp: 1745264221534
- conda: https://conda.anaconda.org/conda-forge/linux-64/libabseil-20250127.1-cxx17_hbbce691_0.conda
  sha256: 65d5ca837c3ee67b9d769125c21dc857194d7f6181bb0e7bd98ae58597b457d0
  md5: 00290e549c5c8a32cc271020acc9ec6b
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - libstdcxx >=13
  constrains:
  - abseil-cpp =20250127.1
  - libabseil-static =20250127.1=cxx17*
  license: Apache-2.0
  license_family: Apache
  purls: []
  size: 1325007
  timestamp: 1742369558286
- conda: https://conda.anaconda.org/conda-forge/linux-64/libarchive-3.7.7-h75ea233_4.conda
  sha256: d49b2a3617b689763d1377a5d1fbfc3c914ee0afa26b3c1858e1c4329329c6df
  md5: b80309616f188ac77c4740acba40f796
  depends:
  - __glibc >=2.17,<3.0.a0
  - bzip2 >=1.0.8,<2.0a0
  - libgcc >=13
  - liblzma >=5.8.1,<6.0a0
  - libxml2 >=2.13.7,<2.14.0a0
  - libzlib >=1.3.1,<2.0a0
  - lz4-c >=1.10.0,<1.11.0a0
  - lzo >=2.10,<3.0a0
  - openssl >=3.5.0,<4.0a0
  - zstd >=1.5.7,<1.6.0a0
  license: BSD-2-Clause
  license_family: BSD
  purls: []
  size: 866358
  timestamp: 1745335292389
- conda: https://conda.anaconda.org/conda-forge/linux-64/libarrow-20.0.0-hebdba27_3_cpu.conda
  build_number: 3
  sha256: dff51b5c2164ad21b7dbf796f7c79c2abba84a88d6932ce7bd09418a672a5e83
  md5: 5be86a1b5f496f82c7dfeb0dbe19ef03
  depends:
  - __glibc >=2.17,<3.0.a0
  - aws-crt-cpp >=0.32.5,<0.32.6.0a0
  - aws-sdk-cpp >=1.11.510,<1.11.511.0a0
  - azure-core-cpp >=1.14.0,<1.14.1.0a0
  - azure-identity-cpp >=1.10.0,<1.10.1.0a0
  - azure-storage-blobs-cpp >=12.13.0,<12.13.1.0a0
  - azure-storage-files-datalake-cpp >=12.12.0,<12.12.1.0a0
  - bzip2 >=1.0.8,<2.0a0
  - glog >=0.7.1,<0.8.0a0
  - libabseil * cxx17*
  - libabseil >=20250127.1,<20250128.0a0
  - libbrotlidec >=1.1.0,<1.2.0a0
  - libbrotlienc >=1.1.0,<1.2.0a0
  - libgcc >=13
  - libgoogle-cloud >=2.36.0,<2.37.0a0
  - libgoogle-cloud-storage >=2.36.0,<2.37.0a0
  - libopentelemetry-cpp >=1.20.0,<1.21.0a0
  - libprotobuf >=5.29.3,<5.29.4.0a0
  - libre2-11 >=2024.7.2
  - libstdcxx >=13
  - libutf8proc >=2.10.0,<2.11.0a0
  - libzlib >=1.3.1,<2.0a0
  - lz4-c >=1.10.0,<1.11.0a0
  - orc >=2.1.2,<2.1.3.0a0
  - re2
  - snappy >=1.2.1,<1.3.0a0
  - zstd >=1.5.7,<1.6.0a0
  constrains:
  - parquet-cpp <0.0a0
  - arrow-cpp <0.0a0
  - apache-arrow-proc =*=cpu
  license: Apache-2.0
  license_family: APACHE
  purls: []
  size: 9189847
  timestamp: 1746920464544
- conda: https://conda.anaconda.org/conda-forge/linux-64/libarrow-acero-20.0.0-hcb10f89_3_cpu.conda
  build_number: 3
  sha256: 28f186a7806085e13cb8ee939931dc2020b59413b762f68b872cc6620f777f69
  md5: 679cd6bb558cd6565d98ad66af6ff6ed
  depends:
  - __glibc >=2.17,<3.0.a0
  - libarrow 20.0.0 hebdba27_3_cpu
  - libgcc >=13
  - libstdcxx >=13
  license: Apache-2.0
  license_family: APACHE
  purls: []
  size: 642069
  timestamp: 1746920544904
- conda: https://conda.anaconda.org/conda-forge/linux-64/libarrow-dataset-20.0.0-hcb10f89_3_cpu.conda
  build_number: 3
  sha256: ae0cc1eade563a14eaf59a921021cec5c526f6c1af93b81d3136caf41075c6ef
  md5: 0e84685fdecbd83666dd73292cc7d05a
  depends:
  - __glibc >=2.17,<3.0.a0
  - libarrow 20.0.0 hebdba27_3_cpu
  - libarrow-acero 20.0.0 hcb10f89_3_cpu
  - libgcc >=13
  - libparquet 20.0.0 h081d1f1_3_cpu
  - libstdcxx >=13
  license: Apache-2.0
  license_family: APACHE
  purls: []
  size: 607683
  timestamp: 1746920679379
- conda: https://conda.anaconda.org/conda-forge/linux-64/libarrow-substrait-20.0.0-h1bed206_3_cpu.conda
  build_number: 3
  sha256: 828806da67cb821c74d43920cc15782d5c8b08318807799b30d9cbcf9fe94733
  md5: c4d2a874f1ca439fb3c2a17060d6b911
  depends:
  - __glibc >=2.17,<3.0.a0
  - libabseil * cxx17*
  - libabseil >=20250127.1,<20250128.0a0
  - libarrow 20.0.0 hebdba27_3_cpu
  - libarrow-acero 20.0.0 hcb10f89_3_cpu
  - libarrow-dataset 20.0.0 hcb10f89_3_cpu
  - libgcc >=13
  - libprotobuf >=5.29.3,<5.29.4.0a0
  - libstdcxx >=13
  license: Apache-2.0
  license_family: APACHE
  purls: []
  size: 525332
  timestamp: 1746920767029
- conda: https://conda.anaconda.org/conda-forge/linux-64/libavif16-1.3.0-h766b0b6_0.conda
  sha256: 170b51a3751c2f842ff9e11d22423494ef7254b448ef2b24751256ef18aa1302
  md5: f17f2d0e5c9ad6b958547fd67b155771
  depends:
  - __glibc >=2.17,<3.0.a0
  - aom >=3.9.1,<3.10.0a0
  - dav1d >=1.2.1,<1.2.2.0a0
  - libgcc >=13
  - rav1e >=0.7.1,<0.8.0a0
  - svt-av1 >=3.0.2,<3.0.3.0a0
  license: BSD-2-Clause
  license_family: BSD
  purls: []
  size: 140052
  timestamp: 1746836263991
- conda: https://conda.anaconda.org/conda-forge/linux-64/libblas-3.9.0-31_hfdb39a5_mkl.conda
  build_number: 31
  sha256: 862289f2cfb84bb6001d0e3569e908b8c42d66b881bd5b03f730a3924628b978
  md5: bdf4a57254e8248222cb631db4393ff1
  depends:
  - mkl >=2024.2.2,<2025.0a0
  constrains:
  - liblapack =3.9.0=31*_mkl
  - liblapacke =3.9.0=31*_mkl
  - blas =2.131=mkl
  - libcblas =3.9.0=31*_mkl
  track_features:
  - blas_mkl
  license: BSD-3-Clause
  license_family: BSD
  purls: []
  size: 17259
  timestamp: 1740087718283
- conda: https://conda.anaconda.org/conda-forge/linux-64/libbrotlicommon-1.1.0-hb9d3cd8_2.conda
  sha256: d9db2de60ea917298e658143354a530e9ca5f9c63471c65cf47ab39fd2f429e3
  md5: 41b599ed2b02abcfdd84302bff174b23
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  license: MIT
  license_family: MIT
  purls: []
  size: 68851
  timestamp: 1725267660471
- conda: https://conda.anaconda.org/conda-forge/linux-64/libbrotlidec-1.1.0-hb9d3cd8_2.conda
  sha256: 2892d512cad096cb03f1b66361deeab58b64e15ba525d6592bb6d609e7045edf
  md5: 9566f0bd264fbd463002e759b8a82401
  depends:
  - __glibc >=2.17,<3.0.a0
  - libbrotlicommon 1.1.0 hb9d3cd8_2
  - libgcc >=13
  license: MIT
  license_family: MIT
  purls: []
  size: 32696
  timestamp: 1725267669305
- conda: https://conda.anaconda.org/conda-forge/linux-64/libbrotlienc-1.1.0-hb9d3cd8_2.conda
  sha256: 779f58174e99de3600e939fa46eddb453ec5d3c60bb46cdaa8b4c127224dbf29
  md5: 06f70867945ea6a84d35836af780f1de
  depends:
  - __glibc >=2.17,<3.0.a0
  - libbrotlicommon 1.1.0 hb9d3cd8_2
  - libgcc >=13
  license: MIT
  license_family: MIT
  purls: []
  size: 281750
  timestamp: 1725267679782
- conda: https://conda.anaconda.org/conda-forge/linux-64/libcblas-3.9.0-31_h372d94f_mkl.conda
  build_number: 31
  sha256: 2ee3ab2b6eeb59f2d3c6f933fa0db28f1b56f0bc543ed2c0f6ec04060e4b6ec0
  md5: 2a06a6c16b45bd3d10002927ca204b67
  depends:
  - libblas 3.9.0 31_hfdb39a5_mkl
  constrains:
  - liblapack =3.9.0=31*_mkl
  - liblapacke =3.9.0=31*_mkl
  - blas =2.131=mkl
  track_features:
  - blas_mkl
  license: BSD-3-Clause
  license_family: BSD
  purls: []
  size: 16724
  timestamp: 1740087727554
- conda: https://conda.anaconda.org/conda-forge/linux-64/libclang-cpp20.1-20.1.5-default_h1df26ce_1.conda
  sha256: 1938ac6a3ac89e2eeed89a01e3c998f87d83cdbde5dd7650c84c64e4b5502f34
  md5: 330b1dadfa7c3205a01fa9599fabe808
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - libllvm20 >=20.1.5,<20.2.0a0
  - libstdcxx >=13
  license: Apache-2.0 WITH LLVM-exception
  license_family: Apache
  purls: []
  size: 20894564
  timestamp: 1747697571531
- conda: https://conda.anaconda.org/conda-forge/linux-64/libclang13-20.1.5-default_he06ed0a_1.conda
  sha256: e3047ed743f54160715a58d9f0a3deff2f76cd847412a6270982c849547a13ef
  md5: 12117145218e7e1a528c8396ed803058
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - libllvm20 >=20.1.5,<20.2.0a0
  - libstdcxx >=13
  license: Apache-2.0 WITH LLVM-exception
  license_family: Apache
  purls: []
  size: 12116539
  timestamp: 1747697840742
- conda: https://conda.anaconda.org/conda-forge/linux-64/libcrc32c-1.1.2-h9c3ff4c_0.tar.bz2
  sha256: fd1d153962764433fe6233f34a72cdeed5dcf8a883a85769e8295ce940b5b0c5
  md5: c965a5aa0d5c1c37ffc62dff36e28400
  depends:
  - libgcc-ng >=9.4.0
  - libstdcxx-ng >=9.4.0
  license: BSD-3-Clause
  license_family: BSD
  purls: []
  size: 20440
  timestamp: 1633683576494
- conda: https://conda.anaconda.org/conda-forge/linux-64/libcups-2.3.3-h4637d8d_4.conda
  sha256: bc67b9b21078c99c6bd8595fe7e1ed6da1f721007726e717f0449de7032798c4
  md5: d4529f4dff3057982a7617c7ac58fde3
  depends:
  - krb5 >=1.21.1,<1.22.0a0
  - libgcc-ng >=12
  - libstdcxx-ng >=12
  - libzlib >=1.2.13,<2.0.0a0
  license: Apache-2.0
  license_family: Apache
  purls: []
  size: 4519402
  timestamp: 1689195353551
- conda: https://conda.anaconda.org/conda-forge/linux-64/libcurl-8.13.0-h332b0f4_0.conda
  sha256: 38e528acfaa0276b7052f4de44271ff9293fdb84579650601a8c49dac171482a
  md5: cbdc92ac0d93fe3c796e36ad65c7905c
  depends:
  - __glibc >=2.17,<3.0.a0
  - krb5 >=1.21.3,<1.22.0a0
  - libgcc >=13
  - libnghttp2 >=1.64.0,<2.0a0
  - libssh2 >=1.11.1,<2.0a0
  - libzlib >=1.3.1,<2.0a0
  - openssl >=3.4.1,<4.0a0
  - zstd >=1.5.7,<1.6.0a0
  license: curl
  license_family: MIT
  purls: []
  size: 438088
  timestamp: 1743601695669
- conda: https://conda.anaconda.org/conda-forge/linux-64/libde265-1.0.15-h00ab1b0_0.conda
  sha256: 7cf7e294e1a7c8219065885e186d8f52002fb900bf384d815f159b5874204e3d
  md5: 407fee7a5d7ab2dca12c9ca7f62310ad
  depends:
  - libgcc-ng >=12
  - libstdcxx-ng >=12
  license: LGPL-3.0-or-later
  license_family: LGPL
  purls: []
  size: 411814
  timestamp: 1703088639063
- conda: https://conda.anaconda.org/conda-forge/linux-64/libdeflate-1.24-h86f0d12_0.conda
  sha256: 8420748ea1cc5f18ecc5068b4f24c7a023cc9b20971c99c824ba10641fb95ddf
  md5: 64f0c503da58ec25ebd359e4d990afa8
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  license: MIT
  license_family: MIT
  purls: []
  size: 72573
  timestamp: 1747040452262
- conda: https://conda.anaconda.org/conda-forge/linux-64/libdrm-2.4.124-hb9d3cd8_0.conda
  sha256: f0d5ffbdf3903a7840184d14c14154b503e1a96767c328f61d99ad24b6963e52
  md5: 8bc89311041d7fcb510238cf0848ccae
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - libpciaccess >=0.18,<0.19.0a0
  license: MIT
  license_family: MIT
  purls: []
  size: 242533
  timestamp: 1733424409299
- conda: https://conda.anaconda.org/conda-forge/linux-64/libedit-3.1.20250104-pl5321h7949ede_0.conda
  sha256: d789471216e7aba3c184cd054ed61ce3f6dac6f87a50ec69291b9297f8c18724
  md5: c277e0a4d549b03ac1e9d6cbbe3d017b
  depends:
  - ncurses
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - ncurses >=6.5,<7.0a0
  license: BSD-2-Clause
  license_family: BSD
  purls: []
  size: 134676
  timestamp: 1738479519902
- conda: https://conda.anaconda.org/conda-forge/linux-64/libegl-1.7.0-ha4b6fd6_2.conda
  sha256: 7fd5408d359d05a969133e47af580183fbf38e2235b562193d427bb9dad79723
  md5: c151d5eb730e9b7480e6d48c0fc44048
  depends:
  - __glibc >=2.17,<3.0.a0
  - libglvnd 1.7.0 ha4b6fd6_2
  license: LicenseRef-libglvnd
  purls: []
  size: 44840
  timestamp: 1731330973553
- conda: https://conda.anaconda.org/conda-forge/linux-64/libev-4.33-hd590300_2.conda
  sha256: 1cd6048169fa0395af74ed5d8f1716e22c19a81a8a36f934c110ca3ad4dd27b4
  md5: 172bf1cd1ff8629f2b1179945ed45055
  depends:
  - libgcc-ng >=12
  license: BSD-2-Clause
  license_family: BSD
  purls: []
  size: 112766
  timestamp: 1702146165126
- conda: https://conda.anaconda.org/conda-forge/linux-64/libevent-2.1.12-hf998b51_1.conda
  sha256: 2e14399d81fb348e9d231a82ca4d816bf855206923759b69ad006ba482764131
  md5: a1cfcc585f0c42bf8d5546bb1dfb668d
  depends:
  - libgcc-ng >=12
  - openssl >=3.1.1,<4.0a0
  license: BSD-3-Clause
  license_family: BSD
  purls: []
  size: 427426
  timestamp: 1685725977222
- conda: https://conda.anaconda.org/conda-forge/linux-64/libexpat-2.7.0-h5888daf_0.conda
  sha256: 33ab03438aee65d6aa667cf7d90c91e5e7d734c19a67aa4c7040742c0a13d505
  md5: db0bfbe7dd197b68ad5f30333bae6ce0
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  constrains:
  - expat 2.7.0.*
  license: MIT
  license_family: MIT
  purls: []
  size: 74427
  timestamp: 1743431794976
- conda: https://conda.anaconda.org/conda-forge/linux-64/libffi-3.4.6-h2dba641_1.conda
  sha256: 764432d32db45466e87f10621db5b74363a9f847d2b8b1f9743746cd160f06ab
  md5: ede4673863426c0883c0063d853bbd85
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  license: MIT
  license_family: MIT
  purls: []
  size: 57433
  timestamp: 1743434498161
- conda: https://conda.anaconda.org/conda-forge/linux-64/libfreetype-2.13.3-ha770c72_1.conda
  sha256: 7be9b3dac469fe3c6146ff24398b685804dfc7a1de37607b84abd076f57cc115
  md5: 51f5be229d83ecd401fb369ab96ae669
  depends:
  - libfreetype6 >=2.13.3
  license: GPL-2.0-only OR FTL
  purls: []
  size: 7693
  timestamp: 1745369988361
- conda: https://conda.anaconda.org/conda-forge/linux-64/libfreetype6-2.13.3-h48d6fc4_1.conda
  sha256: 7759bd5c31efe5fbc36a7a1f8ca5244c2eabdbeb8fc1bee4b99cf989f35c7d81
  md5: 3c255be50a506c50765a93a6644f32fe
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - libpng >=1.6.47,<1.7.0a0
  - libzlib >=1.3.1,<2.0a0
  constrains:
  - freetype >=2.13.3
  license: GPL-2.0-only OR FTL
  purls: []
  size: 380134
  timestamp: 1745369987697
- conda: https://conda.anaconda.org/conda-forge/linux-64/libgcc-15.1.0-h767d61c_2.conda
  sha256: 0024f9ab34c09629621aefd8603ef77bf9d708129b0dd79029e502c39ffc2195
  md5: ea8ac52380885ed41c1baa8f1d6d2b93
  depends:
  - __glibc >=2.17,<3.0.a0
  - _openmp_mutex >=4.5
  constrains:
  - libgcc-ng ==15.1.0=*_2
  - libgomp 15.1.0 h767d61c_2
  license: GPL-3.0-only WITH GCC-exception-3.1
  license_family: GPL
  purls: []
  size: 829108
  timestamp: 1746642191935
- conda: https://conda.anaconda.org/conda-forge/linux-64/libgcc-ng-15.1.0-h69a702a_2.conda
  sha256: 0ab5421a89f090f3aa33841036bb3af4ed85e1f91315b528a9d75fab9aad51ae
  md5: ddca86c7040dd0e73b2b69bd7833d225
  depends:
  - libgcc 15.1.0 h767d61c_2
  license: GPL-3.0-only WITH GCC-exception-3.1
  license_family: GPL
  purls: []
  size: 34586
  timestamp: 1746642200749
- conda: https://conda.anaconda.org/conda-forge/linux-64/libgd-2.3.3-h6f5c62b_11.conda
  sha256: 19e5be91445db119152217e8e8eec4fd0499d854acc7d8062044fb55a70971cd
  md5: 68fc66282364981589ef36868b1a7c78
  depends:
  - __glibc >=2.17,<3.0.a0
  - fontconfig >=2.15.0,<3.0a0
  - fonts-conda-ecosystem
  - freetype >=2.12.1,<3.0a0
  - icu >=75.1,<76.0a0
  - libexpat >=2.6.4,<3.0a0
  - libgcc >=13
  - libjpeg-turbo >=3.0.0,<4.0a0
  - libpng >=1.6.45,<1.7.0a0
  - libtiff >=4.7.0,<4.8.0a0
  - libwebp-base >=1.5.0,<2.0a0
  - libzlib >=1.3.1,<2.0a0
  license: GD
  license_family: BSD
  purls: []
  size: 177082
  timestamp: 1737548051015
- conda: https://conda.anaconda.org/conda-forge/linux-64/libgfortran-15.1.0-h69a702a_2.conda
  sha256: 914daa4f632b786827ea71b5e07cd00d25fc6e67789db2f830dc481eec660342
  md5: f92e6e0a3c0c0c85561ef61aa59d555d
  depends:
  - libgfortran5 15.1.0 hcea5267_2
  constrains:
  - libgfortran-ng ==15.1.0=*_2
  license: GPL-3.0-only WITH GCC-exception-3.1
  license_family: GPL
  purls: []
  size: 34541
  timestamp: 1746642233221
- conda: https://conda.anaconda.org/conda-forge/linux-64/libgfortran-ng-15.1.0-h69a702a_2.conda
  sha256: 0665170a98c8ec586352929d45a9c833c0dcdbead38b0b8f3af7a0deee2af755
  md5: a483a87b71e974bb75d1b9413d4436dd
  depends:
  - libgfortran 15.1.0 h69a702a_2
  license: GPL-3.0-only WITH GCC-exception-3.1
  license_family: GPL
  purls: []
  size: 34616
  timestamp: 1746642441079
- conda: https://conda.anaconda.org/conda-forge/linux-64/libgfortran5-15.1.0-hcea5267_2.conda
  sha256: be23750f3ca1a5cb3ada858c4f633effe777487d1ea35fddca04c0965c073350
  md5: 01de444988ed960031dbe84cf4f9b1fc
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=15.1.0
  constrains:
  - libgfortran 15.1.0
  license: GPL-3.0-only WITH GCC-exception-3.1
  license_family: GPL
  purls: []
  size: 1569986
  timestamp: 1746642212331
- conda: https://conda.anaconda.org/conda-forge/linux-64/libgl-1.7.0-ha4b6fd6_2.conda
  sha256: dc2752241fa3d9e40ce552c1942d0a4b5eeb93740c9723873f6fcf8d39ef8d2d
  md5: 928b8be80851f5d8ffb016f9c81dae7a
  depends:
  - __glibc >=2.17,<3.0.a0
  - libglvnd 1.7.0 ha4b6fd6_2
  - libglx 1.7.0 ha4b6fd6_2
  license: LicenseRef-libglvnd
  purls: []
  size: 134712
  timestamp: 1731330998354
- conda: https://conda.anaconda.org/conda-forge/linux-64/libglib-2.84.1-h2ff4ddf_0.conda
  sha256: 18e354d30a60441b0bf5fcbb125b6b22fd0df179620ae834e2533d44d1598211
  md5: 0305434da649d4fb48a425e588b79ea6
  depends:
  - __glibc >=2.17,<3.0.a0
  - libffi >=3.4.6,<3.5.0a0
  - libgcc >=13
  - libiconv >=1.18,<2.0a0
  - libzlib >=1.3.1,<2.0a0
  - pcre2 >=10.44,<10.45.0a0
  constrains:
  - glib 2.84.1 *_0
  license: LGPL-2.1-or-later
  purls: []
  size: 3947789
  timestamp: 1743773764878
- conda: https://conda.anaconda.org/conda-forge/linux-64/libglvnd-1.7.0-ha4b6fd6_2.conda
  sha256: 1175f8a7a0c68b7f81962699751bb6574e6f07db4c9f72825f978e3016f46850
  md5: 434ca7e50e40f4918ab701e3facd59a0
  depends:
  - __glibc >=2.17,<3.0.a0
  license: LicenseRef-libglvnd
  purls: []
  size: 132463
  timestamp: 1731330968309
- conda: https://conda.anaconda.org/conda-forge/linux-64/libglx-1.7.0-ha4b6fd6_2.conda
  sha256: 2d35a679624a93ce5b3e9dd301fff92343db609b79f0363e6d0ceb3a6478bfa7
  md5: c8013e438185f33b13814c5c488acd5c
  depends:
  - __glibc >=2.17,<3.0.a0
  - libglvnd 1.7.0 ha4b6fd6_2
  - xorg-libx11 >=1.8.10,<2.0a0
  license: LicenseRef-libglvnd
  purls: []
  size: 75504
  timestamp: 1731330988898
- conda: https://conda.anaconda.org/conda-forge/linux-64/libgoogle-cloud-2.36.0-hc4361e1_1.conda
  sha256: 3a56c653231d6233de5853dc01f07afad6a332799a39c3772c0948d2e68547e4
  md5: ae36e6296a8dd8e8a9a8375965bf6398
  depends:
  - __glibc >=2.17,<3.0.a0
  - libabseil * cxx17*
  - libabseil >=20250127.0,<20250128.0a0
  - libcurl >=8.12.1,<9.0a0
  - libgcc >=13
  - libgrpc >=1.71.0,<1.72.0a0
  - libprotobuf >=5.29.3,<5.29.4.0a0
  - libstdcxx >=13
  - openssl >=3.4.1,<4.0a0
  constrains:
  - libgoogle-cloud 2.36.0 *_1
  license: Apache-2.0
  license_family: Apache
  purls: []
  size: 1246764
  timestamp: 1741878603939
- conda: https://conda.anaconda.org/conda-forge/linux-64/libgoogle-cloud-storage-2.36.0-h0121fbd_1.conda
  sha256: 54235d990009417bb20071f5ce7c8dcf186b19fa7d24d72bc5efd2ffb108001c
  md5: a0f7588c1f0a26d550e7bae4fb49427a
  depends:
  - __glibc >=2.17,<3.0.a0
  - libabseil
  - libcrc32c >=1.1.2,<1.2.0a0
  - libcurl
  - libgcc >=13
  - libgoogle-cloud 2.36.0 hc4361e1_1
  - libstdcxx >=13
  - libzlib >=1.3.1,<2.0a0
  - openssl
  license: Apache-2.0
  license_family: Apache
  purls: []
  size: 785719
  timestamp: 1741878763994
- conda: https://conda.anaconda.org/conda-forge/linux-64/libgrpc-1.71.0-h8e591d7_1.conda
  sha256: 37267300b25f292a6024d7fd9331085fe4943897940263c3a41d6493283b2a18
  md5: c3cfd72cbb14113abee7bbd86f44ad69
  depends:
  - __glibc >=2.17,<3.0.a0
  - c-ares >=1.34.5,<2.0a0
  - libabseil * cxx17*
  - libabseil >=20250127.1,<20250128.0a0
  - libgcc >=13
  - libprotobuf >=5.29.3,<5.29.4.0a0
  - libre2-11 >=2024.7.2
  - libstdcxx >=13
  - libzlib >=1.3.1,<2.0a0
  - openssl >=3.5.0,<4.0a0
  - re2
  constrains:
  - grpc-cpp =1.71.0
  license: Apache-2.0
  license_family: APACHE
  purls: []
  size: 7920187
  timestamp: 1745229332239
- conda: https://conda.anaconda.org/conda-forge/linux-64/libgumbo-1.0.0-h3f2d84a_1.conda
  sha256: f807330a15d4e217b48071b7ab6ad04cc4827b06c7f34621774c55097b103471
  md5: 02ad58257c7b448dee1bb60538c022bf
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - libstdcxx >=13
  - libgcc >=13
  license: Apache-2.0
  license_family: APACHE
  purls: []
  size: 174782
  timestamp: 1734515914537
- conda: https://conda.anaconda.org/conda-forge/linux-64/libheif-1.19.7-gpl_hc18d805_100.conda
  sha256: ec9797d57088aeed7ca4905777d4f3e70a4dbe90853590eef7006b0ab337af3f
  md5: 1db2693fa6a50bef58da2df97c5204cb
  depends:
  - __glibc >=2.17,<3.0.a0
  - aom >=3.9.1,<3.10.0a0
  - dav1d >=1.2.1,<1.2.2.0a0
  - libavif16 >=1.2.0,<2.0a0
  - libde265 >=1.0.15,<1.0.16.0a0
  - libgcc >=13
  - libstdcxx >=13
  - x265 >=3.5,<3.6.0a0
  license: LGPL-3.0-or-later
  license_family: LGPL
  purls: []
  size: 596714
  timestamp: 1741306859216
- conda: https://conda.anaconda.org/conda-forge/linux-64/libhwloc-2.11.2-default_h0d58e46_1001.conda
  sha256: d14c016482e1409ae1c50109a9ff933460a50940d2682e745ab1c172b5282a69
  md5: 804ca9e91bcaea0824a341d55b1684f2
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - libstdcxx >=13
  - libxml2 >=2.13.4,<2.14.0a0
  license: BSD-3-Clause
  license_family: BSD
  purls: []
  size: 2423200
  timestamp: 1731374922090
- conda: https://conda.anaconda.org/conda-forge/linux-64/libiconv-1.18-h4ce23a2_1.conda
  sha256: 18a4afe14f731bfb9cf388659994263904d20111e42f841e9eea1bb6f91f4ab4
  md5: e796ff8ddc598affdf7c173d6145f087
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  license: LGPL-2.1-only
  purls: []
  size: 713084
  timestamp: 1740128065462
- conda: https://conda.anaconda.org/conda-forge/linux-64/libjpeg-turbo-3.1.0-hb9d3cd8_0.conda
  sha256: 98b399287e27768bf79d48faba8a99a2289748c65cd342ca21033fab1860d4a4
  md5: 9fa334557db9f63da6c9285fd2a48638
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  constrains:
  - jpeg <0.0.0a
  license: IJG AND BSD-3-Clause AND Zlib
  purls: []
  size: 628947
  timestamp: 1745268527144
- conda: https://conda.anaconda.org/conda-forge/linux-64/liblapack-3.9.0-31_hc41d3b0_mkl.conda
  build_number: 31
  sha256: a2d20845d916ac8fba09376cd791136a9b4547afb2131bc315178adfc87bb4ca
  md5: 10d012ddd7cc1c7ff9093d4974a34e53
  depends:
  - libblas 3.9.0 31_hfdb39a5_mkl
  constrains:
  - liblapacke =3.9.0=31*_mkl
  - blas =2.131=mkl
  - libcblas =3.9.0=31*_mkl
  track_features:
  - blas_mkl
  license: BSD-3-Clause
  license_family: BSD
  purls: []
  size: 16760
  timestamp: 1740087736615
- conda: https://conda.anaconda.org/conda-forge/linux-64/libllvm20-20.1.5-he9d0ab4_0.conda
  sha256: c8bb2c5744e4da00f63d53524897a6cb8fa0dcd7853a6ec6e084e8c5aff001d9
  md5: 8d2f5a2f019bd76ccba5eb771852d411
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - libstdcxx >=13
  - libxml2 >=2.13.8,<2.14.0a0
  - libzlib >=1.3.1,<2.0a0
  - zstd >=1.5.7,<1.6.0a0
  license: Apache-2.0 WITH LLVM-exception
  license_family: Apache
  purls: []
  size: 42996429
  timestamp: 1747318745242
- conda: https://conda.anaconda.org/conda-forge/linux-64/liblzma-5.8.1-hb9d3cd8_1.conda
  sha256: eeff241bddc8f1b87567dd6507c9f441f7f472c27f0860a07628260c000ef27c
  md5: a76fd702c93cd2dfd89eff30a5fd45a8
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  constrains:
  - xz 5.8.1.*
  - xz ==5.8.1=*_1
  license: 0BSD
  purls: []
  size: 112845
  timestamp: 1746531470399
- conda: https://conda.anaconda.org/conda-forge/linux-64/liblzma-devel-5.8.1-hb9d3cd8_1.conda
  sha256: f157a2da5f7bf2c5ce5a18c52ccc76c39f075f7fbb1584d585a8d25c1b17cb92
  md5: 5499e2dd2f567a818b9f111e47caebd2
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - liblzma 5.8.1 hb9d3cd8_1
  license: 0BSD
  purls: []
  size: 441592
  timestamp: 1746531484594
- conda: https://conda.anaconda.org/conda-forge/linux-64/libnghttp2-1.64.0-h161d5f1_0.conda
  sha256: b0f2b3695b13a989f75d8fd7f4778e1c7aabe3b36db83f0fe80b2cd812c0e975
  md5: 19e57602824042dfd0446292ef90488b
  depends:
  - __glibc >=2.17,<3.0.a0
  - c-ares >=1.32.3,<2.0a0
  - libev >=4.33,<4.34.0a0
  - libev >=4.33,<5.0a0
  - libgcc >=13
  - libstdcxx >=13
  - libzlib >=1.3.1,<2.0a0
  - openssl >=3.3.2,<4.0a0
  license: MIT
  license_family: MIT
  purls: []
  size: 647599
  timestamp: 1729571887612
- conda: https://conda.anaconda.org/conda-forge/linux-64/libnsl-2.0.1-hd590300_0.conda
  sha256: 26d77a3bb4dceeedc2a41bd688564fe71bf2d149fdcf117049970bc02ff1add6
  md5: 30fd6e37fe21f86f4bd26d6ee73eeec7
  depends:
  - libgcc-ng >=12
  license: LGPL-2.1-only
  license_family: GPL
  purls: []
  size: 33408
  timestamp: 1697359010159
- conda: https://conda.anaconda.org/conda-forge/linux-64/libntlm-1.8-hb9d3cd8_0.conda
  sha256: 3b3f19ced060013c2dd99d9d46403be6d319d4601814c772a3472fe2955612b0
  md5: 7c7927b404672409d9917d49bff5f2d6
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  license: LGPL-2.1-or-later
  purls: []
  size: 33418
  timestamp: 1734670021371
- conda: https://conda.anaconda.org/conda-forge/linux-64/libopengl-1.7.0-ha4b6fd6_2.conda
  sha256: 215086c108d80349e96051ad14131b751d17af3ed2cb5a34edd62fa89bfe8ead
  md5: 7df50d44d4a14d6c31a2c54f2cd92157
  depends:
  - __glibc >=2.17,<3.0.a0
  - libglvnd 1.7.0 ha4b6fd6_2
  license: LicenseRef-libglvnd
  purls: []
  size: 50757
  timestamp: 1731330993524
- conda: https://conda.anaconda.org/conda-forge/linux-64/libopentelemetry-cpp-1.20.0-hd1b1c89_0.conda
  sha256: 11ba93b440f3332499801b8f9580cea3dc19c3aa440c4deb30fd8be302a71c7f
  md5: e1185384cc23e3bbf85486987835df94
  depends:
  - libabseil * cxx17*
  - libabseil >=20250127.1,<20250128.0a0
  - libcurl >=8.13.0,<9.0a0
  - libgrpc >=1.71.0,<1.72.0a0
  - libopentelemetry-cpp-headers 1.20.0 ha770c72_0
  - libprotobuf >=5.29.3,<5.29.4.0a0
  - libzlib >=1.3.1,<2.0a0
  - nlohmann_json
  - prometheus-cpp >=1.3.0,<1.4.0a0
  constrains:
  - cpp-opentelemetry-sdk =1.20.0
  license: Apache-2.0
  license_family: APACHE
  purls: []
  size: 850005
  timestamp: 1743991616571
- conda: https://conda.anaconda.org/conda-forge/linux-64/libopentelemetry-cpp-headers-1.20.0-ha770c72_0.conda
  sha256: 3a6796711f53c6c3596ff36d5d25aad3c567f6623bc48698037db95d0ce4fd05
  md5: 96806e6c31dc89253daff2134aeb58f3
  license: Apache-2.0
  license_family: APACHE
  purls: []
  size: 347071
  timestamp: 1743991580676
- conda: https://conda.anaconda.org/conda-forge/linux-64/libparquet-20.0.0-h081d1f1_3_cpu.conda
  build_number: 3
  sha256: 113148922c560f8d2dd2a1684782dc4f93f44637dacd97fce1ad5e5af9dd10e9
  md5: f15cc1214c08019be884e3defd93e000
  depends:
  - __glibc >=2.17,<3.0.a0
  - libarrow 20.0.0 hebdba27_3_cpu
  - libgcc >=13
  - libstdcxx >=13
  - libthrift >=0.21.0,<0.21.1.0a0
  - openssl >=3.5.0,<4.0a0
  license: Apache-2.0
  license_family: APACHE
  purls: []
  size: 1243008
  timestamp: 1746920646524
- conda: https://conda.anaconda.org/conda-forge/linux-64/libpciaccess-0.18-hd590300_0.conda
  sha256: c0a30ac74eba66ea76a4f0a39acc7833f5ed783a632ca3bb6665b2d81aabd2fb
  md5: 48f4330bfcd959c3cfb704d424903c82
  depends:
  - libgcc-ng >=12
  license: MIT
  license_family: MIT
  purls: []
  size: 28361
  timestamp: 1707101388552
- conda: https://conda.anaconda.org/conda-forge/linux-64/libpng-1.6.47-h943b412_0.conda
  sha256: 23367d71da58c9a61c8cbd963fcffb92768d4ae5ffbef9a47cdf1f54f98c5c36
  md5: 55199e2ae2c3651f6f9b2a447b47bdc9
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - libzlib >=1.3.1,<2.0a0
  license: zlib-acknowledgement
  purls: []
  size: 288701
  timestamp: 1739952993639
- conda: https://conda.anaconda.org/conda-forge/linux-64/libpq-17.5-h27ae623_0.conda
  sha256: 2dbcef0db82e0e7b6895b6c0dadd3d36c607044c40290c7ca10656f3fca3166f
  md5: 6458be24f09e1b034902ab44fe9de908
  depends:
  - __glibc >=2.17,<3.0.a0
  - icu >=75.1,<76.0a0
  - krb5 >=1.21.3,<1.22.0a0
  - libgcc >=13
  - openldap >=2.6.9,<2.7.0a0
  - openssl >=3.5.0,<4.0a0
  license: PostgreSQL
  purls: []
  size: 2680582
  timestamp: 1746743259857
- conda: https://conda.anaconda.org/conda-forge/linux-64/libprotobuf-5.29.3-h501fc15_1.conda
  sha256: 691af28446345674c6b3fb864d0e1a1574b6cc2f788e0f036d73a6b05dcf81cf
  md5: edb86556cf4a0c133e7932a1597ff236
  depends:
  - __glibc >=2.17,<3.0.a0
  - libabseil * cxx17*
  - libabseil >=20250127.1,<20250128.0a0
  - libgcc >=13
  - libstdcxx >=13
  - libzlib >=1.3.1,<2.0a0
  license: BSD-3-Clause
  license_family: BSD
  purls: []
  size: 3358788
  timestamp: 1745159546868
- conda: https://conda.anaconda.org/conda-forge/linux-64/libre2-11-2024.07.02-hba17884_3.conda
  sha256: 392ec1e49370eb03270ffd4cc8d727f8e03e1e3a92b12f10c53f396ae4554668
  md5: 545e93a513c10603327c76c15485e946
  depends:
  - __glibc >=2.17,<3.0.a0
  - libabseil * cxx17*
  - libabseil >=20250127.0,<20250128.0a0
  - libgcc >=13
  - libstdcxx >=13
  constrains:
  - re2 2024.07.02.*
  license: BSD-3-Clause
  license_family: BSD
  purls: []
  size: 210073
  timestamp: 1741121121238
- conda: https://conda.anaconda.org/conda-forge/linux-64/librsvg-2.58.4-h49af25d_2.conda
  sha256: 475013475a3209c24a82f9e80c545d56ccca2fa04df85952852f3d73caa38ff9
  md5: b9846db0abffb09847e2cb0fec4b4db6
  depends:
  - __glibc >=2.17,<3.0.a0
  - cairo >=1.18.2,<2.0a0
  - freetype >=2.12.1,<3.0a0
  - gdk-pixbuf >=2.42.12,<3.0a0
  - harfbuzz >=10.1.0,<11.0a0
  - libgcc >=13
  - libglib >=2.82.2,<3.0a0
  - libpng >=1.6.44,<1.7.0a0
  - libxml2 >=2.13.5,<2.14.0a0
  - pango >=1.54.0,<2.0a0
  constrains:
  - __glibc >=2.17
  license: LGPL-2.1-or-later
  purls: []
  size: 6342757
  timestamp: 1734902068235
- conda: https://conda.anaconda.org/conda-forge/linux-64/libsodium-1.0.20-h4ab18f5_0.conda
  sha256: 0105bd108f19ea8e6a78d2d994a6d4a8db16d19a41212070d2d1d48a63c34161
  md5: a587892d3c13b6621a6091be690dbca2
  depends:
  - libgcc-ng >=12
  license: ISC
  purls: []
  size: 205978
  timestamp: 1716828628198
- conda: https://conda.anaconda.org/conda-forge/linux-64/libsqlite-3.49.2-hee588c1_0.conda
  sha256: 525d4a0e24843f90b3ff1ed733f0a2e408aa6dd18b9d4f15465595e078e104a2
  md5: 93048463501053a00739215ea3f36324
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - libzlib >=1.3.1,<2.0a0
  license: Unlicense
  purls: []
  size: 916313
  timestamp: 1746637007836
- conda: https://conda.anaconda.org/conda-forge/linux-64/libssh2-1.11.1-hcf80075_0.conda
  sha256: fa39bfd69228a13e553bd24601332b7cfeb30ca11a3ca50bb028108fe90a7661
  md5: eecce068c7e4eddeb169591baac20ac4
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - libzlib >=1.3.1,<2.0a0
  - openssl >=3.5.0,<4.0a0
  license: BSD-3-Clause
  license_family: BSD
  purls: []
  size: 304790
  timestamp: 1745608545575
- conda: https://conda.anaconda.org/conda-forge/linux-64/libstdcxx-15.1.0-h8f9b012_2.conda
  sha256: 6ae3d153e78f6069d503d9309f2cac6de5b93d067fc6433160a4c05226a5dad4
  md5: 1cb1c67961f6dd257eae9e9691b341aa
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc 15.1.0 h767d61c_2
  license: GPL-3.0-only WITH GCC-exception-3.1
  license_family: GPL
  purls: []
  size: 3902355
  timestamp: 1746642227493
- conda: https://conda.anaconda.org/conda-forge/linux-64/libstdcxx-ng-15.1.0-h4852527_2.conda
  sha256: 11bea86e11de7d6bce87589197a383344df3fa0a3552dab7e931785ff1159a5b
  md5: 9d2072af184b5caa29492bf2344597bb
  depends:
  - libstdcxx 15.1.0 h8f9b012_2
  license: GPL-3.0-only WITH GCC-exception-3.1
  license_family: GPL
  purls: []
  size: 34647
  timestamp: 1746642266826
- conda: https://conda.anaconda.org/conda-forge/linux-64/libthrift-0.21.0-h0e7cc3e_0.conda
  sha256: ebb395232973c18745b86c9a399a4725b2c39293c9a91b8e59251be013db42f0
  md5: dcb95c0a98ba9ff737f7ae482aef7833
  depends:
  - __glibc >=2.17,<3.0.a0
  - libevent >=2.1.12,<2.1.13.0a0
  - libgcc >=13
  - libstdcxx >=13
  - libzlib >=1.3.1,<2.0a0
  - openssl >=3.3.2,<4.0a0
  license: Apache-2.0
  license_family: APACHE
  purls: []
  size: 425773
  timestamp: 1727205853307
- conda: https://conda.anaconda.org/conda-forge/linux-64/libtiff-4.7.0-hf01ce69_5.conda
  sha256: 7fa6ddac72e0d803bb08e55090a8f2e71769f1eb7adbd5711bdd7789561601b1
  md5: e79a094918988bb1807462cd42c83962
  depends:
  - __glibc >=2.17,<3.0.a0
  - lerc >=4.0.0,<5.0a0
  - libdeflate >=1.24,<1.25.0a0
  - libgcc >=13
  - libjpeg-turbo >=3.1.0,<4.0a0
  - liblzma >=5.8.1,<6.0a0
  - libstdcxx >=13
  - libwebp-base >=1.5.0,<2.0a0
  - libzlib >=1.3.1,<2.0a0
  - zstd >=1.5.7,<1.6.0a0
  license: HPND
  purls: []
  size: 429575
  timestamp: 1747067001268
- conda: https://conda.anaconda.org/conda-forge/linux-64/libtorch-2.7.0-cpu_mkl_hf6ddc5a_100.conda
  sha256: 7b6178464b02d65c4af92086c71b79e5c2b7fc1500c1547334a4755e6e92d8a9
  md5: 6bdda0b10852c6d03b030bab7ec251f0
  depends:
  - __glibc >=2.17,<3.0.a0
  - _openmp_mutex * *_llvm
  - _openmp_mutex >=4.5
  - libabseil * cxx17*
  - libabseil >=20250127.1,<20250128.0a0
  - libblas * *mkl
  - libcblas >=3.9.0,<4.0a0
  - libgcc >=13
  - libprotobuf >=5.29.3,<5.29.4.0a0
  - libstdcxx >=13
  - libuv >=1.50.0,<2.0a0
  - libzlib >=1.3.1,<2.0a0
  - llvm-openmp >=20.1.4
  - mkl >=2024.2.2,<2025.0a0
  - sleef >=3.8,<4.0a0
  constrains:
  - pytorch-gpu <0.0a0
  - pytorch 2.7.0 cpu_mkl_*_100
  - pytorch-cpu 2.7.0
  license: BSD-3-Clause
  license_family: BSD
  purls: []
  size: 55565925
  timestamp: 1746256872466
- conda: https://conda.anaconda.org/conda-forge/linux-64/libutf8proc-2.10.0-h4c51ac1_0.conda
  sha256: 8e41563ee963bf8ded06da45f4e70bf42f913cb3c2e79364eb3218deffa3cd74
  md5: aeccfff2806ae38430638ffbb4be9610
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  license: MIT
  license_family: MIT
  purls: []
  size: 82745
  timestamp: 1737244366901
- conda: https://conda.anaconda.org/conda-forge/linux-64/libuuid-2.38.1-h0b41bf4_0.conda
  sha256: 787eb542f055a2b3de553614b25f09eefb0a0931b0c87dbcce6efdfd92f04f18
  md5: 40b61aab5c7ba9ff276c41cfffe6b80b
  depends:
  - libgcc-ng >=12
  license: BSD-3-Clause
  license_family: BSD
  purls: []
  size: 33601
  timestamp: 1680112270483
- conda: https://conda.anaconda.org/conda-forge/linux-64/libuv-1.50.0-hb9d3cd8_0.conda
  sha256: b4a8890023902aef9f1f33e3e35603ad9c2f16c21fdb58e968fa6c1bd3e94c0b
  md5: 771ee65e13bc599b0b62af5359d80169
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  license: MIT
  license_family: MIT
  purls: []
  size: 891272
  timestamp: 1737016632446
- conda: https://conda.anaconda.org/conda-forge/linux-64/libwebp-1.5.0-hae8dbeb_0.conda
  sha256: b6a67495d954f8d7287aa20e64e98178f5326f0be4ce638b995dabd5153b52f7
  md5: bb895ca27e7e33ab7a7c2c63529ce1e0
  depends:
  - __glibc >=2.17,<3.0.a0
  - giflib >=5.2.2,<5.3.0a0
  - libgcc >=13
  - libjpeg-turbo >=3.0.0,<4.0a0
  - libpng >=1.6.44,<1.7.0a0
  - libtiff >=4.7.0,<4.8.0a0
  - libwebp-base 1.5.0.*
  - libwebp-base >=1.5.0,<2.0a0
  license: BSD-3-Clause
  license_family: BSD
  purls: []
  size: 92320
  timestamp: 1734956081433
- conda: https://conda.anaconda.org/conda-forge/linux-64/libwebp-base-1.5.0-h851e524_0.conda
  sha256: c45283fd3e90df5f0bd3dbcd31f59cdd2b001d424cf30a07223655413b158eaf
  md5: 63f790534398730f59e1b899c3644d4a
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  constrains:
  - libwebp 1.5.0
  license: BSD-3-Clause
  license_family: BSD
  purls: []
  size: 429973
  timestamp: 1734777489810
- conda: https://conda.anaconda.org/conda-forge/linux-64/libxcb-1.17.0-h8a09558_0.conda
  sha256: 666c0c431b23c6cec6e492840b176dde533d48b7e6fb8883f5071223433776aa
  md5: 92ed62436b625154323d40d5f2f11dd7
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - pthread-stubs
  - xorg-libxau >=1.0.11,<2.0a0
  - xorg-libxdmcp
  license: MIT
  license_family: MIT
  purls: []
  size: 395888
  timestamp: 1727278577118
- conda: https://conda.anaconda.org/conda-forge/linux-64/libxcrypt-4.4.36-hd590300_1.conda
  sha256: 6ae68e0b86423ef188196fff6207ed0c8195dd84273cb5623b85aa08033a410c
  md5: 5aa797f8787fe7a17d1b0821485b5adc
  depends:
  - libgcc-ng >=12
  license: LGPL-2.1-or-later
  purls: []
  size: 100393
  timestamp: 1702724383534
- conda: https://conda.anaconda.org/conda-forge/linux-64/libxkbcommon-1.10.0-h65c71a3_0.conda
  sha256: a8043a46157511b3ceb6573a99952b5c0232313283f2d6a066cec7c8dcaed7d0
  md5: fedf6bfe5d21d21d2b1785ec00a8889a
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - libstdcxx >=13
  - libxcb >=1.17.0,<2.0a0
  - libxml2 >=2.13.8,<2.14.0a0
  - xkeyboard-config
  - xorg-libxau >=1.0.12,<2.0a0
  license: MIT/X11 Derivative
  license_family: MIT
  purls: []
  size: 707156
  timestamp: 1747911059945
- conda: https://conda.anaconda.org/conda-forge/linux-64/libxml2-2.13.8-h4bc477f_0.conda
  sha256: b0b3a96791fa8bb4ec030295e8c8bf2d3278f33c0f9ad540e73b5e538e6268e7
  md5: 14dbe05b929e329dbaa6f2d0aa19466d
  depends:
  - __glibc >=2.17,<3.0.a0
  - icu >=75.1,<76.0a0
  - libgcc >=13
  - libiconv >=1.18,<2.0a0
  - liblzma >=5.8.1,<6.0a0
  - libzlib >=1.3.1,<2.0a0
  license: MIT
  license_family: MIT
  purls: []
  size: 690864
  timestamp: 1746634244154
- conda: https://conda.anaconda.org/conda-forge/linux-64/libxslt-1.1.39-h76b75d6_0.conda
  sha256: 684e9b67ef7b9ca0ca993762eeb39705ec58e2e7f958555c758da7ef416db9f3
  md5: e71f31f8cfb0a91439f2086fc8aa0461
  depends:
  - libgcc-ng >=12
  - libxml2 >=2.12.1,<2.14.0a0
  license: MIT
  license_family: MIT
  purls: []
  size: 254297
  timestamp: 1701628814990
- conda: https://conda.anaconda.org/conda-forge/linux-64/libzlib-1.3.1-hb9d3cd8_2.conda
  sha256: d4bfe88d7cb447768e31650f06257995601f89076080e76df55e3112d4e47dc4
  md5: edb0dca6bc32e4f4789199455a1dbeb8
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  constrains:
  - zlib 1.3.1 *_2
  license: Zlib
  license_family: Other
  purls: []
  size: 60963
  timestamp: 1727963148474
- conda: https://conda.anaconda.org/conda-forge/linux-64/llvm-openmp-20.1.5-h024ca30_0.conda
  sha256: 646907391a8d744508049ef7bd76653d59480b061a3a76ce047064f2923b6f84
  md5: 86f58be65a51d62ccc06cacfd83ff987
  depends:
  - __glibc >=2.17,<3.0.a0
  constrains:
  - openmp 20.1.5|20.1.5.*
  license: Apache-2.0 WITH LLVM-exception
  license_family: APACHE
  purls: []
  size: 3193511
  timestamp: 1747367181459
- conda: https://conda.anaconda.org/conda-forge/linux-64/lz4-c-1.10.0-h5888daf_1.conda
  sha256: 47326f811392a5fd3055f0f773036c392d26fdb32e4d8e7a8197eed951489346
  md5: 9de5350a85c4a20c685259b889aa6393
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - libstdcxx >=13
  license: BSD-2-Clause
  license_family: BSD
  purls: []
  size: 167055
  timestamp: 1733741040117
- conda: https://conda.anaconda.org/conda-forge/linux-64/lzo-2.10-hd590300_1001.conda
  sha256: 88433b98a9dd9da315400e7fb9cd5f70804cb17dca8b1c85163a64f90f584126
  md5: ec7398d21e2651e0dcb0044d03b9a339
  depends:
  - libgcc-ng >=12
  license: GPL-2.0-or-later
  license_family: GPL2
  purls: []
  size: 171416
  timestamp: 1713515738503
- conda: https://conda.anaconda.org/conda-forge/linux-64/marisa-trie-1.2.1-py310hf71b8c6_0.conda
  sha256: e950b0522dfd73a48941a8acf642f1e9d8d46f6eefe06b85c3c13c522e447963
  md5: 0cd2f87506e58216ea26e77ac91fbcd6
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - libstdcxx >=13
  - python >=3.10,<3.11.0a0
  - python_abi 3.10.* *_cp310
  license: MIT
  license_family: MIT
  purls:
  - pkg:pypi/marisa-trie?source=hash-mapping
  size: 182287
  timestamp: 1736179130891
- conda: https://conda.anaconda.org/conda-forge/noarch/markdown-it-py-3.0.0-pyhd8ed1ab_1.conda
  sha256: 0fbacdfb31e55964152b24d5567e9a9996e1e7902fb08eb7d91b5fd6ce60803a
  md5: fee3164ac23dfca50cfcc8b85ddefb81
  depends:
  - mdurl >=0.1,<1
  - python >=3.9
  license: MIT
  license_family: MIT
  purls:
  - pkg:pypi/markdown-it-py?source=hash-mapping
  size: 64430
  timestamp: 1733250550053
- conda: https://conda.anaconda.org/conda-forge/linux-64/markupsafe-3.0.2-py310h89163eb_1.conda
  sha256: 0bed20ec27dcbcaf04f02b2345358e1161fb338f8423a4ada1cf0f4d46918741
  md5: 8ce3f0332fd6de0d737e2911d329523f
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - python >=3.10,<3.11.0a0
  - python_abi 3.10.* *_cp310
  constrains:
  - jinja2 >=3.0.0
  license: BSD-3-Clause
  license_family: BSD
  purls:
  - pkg:pypi/markupsafe?source=hash-mapping
  size: 23091
  timestamp: 1733219814479
- conda: https://conda.anaconda.org/conda-forge/linux-64/matplotlib-3.10.3-py310hff52083_0.conda
  sha256: 9cd37f1d0a833d612752fce297808480d9b0f65f294d146761371e16cb4ae0ec
  md5: 4162a00ddf1d805557aff34ddf113f46
  depends:
  - matplotlib-base >=3.10.3,<3.10.4.0a0
  - pyside6 >=6.7.2
  - python >=3.10,<3.11.0a0
  - python_abi 3.10.* *_cp310
  - tornado >=5
  license: PSF-2.0
  license_family: PSF
  purls: []
  size: 17367
  timestamp: 1746820810201
- conda: https://conda.anaconda.org/conda-forge/linux-64/matplotlib-base-3.10.3-py310h68603db_0.conda
  sha256: e9913cc14bc84844279a4a8db1b65683054db7909b92327ea7d848eaedda7689
  md5: 50084ca38bf28440e2762966bac143fc
  depends:
  - __glibc >=2.17,<3.0.a0
  - contourpy >=1.0.1
  - cycler >=0.10
  - fonttools >=4.22.0
  - freetype
  - kiwisolver >=1.3.1
  - libfreetype >=2.13.3
  - libfreetype6 >=2.13.3
  - libgcc >=13
  - libstdcxx >=13
  - numpy >=1.19,<3
  - numpy >=1.23
  - packaging >=20.0
  - pillow >=8
  - pyparsing >=2.3.1
  - python >=3.10,<3.11.0a0
  - python-dateutil >=2.7
  - python_abi 3.10.* *_cp310
  - qhull >=2020.2,<2020.3.0a0
  - tk >=8.6.13,<8.7.0a0
  license: PSF-2.0
  license_family: PSF
  purls:
  - pkg:pypi/matplotlib?source=hash-mapping
  size: 7326767
  timestamp: 1746820783918
- conda: https://conda.anaconda.org/conda-forge/noarch/matplotlib-inline-0.1.7-pyhd8ed1ab_1.conda
  sha256: 69b7dc7131703d3d60da9b0faa6dd8acbf6f6c396224cf6aef3e855b8c0c41c6
  md5: af6ab708897df59bd6e7283ceab1b56b
  depends:
  - python >=3.9
  - traitlets
  license: BSD-3-Clause
  license_family: BSD
  purls:
  - pkg:pypi/matplotlib-inline?source=hash-mapping
  size: 14467
  timestamp: 1733417051523
- conda: https://conda.anaconda.org/conda-forge/noarch/mdurl-0.1.2-pyhd8ed1ab_1.conda
  sha256: 78c1bbe1723449c52b7a9df1af2ee5f005209f67e40b6e1d3c7619127c43b1c7
  md5: 592132998493b3ff25fd7479396e8351
  depends:
  - python >=3.9
  license: MIT
  license_family: MIT
  purls:
  - pkg:pypi/mdurl?source=hash-mapping
  size: 14465
  timestamp: 1733255681319
- conda: https://conda.anaconda.org/conda-forge/noarch/mistune-3.1.3-pyh29332c3_0.conda
  sha256: a67484d7dd11e815a81786580f18b6e4aa2392f292f29183631a6eccc8dc37b3
  md5: 7ec6576e328bc128f4982cd646eeba85
  depends:
  - python >=3.9
  - typing_extensions
  - python
  license: BSD-3-Clause
  license_family: BSD
  purls:
  - pkg:pypi/mistune?source=hash-mapping
  size: 72749
  timestamp: 1742402716323
- conda: https://conda.anaconda.org/conda-forge/linux-64/mkl-2024.2.2-ha957f24_16.conda
  sha256: 77906b0acead8f86b489da46f53916e624897338770dbf70b04b8f673c9273c1
  md5: 1459379c79dda834673426504d52b319
  depends:
  - _openmp_mutex * *_llvm
  - _openmp_mutex >=4.5
  - llvm-openmp >=19.1.2
  - tbb 2021.*
  license: LicenseRef-IntelSimplifiedSoftwareOct2022
  license_family: Proprietary
  purls: []
  size: 124718448
  timestamp: 1730231808335
- conda: https://conda.anaconda.org/conda-forge/linux-64/mpc-1.3.1-h24ddda3_1.conda
  sha256: 1bf794ddf2c8b3a3e14ae182577c624fa92dea975537accff4bc7e5fea085212
  md5: aa14b9a5196a6d8dd364164b7ce56acf
  depends:
  - __glibc >=2.17,<3.0.a0
  - gmp >=6.3.0,<7.0a0
  - libgcc >=13
  - mpfr >=4.2.1,<5.0a0
  license: LGPL-3.0-or-later
  license_family: LGPL
  purls: []
  size: 116777
  timestamp: 1725629179524
- conda: https://conda.anaconda.org/conda-forge/linux-64/mpfr-4.2.1-h90cbb55_3.conda
  sha256: f25d2474dd557ca66c6231c8f5ace5af312efde1ba8290a6ea5e1732a4e669c0
  md5: 2eeb50cab6652538eee8fc0bc3340c81
  depends:
  - __glibc >=2.17,<3.0.a0
  - gmp >=6.3.0,<7.0a0
  - libgcc >=13
  license: LGPL-3.0-only
  license_family: LGPL
  purls: []
  size: 634751
  timestamp: 1725746740014
- conda: https://conda.anaconda.org/conda-forge/noarch/mpmath-1.3.0-pyhd8ed1ab_1.conda
  sha256: 7d7aa3fcd6f42b76bd711182f3776a02bef09a68c5f117d66b712a6d81368692
  md5: 3585aa87c43ab15b167b574cd73b057b
  depends:
  - python >=3.9
  license: BSD-3-Clause
  license_family: BSD
  purls:
  - pkg:pypi/mpmath?source=hash-mapping
  size: 439705
  timestamp: 1733302781386
- conda: https://conda.anaconda.org/conda-forge/linux-64/multidict-6.4.4-py310h89163eb_0.conda
  sha256: 9772de87426ca5bef0e4fff61a68fcf7ea8f5637b4b7745c7e8f1e3edbf2f29d
  md5: e0ae757607b558453f9a9a8b74573710
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - python >=3.10,<3.11.0a0
  - python_abi 3.10.* *_cp310
  - typing-extensions
  license: Apache-2.0
  license_family: APACHE
  purls:
  - pkg:pypi/multidict?source=hash-mapping
  size: 80809
  timestamp: 1747722554027
- conda: https://conda.anaconda.org/conda-forge/linux-64/multiprocess-0.70.15-py310h2372a71_1.conda
  sha256: 2ba19287b111304701466b3dd3b0329f452be20e4aecf643a047b88ee3cde99a
  md5: 81644426a6bb61245ffadc49b5aa9a14
  depends:
  - dill >=0.3.6
  - libgcc-ng >=12
  - python >=3.10,<3.11.0a0
  - python_abi 3.10.* *_cp310
  license: BSD-3-Clause
  license_family: BSD
  purls:
  - pkg:pypi/multiprocess?source=hash-mapping
  size: 242973
  timestamp: 1695458980978
- conda: https://conda.anaconda.org/conda-forge/noarch/munkres-1.1.4-pyh9f0ad1d_0.tar.bz2
  sha256: f86fb22b58e93d04b6f25e0d811b56797689d598788b59dcb47f59045b568306
  md5: 2ba8498c1018c1e9c61eb99b973dfe19
  depends:
  - python
  license: Apache-2.0
  license_family: Apache
  purls:
  - pkg:pypi/munkres?source=hash-mapping
  size: 12452
  timestamp: 1600387789153
- conda: https://conda.anaconda.org/conda-forge/linux-64/mupdf-1.25.2-py310ha3268d5_0.conda
  sha256: b9da902eaa3047a8728fa0d665c80209af9b89e7d7aeaecd5be1ee8499f14170
  md5: 7ab322fe98f2191eff27ce06b394982b
  depends:
  - __glibc >=2.17,<3.0.a0
  - curl
  - freeglut >=3.2.2,<4.0a0
  - freetype >=2.12.1,<3.0a0
  - harfbuzz >=10.1.0,<11.0a0
  - jbig2dec >=0.18,<0.19.0a0
  - leptonica >=1.83.1,<1.84.0a0
  - libgcc >=13
  - libgumbo >=1.0.0,<1.1.0a0
  - libstdcxx >=13
  - libzlib >=1.3.1,<2.0a0
  - openjpeg >=2.5.3,<3.0a0
  - python >=3.10,<3.11.0a0
  - python_abi 3.10.* *_cp310
  - tesseract >=5.5.0,<5.5.1.0a0
  - xorg-libx11
  - xorg-libxext
  - xorg-libxfixes >=6.0.1,<7.0a0
  license: AGPL-3.0-only
  license_family: GPL
  purls: []
  size: 30337450
  timestamp: 1733935947900
- conda: https://conda.anaconda.org/conda-forge/linux-64/murmurhash-1.0.10-py310hf71b8c6_2.conda
  sha256: 81cad7e594c1d128ce5509465dec31ab649a5f0d5d454f97d3d57d26d550f810
  md5: 46cbd56e86bd1bbe9c21ab98c5bd173a
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - libstdcxx >=13
  - python >=3.10,<3.11.0a0
  - python_abi 3.10.* *_cp310
  license: MIT
  license_family: MIT
  purls:
  - pkg:pypi/murmurhash?source=hash-mapping
  size: 35318
  timestamp: 1728933044820
- conda: https://conda.anaconda.org/conda-forge/linux-64/mysql-common-9.0.1-h266115a_6.conda
  sha256: 9c2e3f9e9883e4b8d7e9e6abf7b235dc00bdcd5ef66640a360464a9f5756294d
  md5: 94116b69829e90b72d566e64421e1bff
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - libstdcxx >=13
  - openssl >=3.4.1,<4.0a0
  license: GPL-2.0-or-later
  license_family: GPL
  purls: []
  size: 616215
  timestamp: 1744124836761
- conda: https://conda.anaconda.org/conda-forge/linux-64/mysql-libs-9.0.1-he0572af_6.conda
  sha256: 274467a602944d12722f757f660ad034de6f5f5d7d2ea1b913ef6fd836c1b8ce
  md5: 9802ae6d20982f42c0f5d69008988763
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - libstdcxx >=13
  - libzlib >=1.3.1,<2.0a0
  - mysql-common 9.0.1 h266115a_6
  - openssl >=3.4.1,<4.0a0
  - zstd >=1.5.7,<1.6.0a0
  license: GPL-2.0-or-later
  license_family: GPL
  purls: []
  size: 1369369
  timestamp: 1744124916632
- conda: https://conda.anaconda.org/conda-forge/noarch/narwhals-1.40.0-pyhe01879c_0.conda
  sha256: eb728d7b0332dc63c2b9331d8e198896001ef4bb07ce01e4480d5558bbff6500
  md5: 578a7350e50600779eac971be60cafdf
  depends:
  - python >=3.9
  - python
  license: MIT
  license_family: MIT
  purls:
  - pkg:pypi/narwhals?source=compressed-mapping
  size: 225140
  timestamp: 1747661220649
- conda: https://conda.anaconda.org/conda-forge/noarch/nbclient-0.10.2-pyhd8ed1ab_0.conda
  sha256: a20cff739d66c2f89f413e4ba4c6f6b59c50d5c30b5f0d840c13e8c9c2df9135
  md5: 6bb0d77277061742744176ab555b723c
  depends:
  - jupyter_client >=6.1.12
  - jupyter_core >=4.12,!=5.0.*
  - nbformat >=5.1
  - python >=3.8
  - traitlets >=5.4
  license: BSD-3-Clause
  license_family: BSD
  purls:
  - pkg:pypi/nbclient?source=hash-mapping
  size: 28045
  timestamp: 1734628936013
- conda: https://conda.anaconda.org/conda-forge/noarch/nbconvert-core-7.16.6-pyh29332c3_0.conda
  sha256: dcccb07c5a1acb7dc8be94330e62d54754c0e9c9cb2bb6865c8e3cfe44cf5a58
  md5: d24beda1d30748afcc87c429454ece1b
  depends:
  - beautifulsoup4
  - bleach-with-css !=5.0.0
  - defusedxml
  - importlib-metadata >=3.6
  - jinja2 >=3.0
  - jupyter_core >=4.7
  - jupyterlab_pygments
  - markupsafe >=2.0
  - mistune >=2.0.3,<4
  - nbclient >=0.5.0
  - nbformat >=5.7
  - packaging
  - pandocfilters >=1.4.1
  - pygments >=2.4.1
  - python >=3.9
  - traitlets >=5.1
  - python
  constrains:
  - pandoc >=2.9.2,<4.0.0
  - nbconvert ==7.16.6 *_0
  license: BSD-3-Clause
  license_family: BSD
  purls:
  - pkg:pypi/nbconvert?source=hash-mapping
  size: 200601
  timestamp: 1738067871724
- conda: https://conda.anaconda.org/conda-forge/noarch/nbformat-5.10.4-pyhd8ed1ab_1.conda
  sha256: 7a5bd30a2e7ddd7b85031a5e2e14f290898098dc85bea5b3a5bf147c25122838
  md5: bbe1963f1e47f594070ffe87cdf612ea
  depends:
  - jsonschema >=2.6
  - jupyter_core >=4.12,!=5.0.*
  - python >=3.9
  - python-fastjsonschema >=2.15
  - traitlets >=5.1
  license: BSD-3-Clause
  license_family: BSD
  purls:
  - pkg:pypi/nbformat?source=hash-mapping
  size: 100945
  timestamp: 1733402844974
- conda: https://conda.anaconda.org/conda-forge/linux-64/ncurses-6.5-h2d0b736_3.conda
  sha256: 3fde293232fa3fca98635e1167de6b7c7fda83caf24b9d6c91ec9eefb4f4d586
  md5: 47e340acb35de30501a76c7c799c41d7
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  license: X11 AND BSD-3-Clause
  purls: []
  size: 891641
  timestamp: 1738195959188
- conda: https://conda.anaconda.org/conda-forge/noarch/nest-asyncio-1.6.0-pyhd8ed1ab_1.conda
  sha256: bb7b21d7fd0445ddc0631f64e66d91a179de4ba920b8381f29b9d006a42788c0
  md5: 598fd7d4d0de2455fb74f56063969a97
  depends:
  - python >=3.9
  license: BSD-2-Clause
  license_family: BSD
  purls:
  - pkg:pypi/nest-asyncio?source=hash-mapping
  size: 11543
  timestamp: 1733325673691
- pypi: https://files.pythonhosted.org/packages/b9/54/dd730b32ea14ea797530a4479b2ed46a6fb250f682a9cfb997e968bf0261/networkx-3.4.2-py3-none-any.whl
  name: networkx
  version: 3.4.2
  sha256: df5d4365b724cf81b8c6a7312509d0c22386097011ad1abe274afd5e9d3bbc5f
  requires_dist:
  - numpy>=1.24 ; extra == 'default'
  - scipy>=1.10,!=1.11.0,!=1.11.1 ; extra == 'default'
  - matplotlib>=3.7 ; extra == 'default'
  - pandas>=2.0 ; extra == 'default'
  - changelist==0.5 ; extra == 'developer'
  - pre-commit>=3.2 ; extra == 'developer'
  - mypy>=1.1 ; extra == 'developer'
  - rtoml ; extra == 'developer'
  - sphinx>=7.3 ; extra == 'doc'
  - pydata-sphinx-theme>=0.15 ; extra == 'doc'
  - sphinx-gallery>=0.16 ; extra == 'doc'
  - numpydoc>=1.8.0 ; extra == 'doc'
  - pillow>=9.4 ; extra == 'doc'
  - texext>=0.6.7 ; extra == 'doc'
  - myst-nb>=1.1 ; extra == 'doc'
  - intersphinx-registry ; extra == 'doc'
  - osmnx>=1.9 ; extra == 'example'
  - momepy>=0.7.2 ; extra == 'example'
  - contextily>=1.6 ; extra == 'example'
  - seaborn>=0.13 ; extra == 'example'
  - cairocffi>=1.7 ; extra == 'example'
  - igraph>=0.11 ; extra == 'example'
  - scikit-learn>=1.5 ; extra == 'example'
  - lxml>=4.6 ; extra == 'extra'
  - pygraphviz>=1.14 ; extra == 'extra'
  - pydot>=3.0.1 ; extra == 'extra'
  - sympy>=1.10 ; extra == 'extra'
  - pytest>=7.2 ; extra == 'test'
  - pytest-cov>=4.0 ; extra == 'test'
  requires_python: '>=3.10'
- conda: https://conda.anaconda.org/conda-forge/noarch/networkx-3.4.2-pyh267e887_2.conda
  sha256: 39625cd0c9747fa5c46a9a90683b8997d8b9649881b3dc88336b13b7bdd60117
  md5: fd40bf7f7f4bc4b647dc8512053d9873
  depends:
  - python >=3.10
  - python
  constrains:
  - numpy >=1.24
  - scipy >=1.10,!=1.11.0,!=1.11.1
  - matplotlib >=3.7
  - pandas >=2.0
  license: BSD-3-Clause
  license_family: BSD
  purls: []
  size: 1265008
  timestamp: 1731521053408
- conda: https://conda.anaconda.org/conda-forge/linux-64/nlohmann_json-3.12.0-h3f2d84a_0.conda
  sha256: e2fc624d6f9b2f1b695b6be6b905844613e813aa180520e73365062683fe7b49
  md5: d76872d096d063e226482c99337209dc
  license: MIT
  license_family: MIT
  purls: []
  size: 135906
  timestamp: 1744445169928
- conda: https://conda.anaconda.org/conda-forge/noarch/nltk-3.9.1-pyhd8ed1ab_1.conda
  sha256: d53f74f0c15770945874dfc3da2c4eddfceed714825f3e421463031d3c3f6d77
  md5: 85fd21c82d46f871d3820c17270e575d
  depends:
  - click
  - joblib
  - python >=3.9
  - regex >=2021.8.3
  - tqdm
  license: Apache-2.0
  license_family: Apache
  purls:
  - pkg:pypi/nltk?source=hash-mapping
  size: 1056436
  timestamp: 1734310024052
- pypi: https://files.pythonhosted.org/packages/ed/9e/ba2d0b47b1c170270e67ed6a9761f5580cb59615388593e287c623a36e2d/nmslib-2.0.6.tar.gz
  name: nmslib
  version: 2.0.6
  sha256: 252f1355385e15c5c855a3f57cd8e48db8154c2a4edd8750956b8e66cf5c79ed
  requires_dist:
  - pybind11>=2.2.3
  - psutil
  - numpy>=1.10.0
- conda: https://conda.anaconda.org/conda-forge/noarch/notebook-7.4.2-pyhd8ed1ab_0.conda
  sha256: 3a22a07f337dc3177c0535ba28446a535195610adb467ef9b49dffcd120ddd44
  md5: 59a96e10c24dd3407ff3366e46a2f5b3
  depends:
  - jupyter_server >=2.4.0,<3
  - jupyterlab >=4.4.2,<4.5
  - jupyterlab_server >=2.27.1,<3
  - notebook-shim >=0.2,<0.3
  - python >=3.9
  - tornado >=6.2.0
  license: BSD-3-Clause
  license_family: BSD
  purls:
  - pkg:pypi/notebook?source=hash-mapping
  size: 10025449
  timestamp: 1746547731270
- conda: https://conda.anaconda.org/conda-forge/noarch/notebook-shim-0.2.4-pyhd8ed1ab_1.conda
  sha256: 7b920e46b9f7a2d2aa6434222e5c8d739021dbc5cc75f32d124a8191d86f9056
  md5: e7f89ea5f7ea9401642758ff50a2d9c1
  depends:
  - jupyter_server >=1.8,<3
  - python >=3.9
  license: BSD-3-Clause
  license_family: BSD
  purls:
  - pkg:pypi/notebook-shim?source=hash-mapping
  size: 16817
  timestamp: 1733408419340
- conda: https://conda.anaconda.org/conda-forge/linux-64/numpy-1.26.4-py310hb13e2d6_0.conda
  sha256: 028fe2ea8e915a0a032b75165f11747770326f3d767e642880540c60a3256425
  md5: 6593de64c935768b6bad3e19b3e978be
  depends:
  - libblas >=3.9.0,<4.0a0
  - libcblas >=3.9.0,<4.0a0
  - libgcc-ng >=12
  - liblapack >=3.9.0,<4.0a0
  - libstdcxx-ng >=12
  - python >=3.10,<3.11.0a0
  - python_abi 3.10.* *_cp310
  constrains:
  - numpy-base <0a0
  license: BSD-3-Clause
  license_family: BSD
  purls:
  - pkg:pypi/numpy?source=hash-mapping
  size: 7009070
  timestamp: 1707225917496
- conda: https://conda.anaconda.org/conda-forge/linux-64/openjpeg-2.5.3-h5fbd93e_0.conda
  sha256: 5bee706ea5ba453ed7fd9da7da8380dd88b865c8d30b5aaec14d2b6dd32dbc39
  md5: 9e5816bc95d285c115a3ebc2f8563564
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - libpng >=1.6.44,<1.7.0a0
  - libstdcxx >=13
  - libtiff >=4.7.0,<4.8.0a0
  - libzlib >=1.3.1,<2.0a0
  license: BSD-2-Clause
  license_family: BSD
  purls: []
  size: 342988
  timestamp: 1733816638720
- conda: https://conda.anaconda.org/conda-forge/linux-64/openldap-2.6.10-he970967_0.conda
  sha256: cb0b07db15e303e6f0a19646807715d28f1264c6350309a559702f4f34f37892
  md5: 2e5bf4f1da39c0b32778561c3c4e5878
  depends:
  - __glibc >=2.17,<3.0.a0
  - cyrus-sasl >=2.1.27,<3.0a0
  - krb5 >=1.21.3,<1.22.0a0
  - libgcc >=13
  - libstdcxx >=13
  - openssl >=3.5.0,<4.0a0
  license: OLDAP-2.8
  license_family: BSD
  purls: []
  size: 780253
  timestamp: 1748010165522
- conda: https://conda.anaconda.org/conda-forge/linux-64/openssl-3.5.0-h7b32b05_1.conda
  sha256: b4491077c494dbf0b5eaa6d87738c22f2154e9277e5293175ec187634bd808a0
  md5: de356753cfdbffcde5bb1e86e3aa6cd0
  depends:
  - __glibc >=2.17,<3.0.a0
  - ca-certificates
  - libgcc >=13
  license: Apache-2.0
  license_family: Apache
  purls: []
  size: 3117410
  timestamp: 1746223723843
- conda: https://conda.anaconda.org/conda-forge/linux-64/optree-0.15.0-py310h3788b33_0.conda
  sha256: 11365a375fccc7851e3f72da04f715a16ac60c7cf1ba4b29d5c205cb19136ee2
  md5: d3a48f9f60836a3aba05f3c87bd539a9
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - libstdcxx >=13
  - python >=3.10,<3.11.0a0
  - python_abi 3.10.* *_cp310
  - typing-extensions >=4.5
  license: Apache-2.0
  license_family: Apache
  purls:
  - pkg:pypi/optree?source=hash-mapping
  size: 386999
  timestamp: 1744034424188
- conda: https://conda.anaconda.org/conda-forge/linux-64/orc-2.1.2-h17f744e_0.conda
  sha256: f6ff644e27f42f2beb877773ba3adc1228dbb43530dbe9426dd672f3b847c7c5
  md5: ef7f9897a244b2023a066c22a1089ce4
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - libprotobuf >=5.29.3,<5.29.4.0a0
  - libstdcxx >=13
  - libzlib >=1.3.1,<2.0a0
  - lz4-c >=1.10.0,<1.11.0a0
  - snappy >=1.2.1,<1.3.0a0
  - tzdata
  - zstd >=1.5.7,<1.6.0a0
  license: Apache-2.0
  license_family: Apache
  purls: []
  size: 1242887
  timestamp: 1746604310927
- conda: https://conda.anaconda.org/conda-forge/noarch/overrides-7.7.0-pyhd8ed1ab_1.conda
  sha256: 1840bd90d25d4930d60f57b4f38d4e0ae3f5b8db2819638709c36098c6ba770c
  md5: e51f1e4089cad105b6cac64bd8166587
  depends:
  - python >=3.9
  - typing_utils
  license: Apache-2.0
  license_family: APACHE
  purls:
  - pkg:pypi/overrides?source=hash-mapping
  size: 30139
  timestamp: 1734587755455
- conda: https://conda.anaconda.org/conda-forge/noarch/packaging-25.0-pyh29332c3_1.conda
  sha256: 289861ed0c13a15d7bbb408796af4de72c2fe67e2bcb0de98f4c3fce259d7991
  md5: 58335b26c38bf4a20f399384c33cbcf9
  depends:
  - python >=3.8
  - python
  license: Apache-2.0
  license_family: APACHE
  purls:
  - pkg:pypi/packaging?source=compressed-mapping
  size: 62477
  timestamp: 1745345660407
- conda: https://conda.anaconda.org/conda-forge/linux-64/pandas-2.2.3-py310h5eaa309_3.conda
  sha256: 43fd80e57ebc9e0c00d169aafce533c49359174dea327a7fa8ca7454628a56f7
  md5: 07697a584fab513ce895c4511f7a2403
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - libstdcxx >=13
  - numpy >=1.19,<3
  - numpy >=1.22.4
  - python >=3.10,<3.11.0a0
  - python-dateutil >=2.8.2
  - python-tzdata >=2022.7
  - python_abi 3.10.* *_cp310
  - pytz >=2020.1
  constrains:
  - tabulate >=0.9.0
  - psycopg2 >=2.9.6
  - pyarrow >=10.0.1
  - numba >=0.56.4
  - xlsxwriter >=3.0.5
  - qtpy >=2.3.0
  - fastparquet >=2022.12.0
  - scipy >=1.10.0
  - sqlalchemy >=2.0.0
  - openpyxl >=3.1.0
  - html5lib >=1.1
  - s3fs >=2022.11.0
  - lxml >=4.9.2
  - odfpy >=1.4.1
  - pandas-gbq >=0.19.0
  - pytables >=3.8.0
  - fsspec >=2022.11.0
  - gcsfs >=2022.11.0
  - bottleneck >=1.3.6
  - zstandard >=0.19.0
  - pyxlsb >=1.0.10
  - xarray >=2022.12.0
  - pyreadstat >=1.2.0
  - python-calamine >=0.1.7
  - beautifulsoup4 >=4.11.2
  - xlrd >=2.0.1
  - tzdata >=2022.7
  - numexpr >=2.8.4
  - matplotlib >=3.6.3
  - blosc >=1.21.3
  - pyqt5 >=5.15.9
  license: BSD-3-Clause
  license_family: BSD
  purls:
  - pkg:pypi/pandas?source=hash-mapping
  size: 13029755
  timestamp: 1744430958318
- conda: https://conda.anaconda.org/conda-forge/noarch/pandocfilters-1.5.0-pyhd8ed1ab_0.tar.bz2
  sha256: 2bb9ba9857f4774b85900c2562f7e711d08dd48e2add9bee4e1612fbee27e16f
  md5: 457c2c8c08e54905d6954e79cb5b5db9
  depends:
  - python !=3.0,!=3.1,!=3.2,!=3.3
  license: BSD-3-Clause
  license_family: BSD
  purls:
  - pkg:pypi/pandocfilters?source=hash-mapping
  size: 11627
  timestamp: 1631603397334
- conda: https://conda.anaconda.org/conda-forge/linux-64/pango-1.56.3-h861ebed_0.conda
  sha256: 6bc073dc2759cb00bc9e94c7142acab58432245c6e04d1cef179e8afd3b58d6f
  md5: 6d853ca33bc46bce99ce16ccd83d0466
  depends:
  - __glibc >=2.17,<3.0.a0
  - cairo >=1.18.4,<2.0a0
  - fontconfig >=2.15.0,<3.0a0
  - fonts-conda-ecosystem
  - freetype >=2.13.3,<3.0a0
  - fribidi >=1.0.10,<2.0a0
  - harfbuzz >=10.4.0,<11.0a0
  - libexpat >=2.6.4,<3.0a0
  - libgcc >=13
  - libglib >=2.82.2,<3.0a0
  - libpng >=1.6.47,<1.7.0a0
  - libzlib >=1.3.1,<2.0a0
  license: LGPL-2.1-or-later
  purls: []
  size: 454553
  timestamp: 1742223788507
- conda: https://conda.anaconda.org/conda-forge/noarch/parso-0.8.4-pyhd8ed1ab_1.conda
  sha256: 17131120c10401a99205fc6fe436e7903c0fa092f1b3e80452927ab377239bcc
  md5: 5c092057b6badd30f75b06244ecd01c9
  depends:
  - python >=3.9
  license: MIT
  license_family: MIT
  purls:
  - pkg:pypi/parso?source=hash-mapping
  size: 75295
  timestamp: 1733271352153
- conda: https://conda.anaconda.org/conda-forge/linux-64/pcre2-10.44-hc749103_2.conda
  sha256: 09717569649d89caafbf32f6cda1e65aef86e5a86c053d30e4ce77fca8d27b68
  md5: 31614c73d7b103ef76faa4d83d261d34
  depends:
  - __glibc >=2.17,<3.0.a0
  - bzip2 >=1.0.8,<2.0a0
  - libgcc >=13
  - libzlib >=1.3.1,<2.0a0
  license: BSD-3-Clause
  license_family: BSD
  purls: []
  size: 956207
  timestamp: 1745931215744
- conda: https://conda.anaconda.org/conda-forge/noarch/pdfminer.six-20231228-pyhd8ed1ab_0.conda
  sha256: 3d57473e40c6fefca597eb394f1b25a6e2353e2597bae13137a70179ad4a3044
  md5: e61f6b78673ea5e0273592cfc76eb16d
  depends:
  - charset-normalizer >=2.0.0
  - cryptography >=36.0.0
  - python >=3.8
  license: MIT
  license_family: MIT
  purls:
  - pkg:pypi/pdfminer-six?source=hash-mapping
  size: 4547693
  timestamp: 1703854446264
- conda: https://conda.anaconda.org/conda-forge/noarch/pdfplumber-0.11.5-pyhd8ed1ab_0.conda
  sha256: ce50f08779fdc4a06c87a558762d555fafae8509dd9a0441a31302bd4892555d
  md5: e92b839c04e74a1ee9c2f0920d473d28
  depends:
  - pdfminer.six 20231228
  - pillow >=9.1
  - pypdfium2
  - python >=3.9
  - wand >=0.6.10
  license: MIT
  license_family: MIT
  purls:
  - pkg:pypi/pdfplumber?source=hash-mapping
  size: 55387
  timestamp: 1735886786575
- conda: https://conda.anaconda.org/conda-forge/noarch/pexpect-4.9.0-pyhd8ed1ab_1.conda
  sha256: 202af1de83b585d36445dc1fda94266697341994d1a3328fabde4989e1b3d07a
  md5: d0d408b1f18883a944376da5cf8101ea
  depends:
  - ptyprocess >=0.5
  - python >=3.9
  license: ISC
  purls:
  - pkg:pypi/pexpect?source=compressed-mapping
  size: 53561
  timestamp: 1733302019362
- conda: https://conda.anaconda.org/conda-forge/noarch/pickleshare-0.7.5-pyhd8ed1ab_1004.conda
  sha256: e2ac3d66c367dada209fc6da43e645672364b9fd5f9d28b9f016e24b81af475b
  md5: 11a9d1d09a3615fc07c3faf79bc0b943
  depends:
  - python >=3.9
  license: MIT
  license_family: MIT
  purls:
  - pkg:pypi/pickleshare?source=hash-mapping
  size: 11748
  timestamp: 1733327448200
- conda: https://conda.anaconda.org/conda-forge/linux-64/pillow-11.2.1-py310h7e6dc6c_0.conda
  sha256: 6f6ee76c94ed9334bba23da03cf72d71bc7d1c122dd294c2885cc33d76159b3d
  md5: 5645a243d90adb50909b9edc209d84fe
  depends:
  - __glibc >=2.17,<3.0.a0
  - lcms2 >=2.17,<3.0a0
  - libfreetype >=2.13.3
  - libfreetype6 >=2.13.3
  - libgcc >=13
  - libjpeg-turbo >=3.1.0,<4.0a0
  - libtiff >=4.7.0,<4.8.0a0
  - libwebp-base >=1.5.0,<2.0a0
  - libxcb >=1.17.0,<2.0a0
  - libzlib >=1.3.1,<2.0a0
  - openjpeg >=2.5.3,<3.0a0
  - python >=3.10,<3.11.0a0
  - python_abi 3.10.* *_cp310
  - tk >=8.6.13,<8.7.0a0
  license: HPND
  purls:
  - pkg:pypi/pillow?source=hash-mapping
  size: 42418256
  timestamp: 1746646380453
- conda: https://conda.anaconda.org/conda-forge/linux-64/pixman-0.46.0-h29eaf8c_0.conda
  sha256: 1330c3fd424fa2deec6a30678f235049c0ed1b0fad8d2d81ef995c9322d5e49a
  md5: d2f1c87d4416d1e7344cf92b1aaee1c4
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - libstdcxx >=13
  license: MIT
  license_family: MIT
  purls: []
  size: 398664
  timestamp: 1746011575217
- conda: https://conda.anaconda.org/conda-forge/linux-64/pkg-config-0.29.2-h4bc722e_1009.conda
  sha256: c9601efb1af5391317e04eca77c6fe4d716bf1ca1ad8da2a05d15cb7c28d7d4e
  md5: 1bee70681f504ea424fb07cdb090c001
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc-ng >=12
  license: GPL-2.0-or-later
  license_family: GPL
  purls: []
  size: 115175
  timestamp: 1720805894943
- conda: https://conda.anaconda.org/conda-forge/noarch/pkgutil-resolve-name-1.3.10-pyhd8ed1ab_2.conda
  sha256: adb2dde5b4f7da70ae81309cce6188ed3286ff280355cf1931b45d91164d2ad8
  md5: 5a5870a74432aa332f7d32180633ad05
  depends:
  - python >=3.9
  license: MIT AND PSF-2.0
  purls:
  - pkg:pypi/pkgutil-resolve-name?source=hash-mapping
  size: 10693
  timestamp: 1733344619659
- conda: https://conda.anaconda.org/conda-forge/noarch/platformdirs-4.3.8-pyhe01879c_0.conda
  sha256: 0f48999a28019c329cd3f6fd2f01f09fc32cc832f7d6bbe38087ddac858feaa3
  md5: 424844562f5d337077b445ec6b1398a7
  depends:
  - python >=3.9
  - python
  license: MIT
  license_family: MIT
  purls:
  - pkg:pypi/platformdirs?source=compressed-mapping
  size: 23531
  timestamp: 1746710438805
- conda: https://conda.anaconda.org/conda-forge/noarch/plotly-6.1.1-pyhd8ed1ab_0.conda
  sha256: 1334e6f606565c431714bcb95c1a5ab0ccd51da400d499318351e5c86aa331f7
  md5: b18343b5d779bf6d9205822c2cf76d6e
  depends:
  - narwhals >=1.15.1
  - packaging
  - python >=3.9
  constrains:
  - ipywidgets >=7.6
  license: MIT
  license_family: MIT
  purls:
  - pkg:pypi/plotly?source=hash-mapping
  size: 6683813
  timestamp: 1747787480892
- conda: https://conda.anaconda.org/conda-forge/noarch/pluggy-1.6.0-pyhd8ed1ab_0.conda
  sha256: a8eb555eef5063bbb7ba06a379fa7ea714f57d9741fe0efdb9442dbbc2cccbcc
  md5: 7da7ccd349dbf6487a7778579d2bb971
  depends:
  - python >=3.9
  license: MIT
  license_family: MIT
  purls:
  - pkg:pypi/pluggy?source=compressed-mapping
  size: 24246
  timestamp: 1747339794916
- conda: https://conda.anaconda.org/conda-forge/linux-64/preshed-3.0.9-py310hf71b8c6_2.conda
  sha256: a1e6948341b3447c850cdccdca0aca73d17cf2fc6a49faea1e603b0a84ab4bbc
  md5: 6898f43937b659d982febbdf75f436b6
  depends:
  - __glibc >=2.17,<3.0.a0
  - cymem >=2.0.2,<2.1.0
  - libgcc >=13
  - libstdcxx >=13
  - murmurhash >=0.28.0,<1.1.0
  - python >=3.10,<3.11.0a0
  - python_abi 3.10.* *_cp310
  license: MIT
  license_family: MIT
  purls:
  - pkg:pypi/preshed?source=hash-mapping
  size: 135109
  timestamp: 1728945816232
- conda: https://conda.anaconda.org/conda-forge/linux-64/prometheus-cpp-1.3.0-ha5d0236_0.conda
  sha256: 013669433eb447548f21c3c6b16b2ed64356f726b5f77c1b39d5ba17a8a4b8bc
  md5: a83f6a2fdc079e643237887a37460668
  depends:
  - __glibc >=2.17,<3.0.a0
  - libcurl >=8.10.1,<9.0a0
  - libgcc >=13
  - libstdcxx >=13
  - libzlib >=1.3.1,<2.0a0
  - zlib
  license: MIT
  license_family: MIT
  purls: []
  size: 199544
  timestamp: 1730769112346
- conda: https://conda.anaconda.org/conda-forge/noarch/prometheus_client-0.22.0-pyhd8ed1ab_0.conda
  sha256: 31d2fbd381d6ecc9f01d106da5e095104b235917a0b3c342887ee66ca0e85025
  md5: 7bfaef51c8364f6f5096a5a60bb83413
  depends:
  - python >=3.9
  license: Apache-2.0
  license_family: Apache
  purls:
  - pkg:pypi/prometheus-client?source=hash-mapping
  size: 53514
  timestamp: 1747487319612
- conda: https://conda.anaconda.org/conda-forge/noarch/prompt-toolkit-3.0.51-pyha770c72_0.conda
  sha256: ebc1bb62ac612af6d40667da266ff723662394c0ca78935340a5b5c14831227b
  md5: d17ae9db4dc594267181bd199bf9a551
  depends:
  - python >=3.9
  - wcwidth
  constrains:
  - prompt_toolkit 3.0.51
  license: BSD-3-Clause
  license_family: BSD
  purls:
  - pkg:pypi/prompt-toolkit?source=compressed-mapping
  size: 271841
  timestamp: 1744724188108
- conda: https://conda.anaconda.org/conda-forge/noarch/prompt_toolkit-3.0.51-hd8ed1ab_0.conda
  sha256: 936189f0373836c1c77cd2d6e71ba1e583e2d3920bf6d015e96ee2d729b5e543
  md5: 1e61ab85dd7c60e5e73d853ea035dc29
  depends:
  - prompt-toolkit >=3.0.51,<3.0.52.0a0
  license: BSD-3-Clause
  license_family: BSD
  purls:
  - pkg:pypi/prompt-toolkit?source=compressed-mapping
  size: 7182
  timestamp: 1744724189376
- conda: https://conda.anaconda.org/conda-forge/linux-64/propcache-0.3.1-py310h89163eb_0.conda
  sha256: 3dbf885bb1eb0e7a5eb3779165517abdb98d53871b36690041f6a366cc501738
  md5: e768486f2be3f50126bf9a54331221d1
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - python >=3.10,<3.11.0a0
  - python_abi 3.10.* *_cp310
  license: Apache-2.0
  license_family: APACHE
  purls:
  - pkg:pypi/propcache?source=hash-mapping
  size: 53576
  timestamp: 1744525075233
- conda: https://conda.anaconda.org/conda-forge/linux-64/psutil-7.0.0-py310ha75aee5_0.conda
  sha256: 31e46270c73cac2b24a7f3462ca03eb39f21cbfdb713b0d41eb61c00867eabe9
  md5: da7d592394ff9084a23f62a1186451a2
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - python >=3.10,<3.11.0a0
  - python_abi 3.10.* *_cp310
  license: BSD-3-Clause
  license_family: BSD
  purls:
  - pkg:pypi/psutil?source=compressed-mapping
  size: 354476
  timestamp: 1740663252954
- conda: https://conda.anaconda.org/conda-forge/linux-64/pthread-stubs-0.4-hb9d3cd8_1002.conda
  sha256: 9c88f8c64590e9567c6c80823f0328e58d3b1efb0e1c539c0315ceca764e0973
  md5: b3c17d95b5a10c6e64a21fa17573e70e
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  license: MIT
  license_family: MIT
  purls: []
  size: 8252
  timestamp: 1726802366959
- conda: https://conda.anaconda.org/conda-forge/noarch/ptyprocess-0.7.0-pyhd8ed1ab_1.conda
  sha256: a7713dfe30faf17508ec359e0bc7e0983f5d94682492469bd462cdaae9c64d83
  md5: 7d9daffbb8d8e0af0f769dbbcd173a54
  depends:
  - python >=3.9
  license: ISC
  purls:
  - pkg:pypi/ptyprocess?source=hash-mapping
  size: 19457
  timestamp: 1733302371990
- conda: https://conda.anaconda.org/conda-forge/noarch/pure_eval-0.2.3-pyhd8ed1ab_1.conda
  sha256: 71bd24600d14bb171a6321d523486f6a06f855e75e547fa0cb2a0953b02047f0
  md5: 3bfdfb8dbcdc4af1ae3f9a8eb3948f04
  depends:
  - python >=3.9
  license: MIT
  license_family: MIT
  purls:
  - pkg:pypi/pure-eval?source=hash-mapping
  size: 16668
  timestamp: 1733569518868
- conda: https://conda.anaconda.org/conda-forge/linux-64/pyarrow-20.0.0-py310hff52083_0.conda
  sha256: 8b2496e8c8c775af90ec91226266297bf655d31451a3dabe38568626c211c27a
  md5: e66347b55094a2cba9551ec4524fd136
  depends:
  - libarrow-acero 20.0.0.*
  - libarrow-dataset 20.0.0.*
  - libarrow-substrait 20.0.0.*
  - libparquet 20.0.0.*
  - pyarrow-core 20.0.0 *_0_*
  - python >=3.10,<3.11.0a0
  - python_abi 3.10.* *_cp310
  license: Apache-2.0
  license_family: APACHE
  purls: []
  size: 25830
  timestamp: 1746001231225
- conda: https://conda.anaconda.org/conda-forge/linux-64/pyarrow-core-20.0.0-py310hac404ae_0_cpu.conda
  sha256: c96fc4d91fbb1b133e35bdeb3ce96874e0a7a385331b3b7a2c298da9b98180bf
  md5: 01d158af8c0d9c2abc09a29ac39284a5
  depends:
  - __glibc >=2.17,<3.0.a0
  - libarrow 20.0.0.* *cpu
  - libgcc >=13
  - libstdcxx >=13
  - libzlib >=1.3.1,<2.0a0
  - python >=3.10,<3.11.0a0
  - python_abi 3.10.* *_cp310
  constrains:
  - apache-arrow-proc * cpu
  - numpy >=1.21,<3
  license: Apache-2.0
  license_family: APACHE
  purls:
  - pkg:pypi/pyarrow?source=hash-mapping
  size: 4644381
  timestamp: 1746000749034
- pypi: https://files.pythonhosted.org/packages/c8/f1/d6a797abb14f6283c0ddff96bbdd46937f64122b8c925cab503dd37f8214/pyasn1-0.6.1-py3-none-any.whl
  name: pyasn1
  version: 0.6.1
  sha256: 0d632f46f2ba09143da3a8afe9e33fb6f92fa2320ab7e886e2d0f7672af84629
  requires_python: '>=3.8'
- conda: https://conda.anaconda.org/conda-forge/noarch/pybind11-2.13.6-pyhc790b64_3.conda
  sha256: d429f6f255fbe49f09b9ae1377aa8cbc4d9285b8b220c17ae2ad9c4894c91317
  md5: 1594696beebf1ecb6d29a1136f859a74
  depends:
  - pybind11-global 2.13.6 *_3
  - python >=3.9
  constrains:
  - pybind11-abi ==4
  license: BSD-3-Clause
  license_family: BSD
  purls:
  - pkg:pypi/pybind11?source=hash-mapping
  size: 186821
  timestamp: 1747935138653
- conda: https://conda.anaconda.org/conda-forge/noarch/pybind11-global-2.13.6-pyh217bc35_3.conda
  sha256: c044cfcbe6ef0062d0960e9f9f0de5f8818cec84ed901219ff9994b9a9e57237
  md5: 730a5284e26d6bdb73332dafb26aec82
  depends:
  - __unix
  - python >=3.9
  constrains:
  - pybind11-abi ==4
  license: BSD-3-Clause
  license_family: BSD
  purls:
  - pkg:pypi/pybind11-global?source=hash-mapping
  size: 180116
  timestamp: 1747934418811
- conda: https://conda.anaconda.org/conda-forge/linux-64/pycairo-1.28.0-py310h516fd05_0.conda
  sha256: b15fd36f7881bb112ab381d65e29572bb4e9994fe63b722e174c371cdaab9135
  md5: b4491415028bd2e09ce4b2046822eb92
  depends:
  - __glibc >=2.17,<3.0.a0
  - cairo >=1.18.4,<2.0a0
  - libexpat >=2.7.0,<3.0a0
  - libgcc >=13
  - libzlib >=1.3.1,<2.0a0
  - python >=3.10,<3.11.0a0
  - python_abi 3.10.* *_cp310
  license: LGPL-2.1-only OR MPL-1.1
  purls:
  - pkg:pypi/pycairo?source=hash-mapping
  size: 117537
  timestamp: 1744682530776
- conda: https://conda.anaconda.org/conda-forge/noarch/pycparser-2.22-pyh29332c3_1.conda
  sha256: 79db7928d13fab2d892592223d7570f5061c192f27b9febd1a418427b719acc6
  md5: 12c566707c80111f9799308d9e265aef
  depends:
  - python >=3.9
  - python
  license: BSD-3-Clause
  license_family: BSD
  purls: []
  size: 110100
  timestamp: 1733195786147
- conda: https://conda.anaconda.org/conda-forge/noarch/pydantic-2.11.4-pyh3cfb1c2_0.conda
  sha256: a522473505ac6a9c10bb304d7338459a406ba22a6d3bb1a355c1b5283553a372
  md5: 8ad3ad8db5ce2ba470c9facc37af00a9
  depends:
  - annotated-types >=0.6.0
  - pydantic-core 2.33.2
  - python >=3.9
  - typing-extensions >=4.6.1
  - typing-inspection >=0.4.0
  - typing_extensions >=4.12.2
  license: MIT
  license_family: MIT
  purls:
  - pkg:pypi/pydantic?source=compressed-mapping
  size: 306304
  timestamp: 1746632069456
- conda: https://conda.anaconda.org/conda-forge/linux-64/pydantic-core-2.33.2-py310hbcd0ec0_0.conda
  sha256: 8da9aed7f21d775a7c91db6c9f95a0e00cae2d132709d5dc608c2e6828f9344b
  md5: 6b210a72e9e1b1cb6d30b266b84ca993
  depends:
  - python
  - typing-extensions >=4.6.0,!=4.7.0
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - python_abi 3.10.* *_cp310
  constrains:
  - __glibc >=2.17
  license: MIT
  license_family: MIT
  purls:
  - pkg:pypi/pydantic-core?source=hash-mapping
  size: 1892885
  timestamp: 1746625312783
- conda: https://conda.anaconda.org/conda-forge/noarch/pygments-2.19.1-pyhd8ed1ab_0.conda
  sha256: 28a3e3161390a9d23bc02b4419448f8d27679d9e2c250e29849e37749c8de86b
  md5: 232fb4577b6687b2d503ef8e254270c9
  depends:
  - python >=3.9
  license: BSD-2-Clause
  license_family: BSD
  purls:
  - pkg:pypi/pygments?source=hash-mapping
  size: 888600
  timestamp: 1736243563082
- conda: https://conda.anaconda.org/conda-forge/linux-64/pymupdf-1.26.0-py310h5f8b001_0.conda
  sha256: cb5a321516159db0e0a65981bdeeb61b68016ea4a7dbd6b3452022d2eb5b8310
  md5: c885e8b23ff11528f469ac256928f931
  depends:
  - python
  - mupdf 1.25.2.*
  - __glibc >=2.17,<3.0.a0
  - libstdcxx >=13
  - libgcc >=13
  - python_abi 3.10.* *_cp310
  license: AGPL-3.0-or-later
  purls:
  - pkg:pypi/pymupdf?source=hash-mapping
  size: 590697
  timestamp: 1747976111204
- conda: https://conda.anaconda.org/conda-forge/noarch/pyparsing-3.2.3-pyhd8ed1ab_1.conda
  sha256: b92afb79b52fcf395fd220b29e0dd3297610f2059afac45298d44e00fcbf23b6
  md5: 513d3c262ee49b54a8fec85c5bc99764
  depends:
  - python >=3.9
  license: MIT
  license_family: MIT
  purls:
  - pkg:pypi/pyparsing?source=hash-mapping
  size: 95988
  timestamp: 1743089832359
- conda: https://conda.anaconda.org/conda-forge/linux-64/pypdfium2-4.30.1-py310h4cff291_0.conda
  sha256: 9467337ccc96e9d1cc6279d91f43ccf11c93a4a25eef1e56ad15bf54027ba9f3
  md5: ec4782eb4aa07b9d5ce44331a5f8d3e5
  depends:
  - python
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - python_abi 3.10.* *_cp310
  license: Apache-2.0
  license_family: APACHE
  purls:
  - pkg:pypi/pypdfium2?source=hash-mapping
  size: 3097498
  timestamp: 1739979070053
- pypi: https://files.pythonhosted.org/packages/48/0a/c99fb7d7e176f8b176ef19704a32e6a9c6aafdf19ef75a187f701fc15801/pysbd-0.3.4-py3-none-any.whl
  name: pysbd
  version: 0.3.4
  sha256: cd838939b7b0b185fcf86b0baf6636667dfb6e474743beeff878e9f42e022953
  requires_python: '>=3'
- conda: https://conda.anaconda.org/conda-forge/linux-64/pyside6-6.8.3-py310hfd10a26_0.conda
  sha256: 08f4f3f7b731e4f7efc21fdbf2fb55c5e141f28546200008a86ac88a90de592f
  md5: dd3dd65ec785c86ed90e8cb4890361f2
  depends:
  - __glibc >=2.17,<3.0.a0
  - libclang13 >=20.1.1
  - libegl >=1.7.0,<2.0a0
  - libgcc >=13
  - libgl >=1.7.0,<2.0a0
  - libopengl >=1.7.0,<2.0a0
  - libstdcxx >=13
  - libxml2 >=2.13.7,<2.14.0a0
  - libxslt >=1.1.39,<2.0a0
  - python >=3.10,<3.11.0a0
  - python_abi 3.10.* *_cp310
  - qt6-main 6.8.3.*
  - qt6-main >=6.8.3,<6.9.0a0
  license: LGPL-3.0-only
  license_family: LGPL
  purls:
  - pkg:pypi/pyside6?source=hash-mapping
  - pkg:pypi/shiboken6?source=hash-mapping
  size: 10528099
  timestamp: 1743274294562
- conda: https://conda.anaconda.org/conda-forge/noarch/pysocks-1.7.1-pyha55dd90_7.conda
  sha256: ba3b032fa52709ce0d9fd388f63d330a026754587a2f461117cac9ab73d8d0d8
  md5: 461219d1a5bd61342293efa2c0c90eac
  depends:
  - __unix
  - python >=3.9
  license: BSD-3-Clause
  license_family: BSD
  purls:
  - pkg:pypi/pysocks?source=hash-mapping
  size: 21085
  timestamp: 1733217331982
- conda: https://conda.anaconda.org/conda-forge/noarch/pytest-8.3.5-pyhd8ed1ab_0.conda
  sha256: 963524de7340c56615583ba7b97a6beb20d5c56a59defb59724dc2a3105169c9
  md5: c3c9316209dec74a705a36797970c6be
  depends:
  - colorama
  - exceptiongroup >=1.0.0rc8
  - iniconfig
  - packaging
  - pluggy <2,>=1.5
  - python >=3.9
  - tomli >=1
  constrains:
  - pytest-faulthandler >=2
  license: MIT
  license_family: MIT
  purls:
  - pkg:pypi/pytest?source=hash-mapping
  size: 259816
  timestamp: 1740946648058
- conda: https://conda.anaconda.org/conda-forge/linux-64/python-3.10.17-hd6af730_0_cpython.conda
  sha256: 0ae32507817402bfad08fbf0f4a9b5ae26859d5390b98bc939da85fd0bd4239f
  md5: 7bb89638dae9ce1b8e051d0b721e83c2
  depends:
  - __glibc >=2.17,<3.0.a0
  - bzip2 >=1.0.8,<2.0a0
  - ld_impl_linux-64 >=2.36.1
  - libexpat >=2.7.0,<3.0a0
  - libffi >=3.4,<4.0a0
  - libgcc >=13
  - liblzma >=5.8.1,<6.0a0
  - libnsl >=2.0.1,<2.1.0a0
  - libsqlite >=3.49.1,<4.0a0
  - libuuid >=2.38.1,<3.0a0
  - libxcrypt >=4.4.36
  - libzlib >=1.3.1,<2.0a0
  - ncurses >=6.5,<7.0a0
  - openssl >=3.5.0,<4.0a0
  - readline >=8.2,<9.0a0
  - tk >=8.6.13,<8.7.0a0
  - tzdata
  constrains:
  - python_abi 3.10.* *_cp310
  license: Python-2.0
  purls: []
  size: 25058210
  timestamp: 1744324903492
- conda: https://conda.anaconda.org/conda-forge/noarch/python-dateutil-2.9.0.post0-pyhff2d567_1.conda
  sha256: a50052536f1ef8516ed11a844f9413661829aa083304dc624c5925298d078d79
  md5: 5ba79d7c71f03c678c8ead841f347d6e
  depends:
  - python >=3.9
  - six >=1.5
  license: Apache-2.0
  license_family: APACHE
  purls:
  - pkg:pypi/python-dateutil?source=hash-mapping
  size: 222505
  timestamp: 1733215763718
- conda: https://conda.anaconda.org/conda-forge/noarch/python-dotenv-1.1.0-pyh29332c3_1.conda
  sha256: 7d927317003544049c97e7108e8ca5f2be5ff0ea954f5c84c8bbeb243b663fc8
  md5: 27d816c6981a8d50090537b761de80f4
  depends:
  - python >=3.9
  - python
  license: BSD-3-Clause
  license_family: BSD
  purls:
  - pkg:pypi/python-dotenv?source=hash-mapping
  size: 25557
  timestamp: 1742948348635
- conda: https://conda.anaconda.org/conda-forge/noarch/python-fastjsonschema-2.21.1-pyhd8ed1ab_0.conda
  sha256: 1b09a28093071c1874862422696429d0d35bd0b8420698003ac004746c5e82a2
  md5: 38e34d2d1d9dca4fb2b9a0a04f604e2c
  depends:
  - python >=3.9
  license: BSD-3-Clause
  license_family: BSD
  purls:
  - pkg:pypi/fastjsonschema?source=hash-mapping
  size: 226259
  timestamp: 1733236073335
- conda: https://conda.anaconda.org/conda-forge/noarch/python-json-logger-2.0.7-pyhd8ed1ab_0.conda
  sha256: 4790787fe1f4e8da616edca4acf6a4f8ed4e7c6967aa31b920208fc8f95efcca
  md5: a61bf9ec79426938ff785eb69dbb1960
  depends:
  - python >=3.6
  license: BSD-2-Clause
  license_family: BSD
  purls:
  - pkg:pypi/python-json-logger?source=hash-mapping
  size: 13383
  timestamp: 1677079727691
- pypi: https://files.pythonhosted.org/packages/7c/0d/8787b021d52eb8764c0bb18ab95f720cf554902044c6a5cb1865daf45763/python-louvain-0.16.tar.gz
  name: python-louvain
  version: '0.16'
  sha256: b7ba2df5002fd28d3ee789a49532baad11fe648e4f2117cf0798e7520a1da56b
  requires_dist:
  - networkx
  - numpy
- conda: https://conda.anaconda.org/conda-forge/noarch/python-tzdata-2025.2-pyhd8ed1ab_0.conda
  sha256: e8392a8044d56ad017c08fec2b0eb10ae3d1235ac967d0aab8bd7b41c4a5eaf0
  md5: 88476ae6ebd24f39261e0854ac244f33
  depends:
  - python >=3.9
  license: Apache-2.0
  license_family: APACHE
  purls:
  - pkg:pypi/tzdata?source=compressed-mapping
  size: 144160
  timestamp: 1742745254292
- conda: https://conda.anaconda.org/conda-forge/linux-64/python-xxhash-3.5.0-py310ha75aee5_2.conda
  sha256: 7747a119a48b785f76cb0337c23bb2ba0e356e76422abc4f2b0c1f650fe06738
  md5: af6da4542e91edaf59b614ce7e991700
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - python >=3.10,<3.11.0a0
  - python_abi 3.10.* *_cp310
  - xxhash >=0.8.3,<0.8.4.0a0
  license: BSD-2-Clause
  license_family: BSD
  purls:
  - pkg:pypi/xxhash?source=hash-mapping
  size: 23075
  timestamp: 1740594937358
- conda: https://conda.anaconda.org/conda-forge/noarch/python_abi-3.10-7_cp310.conda
  build_number: 7
  sha256: 1316c66889313d9caebcfa5d5e9e6af25f8ba09396fc1bc196a08a3febbbabb8
  md5: 44e871cba2b162368476a84b8d040b6c
  constrains:
  - python 3.10.* *_cpython
  license: BSD-3-Clause
  license_family: BSD
  purls: []
  size: 6974
  timestamp: 1745258864549
- conda: https://conda.anaconda.org/conda-forge/linux-64/pytorch-2.7.0-cpu_mkl_py310_h8ec2884_100.conda
  sha256: a91d20ad4a3246ce8e8b90c3498b145b85c30226e3e37e505012d725541b9872
  md5: 80db30658f13a31389c00b26bb494555
  depends:
  - __glibc >=2.17,<3.0.a0
  - _openmp_mutex * *_llvm
  - _openmp_mutex >=4.5
  - filelock
  - fsspec
  - jinja2
  - libabseil * cxx17*
  - libabseil >=20250127.1,<20250128.0a0
  - libblas * *mkl
  - libcblas >=3.9.0,<4.0a0
  - libgcc >=13
  - libprotobuf >=5.29.3,<5.29.4.0a0
  - libstdcxx >=13
  - libtorch 2.7.0 cpu_mkl_hf6ddc5a_100
  - libuv >=1.50.0,<2.0a0
  - libzlib >=1.3.1,<2.0a0
  - llvm-openmp >=20.1.4
  - mkl >=2024.2.2,<2025.0a0
  - networkx
  - numpy >=1.19,<3
  - optree >=0.13.0
  - pybind11
  - python >=3.10,<3.11.0a0
  - python_abi 3.10.* *_cp310
  - setuptools <76
  - sleef >=3.8,<4.0a0
  - sympy >=1.13.3
  - typing_extensions >=4.10.0
  constrains:
  - pytorch-gpu <0.0a0
  - pytorch-cpu 2.7.0
  license: BSD-3-Clause
  license_family: BSD
  purls:
  - pkg:pypi/torch?source=hash-mapping
  size: 25184622
  timestamp: 1746261093140
- conda: https://conda.anaconda.org/conda-forge/noarch/pytz-2025.2-pyhd8ed1ab_0.conda
  sha256: 8d2a8bf110cc1fc3df6904091dead158ba3e614d8402a83e51ed3a8aa93cdeb0
  md5: bc8e3267d44011051f2eb14d22fb0960
  depends:
  - python >=3.9
  license: MIT
  license_family: MIT
  purls:
  - pkg:pypi/pytz?source=compressed-mapping
  size: 189015
  timestamp: 1742920947249
- conda: https://conda.anaconda.org/conda-forge/noarch/pyvis-0.3.2-pyhd8ed1ab_1.conda
  sha256: 3bfa28fbbca73a8db914341ae58c0049757182c9d9c925c378719996b9a26bf1
  md5: eb86dfeb9c60ec66c7a7fa93ae5dc58a
  depends:
  - ipython >=5.3.0
  - jinja2 >=2.9.6
  - jsonpickle >=1.4.1
  - networkx >=1.11
  - python >=3.9
  license: BSD-3-Clause
  license_family: BSD
  purls:
  - pkg:pypi/pyvis?source=hash-mapping
  size: 300893
  timestamp: 1737567297189
- conda: https://conda.anaconda.org/conda-forge/linux-64/pyyaml-6.0.2-py310h89163eb_2.conda
  sha256: 5fba7f5babcac872c72f6509c25331bcfac4f8f5031f0102530a41b41336fce6
  md5: fd343408e64cf1e273ab7c710da374db
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - python >=3.10,<3.11.0a0
  - python_abi 3.10.* *_cp310
  - yaml >=0.2.5,<0.3.0a0
  license: MIT
  license_family: MIT
  purls:
  - pkg:pypi/pyyaml?source=compressed-mapping
  size: 182769
  timestamp: 1737454971552
- conda: https://conda.anaconda.org/conda-forge/linux-64/pyzmq-26.4.0-py310h71f11fc_0.conda
  sha256: 2c93bcd81c1dadeb9b57bc4c833b3638f518f9b960fc1a928d4670abffd25017
  md5: 4859978df0e6408e439cb6badfbb3c5d
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - libsodium >=1.0.20,<1.0.21.0a0
  - libstdcxx >=13
  - python >=3.10,<3.11.0a0
  - python_abi 3.10.* *_cp310
  - zeromq >=4.3.5,<4.4.0a0
  license: BSD-3-Clause
  license_family: BSD
  purls:
  - pkg:pypi/pyzmq?source=hash-mapping
  size: 338321
  timestamp: 1743831462594
- conda: https://conda.anaconda.org/conda-forge/linux-64/qhull-2020.2-h434a139_5.conda
  sha256: 776363493bad83308ba30bcb88c2552632581b143e8ee25b1982c8c743e73abc
  md5: 353823361b1d27eb3960efb076dfcaf6
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc-ng >=12
  - libstdcxx-ng >=12
  license: LicenseRef-Qhull
  purls: []
  size: 552937
  timestamp: 1720813982144
- conda: https://conda.anaconda.org/conda-forge/linux-64/qt6-main-6.8.3-h588cce1_0.conda
  sha256: e822243adaafa72f4a15201ca32a05fddf2bbcd790cef7072a157af502b1dc95
  md5: a91776218fcb57ccda6878ab2030dc3b
  depends:
  - __glibc >=2.17,<3.0.a0
  - alsa-lib >=1.2.13,<1.3.0a0
  - dbus >=1.13.6,<2.0a0
  - double-conversion >=3.3.1,<3.4.0a0
  - fontconfig >=2.15.0,<3.0a0
  - fonts-conda-ecosystem
  - freetype >=2.13.3,<3.0a0
  - harfbuzz >=10.4.0,<11.0a0
  - icu >=75.1,<76.0a0
  - krb5 >=1.21.3,<1.22.0a0
  - libclang-cpp20.1 >=20.1.1,<20.2.0a0
  - libclang13 >=20.1.1
  - libcups >=2.3.3,<2.4.0a0
  - libdrm >=2.4.124,<2.5.0a0
  - libegl >=1.7.0,<2.0a0
  - libgcc >=13
  - libgl >=1.7.0,<2.0a0
  - libglib >=2.82.2,<3.0a0
  - libjpeg-turbo >=3.0.0,<4.0a0
  - libllvm20 >=20.1.1,<20.2.0a0
  - libpng >=1.6.47,<1.7.0a0
  - libpq >=17.4,<18.0a0
  - libsqlite >=3.49.1,<4.0a0
  - libstdcxx >=13
  - libtiff >=4.7.0,<4.8.0a0
  - libwebp-base >=1.5.0,<2.0a0
  - libxcb >=1.17.0,<2.0a0
  - libxkbcommon >=1.8.1,<2.0a0
  - libxml2 >=2.13.6,<2.14.0a0
  - libzlib >=1.3.1,<2.0a0
  - mysql-libs >=9.0.1,<9.1.0a0
  - openssl >=3.4.1,<4.0a0
  - pcre2 >=10.44,<10.45.0a0
  - wayland >=1.23.1,<2.0a0
  - xcb-util >=0.4.1,<0.5.0a0
  - xcb-util-cursor >=0.1.5,<0.2.0a0
  - xcb-util-image >=0.4.0,<0.5.0a0
  - xcb-util-keysyms >=0.4.1,<0.5.0a0
  - xcb-util-renderutil >=0.3.10,<0.4.0a0
  - xcb-util-wm >=0.4.2,<0.5.0a0
  - xorg-libice >=1.1.2,<2.0a0
  - xorg-libsm >=1.2.6,<2.0a0
  - xorg-libx11 >=1.8.12,<2.0a0
  - xorg-libxcomposite >=0.4.6,<1.0a0
  - xorg-libxcursor >=1.2.3,<2.0a0
  - xorg-libxdamage >=1.1.6,<2.0a0
  - xorg-libxext >=1.3.6,<2.0a0
  - xorg-libxrandr >=1.5.4,<2.0a0
  - xorg-libxtst >=1.2.5,<2.0a0
  - xorg-libxxf86vm >=1.1.6,<2.0a0
  - zstd >=1.5.7,<1.6.0a0
  constrains:
  - qt 6.8.3
  license: LGPL-3.0-only
  license_family: LGPL
  purls: []
  size: 51799567
  timestamp: 1743017930833
- conda: https://conda.anaconda.org/conda-forge/linux-64/rav1e-0.7.1-h8fae777_3.conda
  sha256: 6e5e704c1c21f820d760e56082b276deaf2b53cf9b751772761c3088a365f6f4
  md5: 2c42649888aac645608191ffdc80d13a
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  constrains:
  - __glibc >=2.17
  license: BSD-2-Clause
  license_family: BSD
  purls: []
  size: 5176669
  timestamp: 1746622023242
- conda: https://conda.anaconda.org/conda-forge/linux-64/re2-2024.07.02-h9925aae_3.conda
  sha256: 66d34e3b4881f856486d11914392c585713100ca547ccfc0947f3a4765c2c486
  md5: 6f445fb139c356f903746b2b91bbe786
  depends:
  - libre2-11 2024.07.02 hba17884_3
  license: BSD-3-Clause
  license_family: BSD
  purls: []
  size: 26811
  timestamp: 1741121137599
- conda: https://conda.anaconda.org/conda-forge/linux-64/readline-8.2-h8c095d6_2.conda
  sha256: 2d6d0c026902561ed77cd646b5021aef2d4db22e57a5b0178dfc669231e06d2c
  md5: 283b96675859b20a825f8fa30f311446
  depends:
  - libgcc >=13
  - ncurses >=6.5,<7.0a0
  license: GPL-3.0-only
  license_family: GPL
  purls: []
  size: 282480
  timestamp: 1740379431762
- conda: https://conda.anaconda.org/conda-forge/noarch/referencing-0.36.2-pyh29332c3_0.conda
  sha256: e20909f474a6cece176dfc0dc1addac265deb5fa92ea90e975fbca48085b20c3
  md5: 9140f1c09dd5489549c6a33931b943c7
  depends:
  - attrs >=22.2.0
  - python >=3.9
  - rpds-py >=0.7.0
  - typing_extensions >=4.4.0
  - python
  license: MIT
  license_family: MIT
  purls:
  - pkg:pypi/referencing?source=hash-mapping
  size: 51668
  timestamp: 1737836872415
- conda: https://conda.anaconda.org/conda-forge/linux-64/regex-2024.11.6-py310ha75aee5_0.conda
  sha256: 8a069ced0daa925d464d156a5974b0ba0cf694edcd35e355d8831e634e7c5198
  md5: 2a3ef8649e5c64ebbec9536e2fdd856b
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - python >=3.10,<3.11.0a0
  - python_abi 3.10.* *_cp310
  license: Python-2.0
  license_family: PSF
  purls:
  - pkg:pypi/regex?source=hash-mapping
  size: 353231
  timestamp: 1730952338254
- conda: https://conda.anaconda.org/conda-forge/noarch/requests-2.32.3-pyhd8ed1ab_1.conda
  sha256: d701ca1136197aa121bbbe0e8c18db6b5c94acbd041c2b43c70e5ae104e1d8ad
  md5: a9b9368f3701a417eac9edbcae7cb737
  depends:
  - certifi >=2017.4.17
  - charset-normalizer >=2,<4
  - idna >=2.5,<4
  - python >=3.9
  - urllib3 >=1.21.1,<3
  constrains:
  - chardet >=3.0.2,<6
  license: Apache-2.0
  license_family: APACHE
  purls:
  - pkg:pypi/requests?source=hash-mapping
  size: 58723
  timestamp: 1733217126197
- conda: https://conda.anaconda.org/conda-forge/noarch/rfc3339-validator-0.1.4-pyhd8ed1ab_1.conda
  sha256: 2e4372f600490a6e0b3bac60717278448e323cab1c0fecd5f43f7c56535a99c5
  md5: 36de09a8d3e5d5e6f4ee63af49e59706
  depends:
  - python >=3.9
  - six
  license: MIT
  license_family: MIT
  purls:
  - pkg:pypi/rfc3339-validator?source=hash-mapping
  size: 10209
  timestamp: 1733600040800
- conda: https://conda.anaconda.org/conda-forge/noarch/rfc3986-validator-0.1.1-pyh9f0ad1d_0.tar.bz2
  sha256: 2a5b495a1de0f60f24d8a74578ebc23b24aa53279b1ad583755f223097c41c37
  md5: 912a71cc01012ee38e6b90ddd561e36f
  depends:
  - python
  license: MIT
  license_family: MIT
  purls:
  - pkg:pypi/rfc3986-validator?source=hash-mapping
  size: 7818
  timestamp: 1598024297745
- conda: https://conda.anaconda.org/conda-forge/noarch/rich-14.0.0-pyh29332c3_0.conda
  sha256: d10e2b66a557ec6296844e04686db87818b0df87d73c06388f2332fda3f7d2d5
  md5: 202f08242192ce3ed8bdb439ba40c0fe
  depends:
  - markdown-it-py >=2.2.0
  - pygments >=2.13.0,<3.0.0
  - python >=3.9
  - typing_extensions >=4.0.0,<5.0.0
  - python
  license: MIT
  license_family: MIT
  purls:
  - pkg:pypi/rich?source=hash-mapping
  size: 200323
  timestamp: 1743371105291
- conda: https://conda.anaconda.org/conda-forge/linux-64/rpds-py-0.25.1-py310hbcd0ec0_0.conda
  sha256: 8b5b5039b26d98ab6c87c7eb6cf232a4741c96d96e43902a15e6586c4acc5eed
  md5: 64634e6d94c79af4c01725e05e1782d7
  depends:
  - python
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - python_abi 3.10.* *_cp310
  constrains:
  - __glibc >=2.17
  license: MIT
  license_family: MIT
  purls:
  - pkg:pypi/rpds-py?source=hash-mapping
  size: 389203
  timestamp: 1747837825912
- pypi: https://files.pythonhosted.org/packages/e9/93/0c0f002031f18b53af7a6166103c02b9c0667be528944137cc954ec921b3/rsa-4.7.2-py3-none-any.whl
  name: rsa
  version: 4.7.2
  sha256: 78f9a9bf4e7be0c5ded4583326e7461e3a3c5aae24073648b4bdfa797d78c9d2
  requires_dist:
  - pyasn1>=0.1.3
  requires_python: '>=3.5,<4'
- conda: https://conda.anaconda.org/conda-forge/linux-64/s2n-1.5.18-h763c568_1.conda
  sha256: 6d0399775ef7841914e99aed5b7330ce3d9d29a4219d40b1b94fd9a50d902a73
  md5: 0bf75253494a85260575e23c3b29db90
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - openssl >=3.5.0,<4.0a0
  license: Apache-2.0
  license_family: Apache
  purls: []
  size: 353577
  timestamp: 1746350509891
- pypi: https://files.pythonhosted.org/packages/18/17/22bf8155aa0ea2305eefa3a6402e040df7ebe512d1310165eda1e233c3f8/s3transfer-0.13.0-py3-none-any.whl
  name: s3transfer
  version: 0.13.0
  sha256: 0148ef34d6dd964d0d8cf4311b2b21c474693e57c2e069ec708ce043d2b527be
  requires_dist:
  - botocore>=1.37.4,<2.0a0
  - botocore[crt]>=1.37.4,<2.0a0 ; extra == 'crt'
  requires_python: '>=3.9'
- conda: https://conda.anaconda.org/conda-forge/linux-64/safetensors-0.5.3-py310h505e2c1_0.conda
  sha256: 2382852438d116acb06b6f1d77f2d39567fd1047b5a44a88b5308589d8afe021
  md5: e6880c6ac32e13c503e25ccfa1746e11
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - python >=3.10,<3.11.0a0
  - python_abi 3.10.* *_cp310
  constrains:
  - __glibc >=2.17
  license: Apache-2.0
  license_family: APACHE
  purls:
  - pkg:pypi/safetensors?source=hash-mapping
  size: 426407
  timestamp: 1740651617271
- conda: https://conda.anaconda.org/conda-forge/linux-64/scikit-learn-1.6.1-py310h27f47ee_0.conda
  sha256: 5c865487412b900d0abeb934907e5357c4a6cad19093316701ffd575980d0c54
  md5: 618ec5a8500fb53e8e52785e06d239f4
  depends:
  - __glibc >=2.17,<3.0.a0
  - _openmp_mutex >=4.5
  - joblib >=1.2.0
  - libgcc >=13
  - libstdcxx >=13
  - numpy >=1.19,<3
  - python >=3.10,<3.11.0a0
  - python_abi 3.10.* *_cp310
  - scipy
  - threadpoolctl >=3.1.0
  license: BSD-3-Clause
  license_family: BSD
  purls:
  - pkg:pypi/scikit-learn?source=hash-mapping
  size: 9468996
  timestamp: 1736497235051
- conda: https://conda.anaconda.org/conda-forge/linux-64/scipy-1.15.2-py310h1d65ade_0.conda
  sha256: 4cb98641f870666d365594013701d5691205a0fe81ac3ba7778a23b1cc2caa8e
  md5: 8c29cd33b64b2eb78597fa28b5595c8d
  depends:
  - __glibc >=2.17,<3.0.a0
  - libblas >=3.9.0,<4.0a0
  - libcblas >=3.9.0,<4.0a0
  - libgcc >=13
  - libgfortran
  - libgfortran5 >=13.3.0
  - liblapack >=3.9.0,<4.0a0
  - libstdcxx >=13
  - numpy <2.5
  - numpy >=1.19,<3
  - numpy >=1.23.5
  - python >=3.10,<3.11.0a0
  - python_abi 3.10.* *_cp310
  license: BSD-3-Clause
  license_family: BSD
  purls:
  - pkg:pypi/scipy?source=hash-mapping
  size: 16417101
  timestamp: 1739791865060
- pypi: https://files.pythonhosted.org/packages/eb/50/95cd574c3ccf4a268b334ea3c4c3cf9f95d1f24d6c0be82024d51c3e460b/scispacy-0.2.4.tar.gz
  name: scispacy
  version: 0.2.4
  sha256: 35328589d733de90be2f41106e84c76d329c2b310b0fa09d8ac8f1d31fd646ca
  requires_dist:
  - spacy>=2.2.1
  - awscli
  - conllu
  - numpy
  - joblib
  - nmslib>=*******
  - scikit-learn>=0.20.3
  - pysbd
  requires_python: '>=3.6.0'
- conda: https://conda.anaconda.org/conda-forge/noarch/send2trash-1.8.3-pyh0d859eb_1.conda
  sha256: 00926652bbb8924e265caefdb1db100f86a479e8f1066efe395d5552dde54d02
  md5: 938c8de6b9de091997145b3bf25cdbf9
  depends:
  - __linux
  - python >=3.9
  license: BSD-3-Clause
  license_family: BSD
  purls:
  - pkg:pypi/send2trash?source=hash-mapping
  size: 22736
  timestamp: 1733322148326
- conda: https://conda.anaconda.org/conda-forge/noarch/sentence-transformers-4.1.0-pyhd8ed1ab_0.conda
  sha256: c1ba1c9342899fa83d5af8ca50b8f80480412e874ee95a1ff842b6910474c22a
  md5: 0649ddce4ee97e4e891455da0fa50c42
  depends:
  - huggingface_hub >=0.20.0
  - numpy
  - pillow
  - python >=3.9
  - pytorch >=1.11.0
  - scikit-learn
  - scipy
  - tqdm
  - transformers >=4.41.0,<5.0.0
  license: Apache-2.0
  license_family: APACHE
  purls:
  - pkg:pypi/sentence-transformers?source=hash-mapping
  size: 196740
  timestamp: 1744730183978
- conda: https://conda.anaconda.org/conda-forge/noarch/setuptools-75.8.2-pyhff2d567_0.conda
  sha256: 91d664ace7c22e787775069418daa9f232ee8bafdd0a6a080a5ed2395a6fa6b2
  md5: 9bddfdbf4e061821a1a443f93223be61
  depends:
  - python >=3.9
  license: MIT
  license_family: MIT
  purls:
  - pkg:pypi/setuptools?source=hash-mapping
  size: 777736
  timestamp: 1740654030775
- conda: https://conda.anaconda.org/conda-forge/noarch/shellingham-1.5.4-pyhd8ed1ab_1.conda
  sha256: 0557c090913aa63cdbe821dbdfa038a321b488e22bc80196c4b3b1aace4914ef
  md5: 7c3c2a0f3ebdea2bbc35538d162b43bf
  depends:
  - python >=3.9
  license: MIT
  license_family: MIT
  purls:
  - pkg:pypi/shellingham?source=compressed-mapping
  size: 14462
  timestamp: 1733301007770
- conda: https://conda.anaconda.org/conda-forge/noarch/six-1.17.0-pyhd8ed1ab_0.conda
  sha256: 41db0180680cc67c3fa76544ffd48d6a5679d96f4b71d7498a759e94edc9a2db
  md5: a451d576819089b0d672f18768be0f65
  depends:
  - python >=3.9
  license: MIT
  license_family: MIT
  purls:
  - pkg:pypi/six?source=hash-mapping
  size: 16385
  timestamp: 1733381032766
- conda: https://conda.anaconda.org/conda-forge/linux-64/sleef-3.8-h1b44611_0.conda
  sha256: c998d5a29848ce9ff1c53ba506e7d01bbd520c39bbe72e2fb7cdf5a53bad012f
  md5: aec4dba5d4c2924730088753f6fa164b
  depends:
  - __glibc >=2.17,<3.0.a0
  - _openmp_mutex >=4.5
  - libgcc >=13
  - libstdcxx >=13
  license: BSL-1.0
  purls: []
  size: 1920152
  timestamp: 1738089391074
- conda: https://conda.anaconda.org/conda-forge/noarch/smart-open-7.1.0-hd8ed1ab_0.conda
  sha256: 9454c120e2c2defb14e24a93f2e97fb203294f3115d8a1a7913429f24145d83c
  md5: 021f83968041a87ad9f02413267fe97c
  depends:
  - smart_open 7.1.0 pyhd8ed1ab_0
  license: MIT
  license_family: MIT
  purls: []
  size: 7176
  timestamp: 1734485212368
- conda: https://conda.anaconda.org/conda-forge/noarch/smart_open-7.1.0-pyhd8ed1ab_0.conda
  sha256: bb477fcec2074d85e616bf1ce2722cc5c790db2a3f1bd168f1b08b7c5b71bdbe
  md5: a20e42d17e69d364cfb756ac780b0ff1
  depends:
  - python >=3.9
  - wrapt
  license: MIT
  license_family: MIT
  purls:
  - pkg:pypi/smart-open?source=hash-mapping
  size: 51950
  timestamp: 1734485211385
- conda: https://conda.anaconda.org/conda-forge/linux-64/snappy-1.2.1-h8bd8927_1.conda
  sha256: ec91e86eeb2c6bbf09d51351b851e945185d70661d2ada67204c9a6419d282d3
  md5: 3b3e64af585eadfb52bb90b553db5edf
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - libstdcxx >=13
  license: BSD-3-Clause
  license_family: BSD
  purls: []
  size: 42739
  timestamp: 1733501881851
- conda: https://conda.anaconda.org/conda-forge/noarch/sniffio-1.3.1-pyhd8ed1ab_1.conda
  sha256: c2248418c310bdd1719b186796ae50a8a77ce555228b6acd32768e2543a15012
  md5: bf7a226e58dfb8346c70df36065d86c9
  depends:
  - python >=3.9
  license: Apache-2.0
  license_family: Apache
  purls:
  - pkg:pypi/sniffio?source=hash-mapping
  size: 15019
  timestamp: 1733244175724
- conda: https://conda.anaconda.org/conda-forge/noarch/soupsieve-2.7-pyhd8ed1ab_0.conda
  sha256: 7518506cce9a736042132f307b3f4abce63bf076f5fb07c1f4e506c0b214295a
  md5: fb32097c717486aa34b38a9db57eb49e
  depends:
  - python >=3.9
  license: MIT
  license_family: MIT
  purls:
  - pkg:pypi/soupsieve?source=hash-mapping
  size: 37773
  timestamp: 1746563720271
- conda: https://conda.anaconda.org/conda-forge/linux-64/spacy-3.8.6-py310ha2bacc8_0.conda
  sha256: c9a342282620e5bf63dd533408cc69cbbf2aa5ce1b19eba568195ac9cea59fba
  md5: 2d985f078b70c8ee994d2751c936e57d
  depends:
  - __glibc >=2.17,<3.0.a0
  - catalogue >=2.0.6,<2.1.0
  - cymem >=2.0.2,<2.1.0
  - jinja2
  - langcodes >=3.2.0,<4.0.0
  - libgcc >=13
  - libstdcxx >=13
  - murmurhash >=0.28.0,<1.1.0
  - numpy >=1.19,<3
  - packaging >=20.0
  - preshed >=3.0.2,<3.1.0
  - pydantic >=1.7.4,!=1.8,!=1.8.1,<3.0.0
  - python >=3.10,<3.11.0a0
  - python_abi 3.10.* *_cp310
  - requests >=2.13.0,<3.0.0
  - setuptools
  - spacy-legacy >=3.0.11,<3.1.0
  - spacy-loggers >=1.0.0,<2.0.0
  - srsly >=2.4.3,<3.0.0
  - thinc >=8.3.0,<8.4.0
  - tqdm >=4.38.0,<5.0.0
  - typer >=0.3.0,<1.0.0
  - wasabi >=0.9.1,<1.2.0
  - weasel >=0.1.0,<0.5.0
  license: MIT
  license_family: MIT
  purls:
  - pkg:pypi/spacy?source=hash-mapping
  size: 5409254
  timestamp: 1747709200273
- conda: https://conda.anaconda.org/conda-forge/noarch/spacy-legacy-3.0.12-pyhd8ed1ab_0.conda
  sha256: c121bea3de8a53dbbb695c4914c792b22e71db99cb1dc15e95b387a5fce31ee8
  md5: bbe68ced56ea855f0223c329f1fd2fc0
  depends:
  - python >=3.6
  license: MIT
  license_family: MIT
  purls:
  - pkg:pypi/spacy-legacy?source=hash-mapping
  size: 28683
  timestamp: 1674550401013
- conda: https://conda.anaconda.org/conda-forge/noarch/spacy-loggers-1.0.5-pyhd8ed1ab_0.conda
  sha256: 86a8fb3d4af66bc889c3f18255117b6ef8cfb84658e71437f1964717ccd53384
  md5: 017fa97ac8c29416983dc9e67b27f6c8
  depends:
  - python >=3.6
  license: MIT
  license_family: MIT
  purls:
  - pkg:pypi/spacy-loggers?source=hash-mapping
  size: 21760
  timestamp: 1694527261289
- conda: https://conda.anaconda.org/conda-forge/linux-64/srsly-2.5.1-py310hf71b8c6_1.conda
  sha256: 399e99abfd80948d9fa7ababd977e39f3b235e4bac6252681b2bf5363a735511
  md5: 687d00f548bbc4dc1a835c3ac9407a1f
  depends:
  - __glibc >=2.17,<3.0.a0
  - catalogue >=2.0.1,<2.1.0
  - cloudpickle >=2.2.0
  - libgcc >=13
  - libstdcxx >=13
  - python >=3.10,<3.11.0a0
  - python_abi 3.10.* *_cp310
  - ujson >=1.35
  license: MIT
  license_family: MIT
  purls:
  - pkg:pypi/srsly?source=hash-mapping
  size: 510536
  timestamp: 1740609831602
- conda: https://conda.anaconda.org/conda-forge/noarch/stack_data-0.6.3-pyhd8ed1ab_1.conda
  sha256: 570da295d421661af487f1595045760526964f41471021056e993e73089e9c41
  md5: b1b505328da7a6b246787df4b5a49fbc
  depends:
  - asttokens
  - executing
  - pure_eval
  - python >=3.9
  license: MIT
  license_family: MIT
  purls:
  - pkg:pypi/stack-data?source=hash-mapping
  size: 26988
  timestamp: 1733569565672
- conda: https://conda.anaconda.org/conda-forge/linux-64/svt-av1-3.0.2-h5888daf_0.conda
  sha256: fb4b97a3fd259eff4849b2cfe5678ced0c5792b697eb1f7bcd93a4230e90e80e
  md5: 0096882bd623e6cc09e8bf920fc8fb47
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - libstdcxx >=13
  license: BSD-2-Clause
  license_family: BSD
  purls: []
  size: 2750235
  timestamp: 1742907589246
- conda: https://conda.anaconda.org/conda-forge/noarch/sympy-1.14.0-pyh2585a3b_105.conda
  sha256: 09d3b6ac51d437bc996ad006d9f749ca5c645c1900a854a6c8f193cbd13f03a8
  md5: 8c09fac3785696e1c477156192d64b91
  depends:
  - __unix
  - cpython
  - gmpy2 >=2.0.8
  - mpmath >=0.19
  - python >=3.9
  license: BSD-3-Clause
  license_family: BSD
  purls:
  - pkg:pypi/sympy?source=compressed-mapping
  size: 4616621
  timestamp: 1745946173026
- conda: https://conda.anaconda.org/conda-forge/linux-64/tbb-2021.13.0-hceb3a55_1.conda
  sha256: 65463732129899770d54b1fbf30e1bb82fdebda9d7553caf08d23db4590cd691
  md5: ba7726b8df7b9d34ea80e82b097a4893
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - libhwloc >=2.11.2,<2.11.3.0a0
  - libstdcxx >=13
  license: Apache-2.0
  license_family: APACHE
  purls: []
  size: 175954
  timestamp: 1732982638805
- conda: https://conda.anaconda.org/conda-forge/noarch/terminado-0.18.1-pyh0d859eb_0.conda
  sha256: b300557c0382478cf661ddb520263508e4b3b5871b471410450ef2846e8c352c
  md5: efba281bbdae5f6b0a1d53c6d4a97c93
  depends:
  - __linux
  - ptyprocess
  - python >=3.8
  - tornado >=6.1.0
  license: BSD-2-Clause
  license_family: BSD
  purls:
  - pkg:pypi/terminado?source=hash-mapping
  size: 22452
  timestamp: 1710262728753
- conda: https://conda.anaconda.org/conda-forge/linux-64/tesseract-5.5.0-ha6794fa_0.conda
  sha256: 7aefc6012e188ba0c2ce98c7c69f9109622ac1e5b530c56785fc27f4c561efd4
  md5: 3371cb8a434f05cc92846010d071454f
  depends:
  - __glibc >=2.17,<3.0.a0
  - giflib >=5.2.2,<5.3.0a0
  - leptonica >=1.83.1,<1.84.0a0
  - libarchive >=3.7.7,<3.8.0a0
  - libgcc >=13
  - libjpeg-turbo >=3.0.0,<4.0a0
  - liblzma >=5.6.3,<6.0a0
  - libpng >=1.6.44,<1.7.0a0
  - libstdcxx >=13
  - libtiff >=4.7.0,<4.8.0a0
  - libwebp-base >=1.4.0,<2.0a0
  - libzlib >=1.3.1,<2.0a0
  - openjpeg >=2.5.2,<3.0a0
  license: Apache-2.0
  license_family: Apache
  purls: []
  size: 176189247
  timestamp: 1733751928288
- conda: https://conda.anaconda.org/conda-forge/linux-64/thinc-8.3.2-py310ha2bacc8_1.conda
  sha256: 7991af4c7cb62210a03346fd3c21ef84511ce961e10afac1525cb70c061b65a2
  md5: c9b621edb2b6f654e777c40211b5e322
  depends:
  - __glibc >=2.17,<3.0.a0
  - catalogue >=2.0.4,<2.1.0
  - confection >=0.0.1,<1.0.0
  - cymem >=2.0.2,<2.1.0
  - cython-blis >=1.0.0,<1.1.0
  - libgcc >=13
  - libstdcxx >=13
  - murmurhash >=1.0.2,<1.1.0
  - numpy >=1.19,<3
  - packaging >=20.0
  - preshed >=3.0.2,<3.1.0
  - pydantic >=1.7.4,!=1.8,!=1.8.1,<3.0.0
  - python >=3.10,<3.11.0a0
  - python_abi 3.10.* *_cp310
  - srsly >=2.4.0,<3.0.0
  - wasabi >=0.8.1,<1.2.0
  license: MIT
  license_family: MIT
  purls:
  - pkg:pypi/thinc?source=hash-mapping
  size: 888166
  timestamp: **********
- conda: https://conda.anaconda.org/conda-forge/noarch/threadpoolctl-3.6.0-pyhecae5ae_0.conda
  sha256: 6016672e0e72c4cf23c0cf7b1986283bd86a9c17e8d319212d78d8e9ae42fdfd
  md5: 9d64911b31d57ca443e9f1e36b04385f
  depends:
  - python >=3.9
  license: BSD-3-Clause
  license_family: BSD
  purls:
  - pkg:pypi/threadpoolctl?source=hash-mapping
  size: 23869
  timestamp: 1741878358548
- conda: https://conda.anaconda.org/conda-forge/noarch/tinycss2-1.4.0-pyhd8ed1ab_0.conda
  sha256: cad582d6f978276522f84bd209a5ddac824742fe2d452af6acf900f8650a73a2
  md5: f1acf5fdefa8300de697982bcb1761c9
  depends:
  - python >=3.5
  - webencodings >=0.4
  license: BSD-3-Clause
  license_family: BSD
  purls:
  - pkg:pypi/tinycss2?source=hash-mapping
  size: 28285
  timestamp: 1729802975370
- conda: https://conda.anaconda.org/conda-forge/linux-64/tk-8.6.13-noxft_h4845f30_101.conda
  sha256: e0569c9caa68bf476bead1bed3d79650bb080b532c64a4af7d8ca286c08dea4e
  md5: d453b98d9c83e71da0741bb0ff4d76bc
  depends:
  - libgcc-ng >=12
  - libzlib >=1.2.13,<2.0.0a0
  license: TCL
  license_family: BSD
  purls: []
  size: 3318875
  timestamp: 1699202167581
- conda: https://conda.anaconda.org/conda-forge/linux-64/tokenizers-0.21.1-py310hb1c6e2c_0.conda
  sha256: 39938b5f43b112fe3de6763833a2c70b3ae7c539373975d09d8aac10866d6173
  md5: efd7b53d6a48eeb0c22c739bdc98aecd
  depends:
  - __glibc >=2.17,<3.0.a0
  - huggingface_hub >=0.16.4,<1.0
  - libgcc >=13
  - libstdcxx >=13
  - openssl >=3.4.1,<4.0a0
  - python >=3.10,<3.11.0a0
  - python_abi 3.10.* *_cp310
  constrains:
  - __glibc >=2.17
  license: Apache-2.0
  license_family: APACHE
  purls:
  - pkg:pypi/tokenizers?source=hash-mapping
  size: 2304123
  timestamp: 1741890674321
- conda: https://conda.anaconda.org/conda-forge/noarch/tomli-2.2.1-pyhd8ed1ab_1.conda
  sha256: 18636339a79656962723077df9a56c0ac7b8a864329eb8f847ee3d38495b863e
  md5: ac944244f1fed2eb49bae07193ae8215
  depends:
  - python >=3.9
  license: MIT
  license_family: MIT
  purls:
  - pkg:pypi/tomli?source=hash-mapping
  size: 19167
  timestamp: 1733256819729
- conda: https://conda.anaconda.org/conda-forge/linux-64/tornado-6.5.1-py310ha75aee5_0.conda
  sha256: c24cc5952f1f1a84a848427382eecb04fc959987e19423e2c84e3281d0beec32
  md5: 6f3da1072c0c4d2a1beb1e84615f7c9c
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - python >=3.10,<3.11.0a0
  - python_abi 3.10.* *_cp310
  license: Apache-2.0
  license_family: Apache
  purls:
  - pkg:pypi/tornado?source=compressed-mapping
  size: 659208
  timestamp: 1748003428618
- conda: https://conda.anaconda.org/conda-forge/noarch/tqdm-4.67.1-pyhd8ed1ab_1.conda
  sha256: 11e2c85468ae9902d24a27137b6b39b4a78099806e551d390e394a8c34b48e40
  md5: 9efbfdc37242619130ea42b1cc4ed861
  depends:
  - colorama
  - python >=3.9
  license: MPL-2.0 or MIT
  purls:
  - pkg:pypi/tqdm?source=hash-mapping
  size: 89498
  timestamp: 1735661472632
- conda: https://conda.anaconda.org/conda-forge/noarch/traitlets-5.14.3-pyhd8ed1ab_1.conda
  sha256: f39a5620c6e8e9e98357507262a7869de2ae8cc07da8b7f84e517c9fd6c2b959
  md5: 019a7385be9af33791c989871317e1ed
  depends:
  - python >=3.9
  license: BSD-3-Clause
  license_family: BSD
  purls:
  - pkg:pypi/traitlets?source=hash-mapping
  size: 110051
  timestamp: 1733367480074
- conda: https://conda.anaconda.org/conda-forge/noarch/transformers-4.52.3-pyhd8ed1ab_0.conda
  sha256: 72a29636c0818c548df1802825533c731a83acbe2fe2c1107896e3b2b8dc8925
  md5: 2141e69941f35d95bb3d23ae2c7f982d
  depends:
  - datasets !=2.5.0
  - filelock
  - huggingface_hub >=0.30.0,<1.0
  - numpy >=1.17
  - packaging >=20.0
  - python >=3.9
  - pyyaml >=5.1
  - regex !=2019.12.17
  - requests
  - safetensors >=0.4.1
  - tokenizers >=0.21,<0.22
  - tqdm >=4.27
  license: Apache-2.0
  license_family: APACHE
  purls:
  - pkg:pypi/transformers?source=compressed-mapping
  size: 3747724
  timestamp: 1747930494046
- conda: https://conda.anaconda.org/conda-forge/noarch/typer-0.15.3-pyhf21524f_0.conda
  sha256: 8cd849ceb5e2f50481b1f30f083ee134fac706a56d7879c61248f0aadad4ea5b
  md5: b4bed8eb8dd4fe076f436e5506d31673
  depends:
  - typer-slim-standard ==0.15.3 h1a15894_0
  - python >=3.9
  - python
  license: MIT
  license_family: MIT
  purls:
  - pkg:pypi/typer?source=compressed-mapping
  size: 77044
  timestamp: 1745886712803
- conda: https://conda.anaconda.org/conda-forge/noarch/typer-slim-0.15.3-pyh29332c3_0.conda
  sha256: 1768d1d9914d4237b0a1ae8bcb30dace44ac80b9ab1516a2d429d0b27ad70ab9
  md5: 20c0f2ae932004d7118c172eeb035cea
  depends:
  - python >=3.9
  - click >=8.0.0
  - typing_extensions >=3.7.4.3
  - python
  constrains:
  - typer 0.15.3.*
  - rich >=10.11.0
  - shellingham >=1.3.0
  license: MIT
  license_family: MIT
  purls:
  - pkg:pypi/typer-slim?source=compressed-mapping
  size: 46152
  timestamp: 1745886712803
- conda: https://conda.anaconda.org/conda-forge/noarch/typer-slim-standard-0.15.3-h1a15894_0.conda
  sha256: 72f77e8e61b28058562f2782cf32ff84f14f6c11c6cea7a3fe2839d34654ea45
  md5: 120216d3a2e51dfbb87bbba173ebf210
  depends:
  - typer-slim ==0.15.3 pyh29332c3_0
  - rich
  - shellingham
  license: MIT
  license_family: MIT
  purls: []
  size: 5411
  timestamp: 1745886712803
- conda: https://conda.anaconda.org/conda-forge/noarch/types-python-dateutil-2.9.0.20250516-pyhd8ed1ab_0.conda
  sha256: 0fb78e97cad71ebf911958bf97777ec958a64a4621615a4dcc3ffb52cda7c6d0
  md5: e3465397ca4b5b60ba9fbc92ef0672f9
  depends:
  - python >=3.9
  license: Apache-2.0 AND MIT
  purls:
  - pkg:pypi/types-python-dateutil?source=hash-mapping
  size: 22634
  timestamp: 1747417327584
- conda: https://conda.anaconda.org/conda-forge/noarch/typing-extensions-4.13.2-h0e9735f_0.conda
  sha256: 4865fce0897d3cb0ffc8998219157a8325f6011c136e6fd740a9a6b169419296
  md5: 568ed1300869dca0ba09fb750cda5dbb
  depends:
  - typing_extensions ==4.13.2 pyh29332c3_0
  license: PSF-2.0
  license_family: PSF
  purls: []
  size: 89900
  timestamp: 1744302253997
- conda: https://conda.anaconda.org/conda-forge/noarch/typing-inspection-0.4.1-pyhd8ed1ab_0.conda
  sha256: 4259a7502aea516c762ca8f3b8291b0d4114e094bdb3baae3171ccc0900e722f
  md5: e0c3cd765dc15751ee2f0b03cd015712
  depends:
  - python >=3.9
  - typing_extensions >=4.12.0
  license: MIT
  license_family: MIT
  purls:
  - pkg:pypi/typing-inspection?source=compressed-mapping
  size: 18809
  timestamp: 1747870776989
- conda: https://conda.anaconda.org/conda-forge/noarch/typing_extensions-4.13.2-pyh29332c3_0.conda
  sha256: a8aaf351e6461de0d5d47e4911257e25eec2fa409d71f3b643bb2f748bde1c08
  md5: 83fc6ae00127671e301c9f44254c31b8
  depends:
  - python >=3.9
  - python
  license: PSF-2.0
  license_family: PSF
  purls:
  - pkg:pypi/typing-extensions?source=compressed-mapping
  size: 52189
  timestamp: 1744302253997
- conda: https://conda.anaconda.org/conda-forge/noarch/typing_utils-0.1.0-pyhd8ed1ab_1.conda
  sha256: 3088d5d873411a56bf988eee774559335749aed6f6c28e07bf933256afb9eb6c
  md5: f6d7aa696c67756a650e91e15e88223c
  depends:
  - python >=3.9
  license: Apache-2.0
  license_family: APACHE
  purls:
  - pkg:pypi/typing-utils?source=hash-mapping
  size: 15183
  timestamp: 1733331395943
- conda: https://conda.anaconda.org/conda-forge/noarch/tzdata-2025b-h78e105d_0.conda
  sha256: 5aaa366385d716557e365f0a4e9c3fca43ba196872abbbe3d56bb610d131e192
  md5: 4222072737ccff51314b5ece9c7d6f5a
  license: LicenseRef-Public-Domain
  purls: []
  size: 122968
  timestamp: 1742727099393
- conda: https://conda.anaconda.org/conda-forge/linux-64/ujson-5.10.0-py310hf71b8c6_1.conda
  sha256: e540f245bdc5ee5fc89d418d18f60ee571f78567272d093bc1ad9602145f799e
  md5: a67e5cc75e3e63c950c3b9f34d477f73
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - libstdcxx >=13
  - python >=3.10,<3.11.0a0
  - python_abi 3.10.* *_cp310
  license: BSD-3-Clause
  license_family: BSD
  purls:
  - pkg:pypi/ujson?source=hash-mapping
  size: 51888
  timestamp: 1724954522278
- conda: https://conda.anaconda.org/conda-forge/linux-64/unicodedata2-16.0.0-py310ha75aee5_0.conda
  sha256: 0468c864c60190fdb94b4705bca618e77589d5cb9fa096de47caccd1f22b0b54
  md5: 1d7a4b9202cdd10d56ecdd7f6c347190
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - python >=3.10,<3.11.0a0
  - python_abi 3.10.* *_cp310
  license: Apache-2.0
  license_family: Apache
  purls:
  - pkg:pypi/unicodedata2?source=hash-mapping
  size: 404975
  timestamp: 1736692615537
- conda: https://conda.anaconda.org/conda-forge/noarch/uri-template-1.3.0-pyhd8ed1ab_1.conda
  sha256: e0eb6c8daf892b3056f08416a96d68b0a358b7c46b99c8a50481b22631a4dfc0
  md5: e7cb0f5745e4c5035a460248334af7eb
  depends:
  - python >=3.9
  license: MIT
  license_family: MIT
  purls:
  - pkg:pypi/uri-template?source=hash-mapping
  size: 23990
  timestamp: 1733323714454
- conda: https://conda.anaconda.org/conda-forge/noarch/urllib3-2.4.0-pyhd8ed1ab_0.conda
  sha256: a25403b76f7f03ca1a906e1ef0f88521edded991b9897e7fed56a3e334b3db8c
  md5: c1e349028e0052c4eea844e94f773065
  depends:
  - brotli-python >=1.0.9
  - h2 >=4,<5
  - pysocks >=1.5.6,<2.0,!=1.5.7
  - python >=3.9
  - zstandard >=0.18.0
  license: MIT
  license_family: MIT
  purls:
  - pkg:pypi/urllib3?source=hash-mapping
  size: 100791
  timestamp: 1744323705540
- conda: https://conda.anaconda.org/conda-forge/noarch/wand-0.6.10-pyhd8ed1ab_0.tar.bz2
  sha256: d5becb1dd154d26a2daeb601dca9535c2ab16b25e0cd8dede8b5a5940ba55d64
  md5: b2dd46b74e85947726cca13a8c3a1e5c
  depends:
  - imagemagick
  - python >=3.3|>=2.7
  license: MIT
  license_family: MIT
  purls:
  - pkg:pypi/wand?source=hash-mapping
  size: 107903
  timestamp: 1661205389684
- conda: https://conda.anaconda.org/conda-forge/noarch/wasabi-1.1.3-pyhd8ed1ab_1.conda
  sha256: fd0635cbd4f76916e2ec39d0c359e930fc377ff6be1ac13cc375cf7fa8a3eebc
  md5: fa76741f59d973f2e07d810ee124cb25
  depends:
  - python >=3.9
  license: MIT
  license_family: MIT
  purls:
  - pkg:pypi/wasabi?source=hash-mapping
  size: 28780
  timestamp: 1740630860493
- conda: https://conda.anaconda.org/conda-forge/linux-64/wayland-1.23.1-h3e06ad9_1.conda
  sha256: 73d809ec8056c2f08e077f9d779d7f4e4c2b625881cad6af303c33dc1562ea01
  md5: a37843723437ba75f42c9270ffe800b1
  depends:
  - __glibc >=2.17,<3.0.a0
  - libexpat >=2.7.0,<3.0a0
  - libffi >=3.4.6,<3.5.0a0
  - libgcc >=13
  - libstdcxx >=13
  license: MIT
  license_family: MIT
  purls: []
  size: 321099
  timestamp: 1745806602179
- conda: https://conda.anaconda.org/conda-forge/noarch/wcwidth-0.2.13-pyhd8ed1ab_1.conda
  sha256: f21e63e8f7346f9074fd00ca3b079bd3d2fa4d71f1f89d5b6934bf31446dc2a5
  md5: b68980f2495d096e71c7fd9d7ccf63e6
  depends:
  - python >=3.9
  license: MIT
  license_family: MIT
  purls:
  - pkg:pypi/wcwidth?source=hash-mapping
  size: 32581
  timestamp: 1733231433877
- conda: https://conda.anaconda.org/conda-forge/noarch/weasel-0.4.1-pyhd8ed1ab_2.conda
  sha256: 737da689fdf8a056d24ec104585b9e500849369a7b2e78c1923373c85b17cabf
  md5: c909d5a153ce7e0af0ff051b851dd38a
  depends:
  - cloudpathlib >=0.7.0,<1.0.0
  - confection >=0.0.4,<0.2.0
  - packaging >=20.0
  - pydantic >=1.7.4,!=1.8,!=1.8.1,<3.0.0
  - python >=3.9
  - requests >=2.13.0,<3.0.0
  - smart-open >=5.2.1,<8.0.0
  - srsly >=2.4.3,<3.0.0
  - typer >=0.3.0,<1.0.0
  - wasabi >=0.9.1,<1.2.0
  license: MIT
  license_family: MIT
  purls:
  - pkg:pypi/weasel?source=hash-mapping
  size: 42533
  timestamp: 1734270255037
- conda: https://conda.anaconda.org/conda-forge/noarch/webcolors-24.11.1-pyhd8ed1ab_0.conda
  sha256: 08315dc2e61766a39219b2d82685fc25a56b2817acf84d5b390176080eaacf99
  md5: b49f7b291e15494aafb0a7d74806f337
  depends:
  - python >=3.9
  license: BSD-3-Clause
  license_family: BSD
  purls:
  - pkg:pypi/webcolors?source=hash-mapping
  size: 18431
  timestamp: 1733359823938
- conda: https://conda.anaconda.org/conda-forge/noarch/webencodings-0.5.1-pyhd8ed1ab_3.conda
  sha256: 19ff205e138bb056a46f9e3839935a2e60bd1cf01c8241a5e172a422fed4f9c6
  md5: 2841eb5bfc75ce15e9a0054b98dcd64d
  depends:
  - python >=3.9
  license: BSD-3-Clause
  license_family: BSD
  purls:
  - pkg:pypi/webencodings?source=hash-mapping
  size: 15496
  timestamp: 1733236131358
- conda: https://conda.anaconda.org/conda-forge/noarch/websocket-client-1.8.0-pyhd8ed1ab_1.conda
  sha256: 1dd84764424ffc82030c19ad70607e6f9e3b9cb8e633970766d697185652053e
  md5: 84f8f77f0a9c6ef401ee96611745da8f
  depends:
  - python >=3.9
  license: Apache-2.0
  license_family: APACHE
  purls:
  - pkg:pypi/websocket-client?source=hash-mapping
  size: 46718
  timestamp: 1733157432924
- conda: https://conda.anaconda.org/conda-forge/noarch/widgetsnbextension-4.0.14-pyhd8ed1ab_0.conda
  sha256: 7df3620c88343f2d960a58a81b79d4e4aa86ab870249e7165db7c3e2971a2664
  md5: 2f1f99b13b9d2a03570705030a0b3e7c
  depends:
  - python >=3.9
  license: BSD-3-Clause
  license_family: BSD
  purls:
  - pkg:pypi/widgetsnbextension?source=compressed-mapping
  size: 889285
  timestamp: 1744291155057
- conda: https://conda.anaconda.org/conda-forge/linux-64/wrapt-1.17.2-py310ha75aee5_0.conda
  sha256: 16b76bf5d540d55297650b45dfead91c7ddd43a8f15380d9035d140aa023f3da
  md5: 4bfec5ca281bf0c9d701e82d473be899
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - python >=3.10,<3.11.0a0
  - python_abi 3.10.* *_cp310
  license: BSD-2-Clause
  license_family: BSD
  purls:
  - pkg:pypi/wrapt?source=hash-mapping
  size: 56470
  timestamp: 1736869620642
- conda: https://conda.anaconda.org/conda-forge/linux-64/x265-3.5-h924138e_3.tar.bz2
  sha256: 76c7405bcf2af639971150f342550484efac18219c0203c5ee2e38b8956fe2a0
  md5: e7f6ed84d4623d52ee581325c1587a6b
  depends:
  - libgcc-ng >=10.3.0
  - libstdcxx-ng >=10.3.0
  license: GPL-2.0-or-later
  license_family: GPL
  purls: []
  size: 3357188
  timestamp: 1646609687141
- conda: https://conda.anaconda.org/conda-forge/linux-64/xcb-util-0.4.1-hb711507_2.conda
  sha256: 416aa55d946ce4ab173ab338796564893a2f820e80e04e098ff00c25fb981263
  md5: 8637c3e5821654d0edf97e2b0404b443
  depends:
  - libgcc-ng >=12
  - libxcb >=1.16,<2.0.0a0
  license: MIT
  license_family: MIT
  purls: []
  size: 19965
  timestamp: 1718843348208
- conda: https://conda.anaconda.org/conda-forge/linux-64/xcb-util-cursor-0.1.5-hb9d3cd8_0.conda
  sha256: c7b35db96f6e32a9e5346f97adc968ef2f33948e3d7084295baebc0e33abdd5b
  md5: eb44b3b6deb1cab08d72cb61686fe64c
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - libxcb >=1.13
  - libxcb >=1.16,<2.0.0a0
  - xcb-util-image >=0.4.0,<0.5.0a0
  - xcb-util-renderutil >=0.3.10,<0.4.0a0
  license: MIT
  license_family: MIT
  purls: []
  size: 20296
  timestamp: 1726125844850
- conda: https://conda.anaconda.org/conda-forge/linux-64/xcb-util-image-0.4.0-hb711507_2.conda
  sha256: 94b12ff8b30260d9de4fd7a28cca12e028e572cbc504fd42aa2646ec4a5bded7
  md5: a0901183f08b6c7107aab109733a3c91
  depends:
  - libgcc-ng >=12
  - libxcb >=1.16,<2.0.0a0
  - xcb-util >=0.4.1,<0.5.0a0
  license: MIT
  license_family: MIT
  purls: []
  size: 24551
  timestamp: 1718880534789
- conda: https://conda.anaconda.org/conda-forge/linux-64/xcb-util-keysyms-0.4.1-hb711507_0.conda
  sha256: 546e3ee01e95a4c884b6401284bb22da449a2f4daf508d038fdfa0712fe4cc69
  md5: ad748ccca349aec3e91743e08b5e2b50
  depends:
  - libgcc-ng >=12
  - libxcb >=1.16,<2.0.0a0
  license: MIT
  license_family: MIT
  purls: []
  size: 14314
  timestamp: 1718846569232
- conda: https://conda.anaconda.org/conda-forge/linux-64/xcb-util-renderutil-0.3.10-hb711507_0.conda
  sha256: 2d401dadc43855971ce008344a4b5bd804aca9487d8ebd83328592217daca3df
  md5: 0e0cbe0564d03a99afd5fd7b362feecd
  depends:
  - libgcc-ng >=12
  - libxcb >=1.16,<2.0.0a0
  license: MIT
  license_family: MIT
  purls: []
  size: 16978
  timestamp: 1718848865819
- conda: https://conda.anaconda.org/conda-forge/linux-64/xcb-util-wm-0.4.2-hb711507_0.conda
  sha256: 31d44f297ad87a1e6510895740325a635dd204556aa7e079194a0034cdd7e66a
  md5: 608e0ef8256b81d04456e8d211eee3e8
  depends:
  - libgcc-ng >=12
  - libxcb >=1.16,<2.0.0a0
  license: MIT
  license_family: MIT
  purls: []
  size: 51689
  timestamp: 1718844051451
- conda: https://conda.anaconda.org/conda-forge/linux-64/xkeyboard-config-2.44-hb9d3cd8_0.conda
  sha256: 83ad2be5eb1d359b4cd7d7a93a6b25cdbfdce9d27b37508e2a4efe90d3a4ed80
  md5: 7c91bfc90672888259675ad2ad28af9c
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - xorg-libx11 >=1.8.12,<2.0a0
  license: MIT
  license_family: MIT
  purls: []
  size: 392870
  timestamp: 1745806998840
- conda: https://conda.anaconda.org/conda-forge/linux-64/xorg-libice-1.1.2-hb9d3cd8_0.conda
  sha256: c12396aabb21244c212e488bbdc4abcdef0b7404b15761d9329f5a4a39113c4b
  md5: fb901ff28063514abb6046c9ec2c4a45
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  license: MIT
  license_family: MIT
  purls: []
  size: 58628
  timestamp: 1734227592886
- conda: https://conda.anaconda.org/conda-forge/linux-64/xorg-libsm-1.2.6-he73a12e_0.conda
  sha256: 277841c43a39f738927145930ff963c5ce4c4dacf66637a3d95d802a64173250
  md5: 1c74ff8c35dcadf952a16f752ca5aa49
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - libuuid >=2.38.1,<3.0a0
  - xorg-libice >=1.1.2,<2.0a0
  license: MIT
  license_family: MIT
  purls: []
  size: 27590
  timestamp: 1741896361728
- conda: https://conda.anaconda.org/conda-forge/linux-64/xorg-libx11-1.8.12-h4f16b4b_0.conda
  sha256: 51909270b1a6c5474ed3978628b341b4d4472cd22610e5f22b506855a5e20f67
  md5: db038ce880f100acc74dba10302b5630
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - libxcb >=1.17.0,<2.0a0
  license: MIT
  license_family: MIT
  purls: []
  size: 835896
  timestamp: 1741901112627
- conda: https://conda.anaconda.org/conda-forge/linux-64/xorg-libxau-1.0.12-hb9d3cd8_0.conda
  sha256: ed10c9283974d311855ae08a16dfd7e56241fac632aec3b92e3cfe73cff31038
  md5: f6ebe2cb3f82ba6c057dde5d9debe4f7
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  license: MIT
  license_family: MIT
  purls: []
  size: 14780
  timestamp: 1734229004433
- conda: https://conda.anaconda.org/conda-forge/linux-64/xorg-libxcomposite-0.4.6-hb9d3cd8_2.conda
  sha256: 753f73e990c33366a91fd42cc17a3d19bb9444b9ca5ff983605fa9e953baf57f
  md5: d3c295b50f092ab525ffe3c2aa4b7413
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - xorg-libx11 >=1.8.10,<2.0a0
  - xorg-libxfixes >=6.0.1,<7.0a0
  license: MIT
  license_family: MIT
  purls: []
  size: 13603
  timestamp: 1727884600744
- conda: https://conda.anaconda.org/conda-forge/linux-64/xorg-libxcursor-1.2.3-hb9d3cd8_0.conda
  sha256: 832f538ade441b1eee863c8c91af9e69b356cd3e9e1350fff4fe36cc573fc91a
  md5: 2ccd714aa2242315acaf0a67faea780b
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - xorg-libx11 >=1.8.10,<2.0a0
  - xorg-libxfixes >=6.0.1,<7.0a0
  - xorg-libxrender >=0.9.11,<0.10.0a0
  license: MIT
  license_family: MIT
  purls: []
  size: 32533
  timestamp: 1730908305254
- conda: https://conda.anaconda.org/conda-forge/linux-64/xorg-libxdamage-1.1.6-hb9d3cd8_0.conda
  sha256: 43b9772fd6582bf401846642c4635c47a9b0e36ca08116b3ec3df36ab96e0ec0
  md5: b5fcc7172d22516e1f965490e65e33a4
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - xorg-libx11 >=1.8.10,<2.0a0
  - xorg-libxext >=1.3.6,<2.0a0
  - xorg-libxfixes >=6.0.1,<7.0a0
  license: MIT
  license_family: MIT
  purls: []
  size: 13217
  timestamp: 1727891438799
- conda: https://conda.anaconda.org/conda-forge/linux-64/xorg-libxdmcp-1.1.5-hb9d3cd8_0.conda
  sha256: 6b250f3e59db07c2514057944a3ea2044d6a8cdde8a47b6497c254520fade1ee
  md5: 8035c64cb77ed555e3f150b7b3972480
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  license: MIT
  license_family: MIT
  purls: []
  size: 19901
  timestamp: 1727794976192
- conda: https://conda.anaconda.org/conda-forge/linux-64/xorg-libxext-1.3.6-hb9d3cd8_0.conda
  sha256: da5dc921c017c05f38a38bd75245017463104457b63a1ce633ed41f214159c14
  md5: febbab7d15033c913d53c7a2c102309d
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - xorg-libx11 >=1.8.10,<2.0a0
  license: MIT
  license_family: MIT
  purls: []
  size: 50060
  timestamp: 1727752228921
- conda: https://conda.anaconda.org/conda-forge/linux-64/xorg-libxfixes-6.0.1-hb9d3cd8_0.conda
  sha256: 2fef37e660985794617716eb915865ce157004a4d567ed35ec16514960ae9271
  md5: 4bdb303603e9821baf5fe5fdff1dc8f8
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - xorg-libx11 >=1.8.10,<2.0a0
  license: MIT
  license_family: MIT
  purls: []
  size: 19575
  timestamp: 1727794961233
- conda: https://conda.anaconda.org/conda-forge/linux-64/xorg-libxi-1.8.2-hb9d3cd8_0.conda
  sha256: 1a724b47d98d7880f26da40e45f01728e7638e6ec69f35a3e11f92acd05f9e7a
  md5: 17dcc85db3c7886650b8908b183d6876
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - xorg-libx11 >=1.8.10,<2.0a0
  - xorg-libxext >=1.3.6,<2.0a0
  - xorg-libxfixes >=6.0.1,<7.0a0
  license: MIT
  license_family: MIT
  purls: []
  size: 47179
  timestamp: 1727799254088
- conda: https://conda.anaconda.org/conda-forge/linux-64/xorg-libxinerama-1.1.5-h5888daf_1.conda
  sha256: 1b9141c027f9d84a9ee5eb642a0c19457c788182a5a73c5a9083860ac5c20a8c
  md5: 5e2eb9bf77394fc2e5918beefec9f9ab
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - libstdcxx >=13
  - xorg-libx11 >=1.8.10,<2.0a0
  - xorg-libxext >=1.3.6,<2.0a0
  license: MIT
  license_family: MIT
  purls: []
  size: 13891
  timestamp: 1727908521531
- conda: https://conda.anaconda.org/conda-forge/linux-64/xorg-libxrandr-1.5.4-hb9d3cd8_0.conda
  sha256: ac0f037e0791a620a69980914a77cb6bb40308e26db11698029d6708f5aa8e0d
  md5: 2de7f99d6581a4a7adbff607b5c278ca
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - xorg-libx11 >=1.8.10,<2.0a0
  - xorg-libxext >=1.3.6,<2.0a0
  - xorg-libxrender >=0.9.11,<0.10.0a0
  license: MIT
  license_family: MIT
  purls: []
  size: 29599
  timestamp: 1727794874300
- conda: https://conda.anaconda.org/conda-forge/linux-64/xorg-libxrender-0.9.12-hb9d3cd8_0.conda
  sha256: 044c7b3153c224c6cedd4484dd91b389d2d7fd9c776ad0f4a34f099b3389f4a1
  md5: 96d57aba173e878a2089d5638016dc5e
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - xorg-libx11 >=1.8.10,<2.0a0
  license: MIT
  license_family: MIT
  purls: []
  size: 33005
  timestamp: 1734229037766
- conda: https://conda.anaconda.org/conda-forge/linux-64/xorg-libxt-1.3.1-hb9d3cd8_0.conda
  sha256: a8afba4a55b7b530eb5c8ad89737d60d60bc151a03fbef7a2182461256953f0e
  md5: 279b0de5f6ba95457190a1c459a64e31
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - xorg-libice >=1.1.1,<2.0a0
  - xorg-libsm >=1.2.4,<2.0a0
  - xorg-libx11 >=1.8.10,<2.0a0
  license: MIT
  license_family: MIT
  purls: []
  size: 379686
  timestamp: 1731860547604
- conda: https://conda.anaconda.org/conda-forge/linux-64/xorg-libxtst-1.2.5-hb9d3cd8_3.conda
  sha256: 752fdaac5d58ed863bbf685bb6f98092fe1a488ea8ebb7ed7b606ccfce08637a
  md5: 7bbe9a0cc0df0ac5f5a8ad6d6a11af2f
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - xorg-libx11 >=1.8.10,<2.0a0
  - xorg-libxext >=1.3.6,<2.0a0
  - xorg-libxi >=1.7.10,<2.0a0
  license: MIT
  license_family: MIT
  purls: []
  size: 32808
  timestamp: 1727964811275
- conda: https://conda.anaconda.org/conda-forge/linux-64/xorg-libxxf86vm-1.1.6-hb9d3cd8_0.conda
  sha256: 8a4e2ee642f884e6b78c20c0892b85dd9b2a6e64a6044e903297e616be6ca35b
  md5: 5efa5fa6243a622445fdfd72aee15efa
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - xorg-libx11 >=1.8.10,<2.0a0
  - xorg-libxext >=1.3.6,<2.0a0
  license: MIT
  license_family: MIT
  purls: []
  size: 17819
  timestamp: 1734214575628
- conda: https://conda.anaconda.org/conda-forge/linux-64/xxhash-0.8.3-hb47aa4a_0.conda
  sha256: 08e12f140b1af540a6de03dd49173c0e5ae4ebc563cabdd35ead0679835baf6f
  md5: 607e13a8caac17f9a664bcab5302ce06
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  license: BSD-2-Clause
  license_family: BSD
  purls: []
  size: 108219
  timestamp: 1746457673761
- conda: https://conda.anaconda.org/conda-forge/linux-64/yaml-0.2.5-h7f98852_2.tar.bz2
  sha256: a4e34c710eeb26945bdbdaba82d3d74f60a78f54a874ec10d373811a5d217535
  md5: 4cb3ad778ec2d5a7acbdf254eb1c42ae
  depends:
  - libgcc-ng >=9.4.0
  license: MIT
  license_family: MIT
  purls: []
  size: 89141
  timestamp: 1641346969816
- conda: https://conda.anaconda.org/conda-forge/linux-64/yarl-1.20.0-py310h89163eb_0.conda
  sha256: c652eef585f13e5e9dc154fa9d6344370e1c2f3d90af65270af86faffbdd2be8
  md5: 7140707840ea7c82607620c545f5dd01
  depends:
  - __glibc >=2.17,<3.0.a0
  - idna >=2.0
  - libgcc >=13
  - multidict >=4.0
  - propcache >=0.2.1
  - python >=3.10,<3.11.0a0
  - python_abi 3.10.* *_cp310
  license: Apache-2.0
  license_family: Apache
  purls:
  - pkg:pypi/yarl?source=hash-mapping
  size: 144601
  timestamp: 1744972837538
- conda: https://conda.anaconda.org/conda-forge/linux-64/zeromq-4.3.5-h3b0a872_7.conda
  sha256: a4dc72c96848f764bb5a5176aa93dd1e9b9e52804137b99daeebba277b31ea10
  md5: 3947a35e916fcc6b9825449affbf4214
  depends:
  - __glibc >=2.17,<3.0.a0
  - krb5 >=1.21.3,<1.22.0a0
  - libgcc >=13
  - libsodium >=1.0.20,<1.0.21.0a0
  - libstdcxx >=13
  license: MPL-2.0
  license_family: MOZILLA
  purls: []
  size: 335400
  timestamp: 1731585026517
- conda: https://conda.anaconda.org/conda-forge/noarch/zipp-3.21.0-pyhd8ed1ab_1.conda
  sha256: 567c04f124525c97a096b65769834b7acb047db24b15a56888a322bf3966c3e1
  md5: 0c3cc595284c5e8f0f9900a9b228a332
  depends:
  - python >=3.9
  license: MIT
  license_family: MIT
  purls:
  - pkg:pypi/zipp?source=hash-mapping
  size: 21809
  timestamp: 1732827613585
- conda: https://conda.anaconda.org/conda-forge/linux-64/zlib-1.3.1-hb9d3cd8_2.conda
  sha256: 5d7c0e5f0005f74112a34a7425179f4eb6e73c92f5d109e6af4ddeca407c92ab
  md5: c9f075ab2f33b3bbee9e62d4ad0a6cd8
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - libzlib 1.3.1 hb9d3cd8_2
  license: Zlib
  license_family: Other
  purls: []
  size: 92286
  timestamp: 1727963153079
- conda: https://conda.anaconda.org/conda-forge/linux-64/zstandard-0.23.0-py310ha75aee5_2.conda
  sha256: f9b76c2f8a0f96e656843553272e547170182f5b8aba1a6bcba28f7611d87c23
  md5: f9254b5b0193982416b91edcb4b2676f
  depends:
  - __glibc >=2.17,<3.0.a0
  - cffi >=1.11
  - libgcc >=13
  - python >=3.10,<3.11.0a0
  - python_abi 3.10.* *_cp310
  license: BSD-3-Clause
  license_family: BSD
  purls:
  - pkg:pypi/zstandard?source=hash-mapping
  size: 722119
  timestamp: 1745869786772
- conda: https://conda.anaconda.org/conda-forge/linux-64/zstd-1.5.7-hb8e6e7a_2.conda
  sha256: a4166e3d8ff4e35932510aaff7aa90772f84b4d07e9f6f83c614cba7ceefe0eb
  md5: 6432cb5d4ac0046c3ac0a8a0f95842f9
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - libstdcxx >=13
  - libzlib >=1.3.1,<2.0a0
  license: BSD-3-Clause
  license_family: BSD
  purls: []
  size: 567578
  timestamp: 1742433379869
