#!/usr/bin/env python3
"""Create a focused graph visualization highlighting the most novel papers."""

import json
import numpy as np
import networkx as nx
import matplotlib.pyplot as plt
from matplotlib import cm
from typing import Set
import click


def create_novel_paper_subgraph(G: nx.Graph, top_n: int = 20, 
                               neighbor_hops: int = 1) -> nx.Graph:
    """Extract subgraph focused on most novel papers and their neighbors."""
    
    # Get novelty scores
    novelty_scores = [(node, data['novelty_score']) 
                     for node, data in G.nodes(data=True)]
    novelty_scores.sort(key=lambda x: x[1], reverse=True)
    
    # Get top N most novel papers
    top_novel_nodes = [node for node, _ in novelty_scores[:top_n]]
    
    # Find neighbors within specified hops
    nodes_to_include = set(top_novel_nodes)
    for node in top_novel_nodes:
        # Get neighbors within hop distance
        for hop in range(1, neighbor_hops + 1):
            neighbors = set()
            for n in nodes_to_include.copy():
                if n in G:
                    neighbors.update(G.neighbors(n))
            nodes_to_include.update(neighbors)
    
    # Create subgraph
    subgraph = G.subgraph(nodes_to_include).copy()
    
    print(f"Novel paper subgraph: {subgraph.number_of_nodes()} nodes, "
          f"{subgraph.number_of_edges()} edges")
    
    return subgraph, set(top_novel_nodes)


def visualize_novel_papers(graph_file: str = None, output_file: str = 'novelty_focus.png',
                         top_n: int = 20, figsize=(20, 20), dpi=300):
    """Create visualization focused on most novel papers."""
    
    # Load or recreate graph
    if graph_file:
        # Load from saved graph file if provided
        G = nx.read_gexf(graph_file)
    else:
        # Recreate from extracted papers
        from graph_enhancer import load_graph_from_results
        G, papers = load_graph_from_results()
    
    # Extract novel paper subgraph
    subgraph, novel_nodes = create_novel_paper_subgraph(G, top_n=top_n, neighbor_hops=1)
    
    # Compute layout with novel papers at center
    pos = nx.spring_layout(subgraph, k=2, iterations=100, seed=42)
    
    # Adjust positions to spread out novel papers
    for node in novel_nodes:
        if node in pos:
            # Push novel papers outward slightly
            pos[node] = pos[node] * 1.2
    
    # Create figure
    fig, ax = plt.subplots(figsize=figsize, facecolor='white')
    ax.set_facecolor('#f8f9fa')
    
    # Draw edges
    edge_weights = [subgraph[u][v].get('weight', 0.5) for u, v in subgraph.edges()]
    
    # Draw all edges in light gray
    nx.draw_networkx_edges(subgraph, pos,
                         edge_color='#cccccc',
                         width=[w * 2 for w in edge_weights],
                         alpha=0.4,
                         ax=ax)
    
    # Draw edges connected to novel papers in different color
    novel_edges = [(u, v) for u, v in subgraph.edges() 
                  if u in novel_nodes or v in novel_nodes]
    if novel_edges:
        nx.draw_networkx_edges(subgraph, pos,
                             edgelist=novel_edges,
                             edge_color='#ff6b6b',
                             width=2,
                             alpha=0.6,
                             ax=ax)
    
    # Prepare node properties
    node_colors = []
    node_sizes = []
    for node in subgraph.nodes():
        if node in novel_nodes:
            # Novel papers in red gradient based on novelty score
            novelty = subgraph.nodes[node]['novelty_score']
            node_colors.append(plt.cm.Reds(0.3 + novelty * 0.7))
            node_sizes.append(800 + novelty * 1200)
        else:
            # Other papers in blue
            node_colors.append('#4ECDC4')
            node_sizes.append(300)
    
    # Draw nodes
    nx.draw_networkx_nodes(subgraph, pos,
                         node_color=node_colors,
                         node_size=node_sizes,
                         alpha=0.9,
                         linewidths=2,
                         edgecolors='black',
                         ax=ax)
    
    # Add labels for all nodes
    labels = {}
    for node in subgraph.nodes():
        file_name = subgraph.nodes[node].get('file_name', f'Paper {node}')
        if node in novel_nodes:
            # Full name for novel papers
            labels[node] = file_name.replace('10.1038_nmicrobiol.', '')
        else:
            # Shortened for others
            labels[node] = file_name[:10] + '...'
    
    # Draw labels with different styles
    novel_labels = {n: labels[n] for n in novel_nodes if n in subgraph}
    other_labels = {n: labels[n] for n in subgraph.nodes() if n not in novel_nodes}
    
    # Novel paper labels - larger and bold
    nx.draw_networkx_labels(subgraph, pos,
                          labels=novel_labels,
                          font_size=12,
                          font_weight='bold',
                          font_color='darkred',
                          ax=ax)
    
    # Other paper labels - smaller
    nx.draw_networkx_labels(subgraph, pos,
                          labels=other_labels,
                          font_size=8,
                          font_color='gray',
                          ax=ax)
    
    # Add title
    ax.set_title(f'Most Novel Papers Network\n'
                f'Top {top_n} novel papers (red) with direct connections\n'
                f'{subgraph.number_of_nodes()} papers total',
                fontsize=18, fontweight='bold', pad=20)
    ax.axis('off')
    
    # Add novelty score color bar
    sm = plt.cm.ScalarMappable(cmap=plt.cm.Reds, 
                               norm=plt.Normalize(vmin=0.3, vmax=0.7))
    sm.set_array([])
    cbar = plt.colorbar(sm, ax=ax, orientation='horizontal', 
                       pad=0.02, fraction=0.05, anchor=(0.5, 1.0))
    cbar.set_label('Novelty Score', fontsize=12)
    
    # Save
    plt.tight_layout()
    plt.savefig(output_file, dpi=dpi, bbox_inches='tight',
               facecolor='white', edgecolor='none')
    plt.close()
    
    print(f"Novel papers visualization saved to: {output_file}")
    
    # Save info about novel papers
    novel_info = []
    for node in novel_nodes:
        if node in subgraph:
            info = {
                'node_id': int(node),
                'file_name': subgraph.nodes[node].get('file_name', ''),
                'title': subgraph.nodes[node].get('title', ''),
                'novelty_score': float(subgraph.nodes[node].get('novelty_score', 0)),
                'connections': len(list(subgraph.neighbors(node)))
            }
            novel_info.append(info)
    
    novel_info.sort(key=lambda x: x['novelty_score'], reverse=True)
    
    info_file = output_file.replace('.png', '_info.json')
    with open(info_file, 'w') as f:
        json.dump(novel_info, f, indent=2)
    
    print(f"Novel papers info saved to: {info_file}")


@click.command()
@click.option('--top-n', '-n', default=15, type=int,
              help='Number of most novel papers to highlight')
@click.option('--output', '-o', default='novelty_focus.png',
              help='Output PNG file')
@click.option('--size', default=20, type=int,
              help='Figure size in inches')
def main(top_n, output, size):
    """Create focused visualization of most novel papers."""
    visualize_novel_papers(
        output_file=output,
        top_n=top_n,
        figsize=(size, size)
    )


if __name__ == '__main__':
    main()