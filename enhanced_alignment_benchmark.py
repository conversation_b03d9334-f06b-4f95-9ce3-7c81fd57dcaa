#!/usr/bin/env python3
"""Enhanced alignment benchmark for improved extraction methods."""

import json
import numpy as np
import matplotlib.pyplot as plt
from sklearn.metrics import r2_score
from scipy.stats import pearsonr
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.metrics.pairwise import cosine_similarity
import pandas as pd
from pathlib import Path


def load_enhanced_alignment_data(file_path: str = "enhanced_alignment_test_data.json"):
    """Load enhanced alignment test data."""
    
    if not Path(file_path).exists():
        print(f"❌ {file_path} not found")
        print("Run: python test_enhanced_extraction.py")
        return None
    
    with open(file_path, 'r') as f:
        data = json.load(f)
    
    print(f"📊 Loaded {len(data)} papers with enhanced extraction data")
    return data


def analyze_enhanced_alignment(data):
    """Analyze alignment between enhanced rule-based and LLM methods."""
    
    print("\n🔍 ENHANCED ALIGNMENT ANALYSIS")
    print("=" * 50)
    
    # Extract counts for comparison
    comparison_data = []
    
    for paper in data:
        rule_data = paper['enhanced_rule_based']
        llm_data = paper['enhanced_llm']
        
        # Only include papers where LLM extraction succeeded
        if not llm_data.get('extraction_success', False):
            continue
        
        comparison_data.append({
            'title': paper['title'][:30],
            'rule_hyp_count': rule_data['hypotheses_count'],
            'llm_hyp_count': llm_data['hypotheses_count'],
            'rule_ev_count': rule_data['evidence_count'],
            'llm_ev_count': llm_data['evidence_count'],
            'rule_hyp_text': ' '.join([h['text'] for h in rule_data['hypotheses']]),
            'llm_hyp_text': ' '.join([h['text'] for h in llm_data['hypotheses']]),
            'rule_ev_text': ' '.join([e['text'] for e in rule_data['evidence']]),
            'llm_ev_text': ' '.join([e['text'] for e in llm_data['evidence']])
        })
    
    if len(comparison_data) < 2:
        print("❌ Need at least 2 papers with successful LLM extraction")
        return None
    
    df = pd.DataFrame(comparison_data)
    
    print(f"Valid papers for comparison: {len(df)}")
    
    # Calculate alignment metrics
    results = calculate_enhanced_alignment_metrics(df)
    
    # Create visualizations
    create_enhanced_alignment_plots(df, results)
    
    # Analyze semantic similarity
    semantic_results = analyze_semantic_alignment(df)
    
    # Generate report
    generate_enhanced_alignment_report(df, results, semantic_results)
    
    return results


def calculate_enhanced_alignment_metrics(df):
    """Calculate enhanced alignment metrics."""
    
    results = {}
    
    # Hypothesis count alignment
    if len(df) > 1 and df['rule_hyp_count'].var() > 0:
        results['hyp_count_r2'] = r2_score(df['rule_hyp_count'], df['llm_hyp_count'])
        results['hyp_count_corr'], results['hyp_count_p'] = pearsonr(df['rule_hyp_count'], df['llm_hyp_count'])
    else:
        results['hyp_count_r2'] = 0
        results['hyp_count_corr'] = 0
        results['hyp_count_p'] = 1
    
    # Evidence count alignment
    if len(df) > 1 and df['rule_ev_count'].var() > 0:
        results['ev_count_r2'] = r2_score(df['rule_ev_count'], df['llm_ev_count'])
        results['ev_count_corr'], results['ev_count_p'] = pearsonr(df['rule_ev_count'], df['llm_ev_count'])
    else:
        results['ev_count_r2'] = 0
        results['ev_count_corr'] = 0
        results['ev_count_p'] = 1
    
    # Text length alignment
    df['rule_hyp_len'] = df['rule_hyp_text'].str.len()
    df['llm_hyp_len'] = df['llm_hyp_text'].str.len()
    
    if len(df) > 1 and df['rule_hyp_len'].var() > 0:
        results['hyp_len_r2'] = r2_score(df['rule_hyp_len'], df['llm_hyp_len'])
        results['hyp_len_corr'], results['hyp_len_p'] = pearsonr(df['rule_hyp_len'], df['llm_hyp_len'])
    else:
        results['hyp_len_r2'] = 0
        results['hyp_len_corr'] = 0
        results['hyp_len_p'] = 1
    
    return results


def analyze_semantic_alignment(df):
    """Analyze semantic similarity between methods."""
    
    print("\n📝 SEMANTIC ALIGNMENT ANALYSIS")
    print("-" * 40)
    
    vectorizer = TfidfVectorizer(max_features=500, stop_words='english', min_df=1)
    
    semantic_results = {
        'hypothesis_similarities': [],
        'evidence_similarities': []
    }
    
    # Calculate hypothesis semantic similarity
    for _, row in df.iterrows():
        rule_text = row['rule_hyp_text']
        llm_text = row['llm_hyp_text']
        
        if rule_text.strip() and llm_text.strip():
            try:
                vectors = vectorizer.fit_transform([rule_text, llm_text])
                similarity = cosine_similarity(vectors[0:1], vectors[1:2])[0][0]
                semantic_results['hypothesis_similarities'].append(similarity)
            except:
                semantic_results['hypothesis_similarities'].append(0.0)
        else:
            semantic_results['hypothesis_similarities'].append(0.0)
    
    # Calculate evidence semantic similarity
    for _, row in df.iterrows():
        rule_text = row['rule_ev_text']
        llm_text = row['llm_ev_text']
        
        if rule_text.strip() and llm_text.strip():
            try:
                vectors = vectorizer.fit_transform([rule_text, llm_text])
                similarity = cosine_similarity(vectors[0:1], vectors[1:2])[0][0]
                semantic_results['evidence_similarities'].append(similarity)
            except:
                semantic_results['evidence_similarities'].append(0.0)
        else:
            semantic_results['evidence_similarities'].append(0.0)
    
    # Calculate averages
    semantic_results['mean_hyp_similarity'] = np.mean(semantic_results['hypothesis_similarities'])
    semantic_results['mean_ev_similarity'] = np.mean(semantic_results['evidence_similarities'])
    
    print(f"Mean hypothesis semantic similarity: {semantic_results['mean_hyp_similarity']:.3f}")
    print(f"Mean evidence semantic similarity: {semantic_results['mean_ev_similarity']:.3f}")
    
    return semantic_results


def create_enhanced_alignment_plots(df, results):
    """Create enhanced alignment visualization plots."""
    
    fig, axes = plt.subplots(2, 2, figsize=(15, 12))
    fig.suptitle('Enhanced Extraction Methods: Alignment Analysis', fontsize=16, fontweight='bold')
    
    # Hypothesis count alignment
    axes[0, 0].scatter(df['rule_hyp_count'], df['llm_hyp_count'], alpha=0.7, s=60, color='blue')
    max_val = max(df['rule_hyp_count'].max(), df['llm_hyp_count'].max())
    axes[0, 0].plot([0, max_val], [0, max_val], 'r--', alpha=0.8, label='Perfect Alignment')
    
    if len(df) > 1:
        z = np.polyfit(df['rule_hyp_count'], df['llm_hyp_count'], 1)
        p = np.poly1d(z)
        axes[0, 0].plot(df['rule_hyp_count'], p(df['rule_hyp_count']), "g-", alpha=0.8, label='Trend Line')
    
    axes[0, 0].set_xlabel('Enhanced Rule-Based Hypothesis Count')
    axes[0, 0].set_ylabel('Enhanced LLM Hypothesis Count')
    axes[0, 0].set_title(f'Hypothesis Counts\nR² = {results["hyp_count_r2"]:.3f}, r = {results["hyp_count_corr"]:.3f}')
    axes[0, 0].legend()
    axes[0, 0].grid(True, alpha=0.3)
    
    # Evidence count alignment
    axes[0, 1].scatter(df['rule_ev_count'], df['llm_ev_count'], alpha=0.7, s=60, color='orange')
    max_val = max(df['rule_ev_count'].max(), df['llm_ev_count'].max())
    axes[0, 1].plot([0, max_val], [0, max_val], 'r--', alpha=0.8, label='Perfect Alignment')
    
    if len(df) > 1:
        z = np.polyfit(df['rule_ev_count'], df['llm_ev_count'], 1)
        p = np.poly1d(z)
        axes[0, 1].plot(df['rule_ev_count'], p(df['rule_ev_count']), "g-", alpha=0.8, label='Trend Line')
    
    axes[0, 1].set_xlabel('Enhanced Rule-Based Evidence Count')
    axes[0, 1].set_ylabel('Enhanced LLM Evidence Count')
    axes[0, 1].set_title(f'Evidence Counts\nR² = {results["ev_count_r2"]:.3f}, r = {results["ev_count_corr"]:.3f}')
    axes[0, 1].legend()
    axes[0, 1].grid(True, alpha=0.3)
    
    # Text length alignment
    axes[1, 0].scatter(df['rule_hyp_len'], df['llm_hyp_len'], alpha=0.7, s=60, color='green')
    
    if len(df) > 1:
        z = np.polyfit(df['rule_hyp_len'], df['llm_hyp_len'], 1)
        p = np.poly1d(z)
        axes[1, 0].plot(df['rule_hyp_len'], p(df['rule_hyp_len']), "g-", alpha=0.8, label='Trend Line')
    
    axes[1, 0].set_xlabel('Enhanced Rule-Based Hypothesis Text Length')
    axes[1, 0].set_ylabel('Enhanced LLM Hypothesis Text Length')
    axes[1, 0].set_title(f'Text Lengths\nR² = {results["hyp_len_r2"]:.3f}, r = {results["hyp_len_corr"]:.3f}')
    axes[1, 0].legend()
    axes[1, 0].grid(True, alpha=0.3)
    
    # Summary statistics
    axes[1, 1].axis('off')
    
    summary_text = f"""
    ENHANCED ALIGNMENT SUMMARY
    ═══════════════════════════
    
    Dataset: {len(df)} papers
    
    Hypothesis Counts:
    • R² = {results['hyp_count_r2']:.3f}
    • Correlation = {results['hyp_count_corr']:.3f}
    • p-value = {results['hyp_count_p']:.3f}
    
    Evidence Counts:
    • R² = {results['ev_count_r2']:.3f}
    • Correlation = {results['ev_count_corr']:.3f}
    • p-value = {results['ev_count_p']:.3f}
    
    Text Lengths:
    • R² = {results['hyp_len_r2']:.3f}
    • Correlation = {results['hyp_len_corr']:.3f}
    • p-value = {results['hyp_len_p']:.3f}
    
    Interpretation:
    • R² > 0.7: Strong alignment ✅
    • R² 0.3-0.7: Moderate alignment ⚠️
    • R² < 0.3: Weak alignment ❌
    """
    
    axes[1, 1].text(0.1, 0.9, summary_text, transform=axes[1, 1].transAxes, 
                   fontsize=10, verticalalignment='top', fontfamily='monospace',
                   bbox=dict(boxstyle="round,pad=0.3", facecolor="lightgray", alpha=0.8))
    
    plt.tight_layout()
    plt.savefig('enhanced_extraction_alignment.png', dpi=300, bbox_inches='tight')
    plt.show()


def generate_enhanced_alignment_report(df, results, semantic_results):
    """Generate comprehensive alignment report."""
    
    with open('enhanced_alignment_report.txt', 'w') as f:
        f.write("=" * 70 + "\n")
        f.write("ENHANCED EXTRACTION METHODS ALIGNMENT REPORT\n")
        f.write("=" * 70 + "\n\n")
        
        f.write(f"Dataset: {len(df)} papers with successful enhanced extractions\n\n")
        
        f.write("COUNT ALIGNMENT ANALYSIS:\n")
        f.write("-" * 30 + "\n")
        f.write(f"Hypothesis Count R²: {results['hyp_count_r2']:.3f}\n")
        f.write(f"Hypothesis Count Correlation: {results['hyp_count_corr']:.3f} (p={results['hyp_count_p']:.3f})\n")
        f.write(f"Evidence Count R²: {results['ev_count_r2']:.3f}\n")
        f.write(f"Evidence Count Correlation: {results['ev_count_corr']:.3f} (p={results['ev_count_p']:.3f})\n\n")
        
        f.write("SEMANTIC SIMILARITY ANALYSIS:\n")
        f.write("-" * 30 + "\n")
        f.write(f"Mean Hypothesis Similarity: {semantic_results['mean_hyp_similarity']:.3f}\n")
        f.write(f"Mean Evidence Similarity: {semantic_results['mean_ev_similarity']:.3f}\n\n")
        
        f.write("DETAILED PAPER COMPARISON:\n")
        f.write("-" * 30 + "\n")
        f.write(f"{'Paper':<30} {'Rule H':<8} {'LLM H':<8} {'Rule E':<8} {'LLM E':<8}\n")
        f.write("-" * 70 + "\n")
        
        for _, row in df.iterrows():
            f.write(f"{row['title']:<30} {row['rule_hyp_count']:<8} {row['llm_hyp_count']:<8} "
                   f"{row['rule_ev_count']:<8} {row['llm_ev_count']:<8}\n")
        
        # Overall assessment
        avg_r2 = np.mean([results['hyp_count_r2'], results['ev_count_r2'], results['hyp_len_r2']])
        avg_semantic = np.mean([semantic_results['mean_hyp_similarity'], semantic_results['mean_ev_similarity']])
        
        f.write(f"\nOVERALL ASSESSMENT:\n")
        f.write("-" * 30 + "\n")
        f.write(f"Average R²: {avg_r2:.3f}\n")
        f.write(f"Average Semantic Similarity: {avg_semantic:.3f}\n")
        
        if avg_r2 > 0.7 and avg_semantic > 0.5:
            f.write("✅ STRONG alignment between enhanced methods\n")
        elif avg_r2 > 0.3 and avg_semantic > 0.3:
            f.write("⚠️  MODERATE alignment between enhanced methods\n")
        else:
            f.write("❌ WEAK alignment between enhanced methods\n")
    
    print("📁 Enhanced alignment report saved to: enhanced_alignment_report.txt")


def main():
    """Run enhanced alignment benchmark."""
    
    print("🎯 ENHANCED EXTRACTION ALIGNMENT BENCHMARK")
    print("=" * 60)
    
    # Load enhanced alignment data
    data = load_enhanced_alignment_data()
    
    if not data:
        return
    
    # Analyze alignment
    results = analyze_enhanced_alignment(data)
    
    if results:
        print(f"\n📊 ENHANCED ALIGNMENT RESULTS:")
        print("-" * 40)
        print(f"Hypothesis Count R²: {results['hyp_count_r2']:.3f}")
        print(f"Evidence Count R²: {results['ev_count_r2']:.3f}")
        print(f"Hypothesis Correlation: {results['hyp_count_corr']:.3f}")
        print(f"Evidence Correlation: {results['ev_count_corr']:.3f}")
        
        avg_r2 = np.mean([results['hyp_count_r2'], results['ev_count_r2']])
        print(f"Average R²: {avg_r2:.3f}")
        
        if avg_r2 > 0.7:
            print("✅ STRONG alignment achieved with enhanced methods!")
        elif avg_r2 > 0.3:
            print("⚠️  MODERATE alignment with enhanced methods")
        else:
            print("❌ Still weak alignment - further improvements needed")
        
        print(f"\n📁 Files created:")
        print("  - enhanced_extraction_alignment.png")
        print("  - enhanced_alignment_report.txt")


if __name__ == '__main__':
    main()
