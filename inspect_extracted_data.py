#!/usr/bin/env python3
"""Quick inspector for extracted data to identify alignment issues."""

import json
from pathlib import Path
import pandas as pd


def inspect_extracted_data(file_path: str = "extracted_papers.json"):
    """Inspect extracted data to identify why alignment is poor."""
    
    if not Path(file_path).exists():
        print(f"❌ {file_path} not found")
        return
    
    with open(file_path, 'r') as f:
        papers = json.load(f)
    
    print("🔍 EXTRACTED DATA INSPECTION")
    print("=" * 50)
    print(f"Total papers: {len(papers)}")
    
    # Check data structure
    sample_paper = papers[0] if papers else {}
    print(f"\nSample paper keys: {list(sample_paper.keys())}")
    
    # Count different extraction types
    has_rule_hyp = sum(1 for p in papers if p.get('hypotheses'))
    has_rule_ev = sum(1 for p in papers if p.get('evidence'))
    has_llm_hyp = sum(1 for p in papers if p.get('llm_enhanced_hypotheses'))
    has_llm_ev = sum(1 for p in papers if p.get('llm_enhanced_evidence'))
    has_llm_flag = sum(1 for p in papers if p.get('llm_extraction_used'))
    
    print(f"\nExtraction Coverage:")
    print(f"Rule-based hypotheses: {has_rule_hyp}/{len(papers)} ({has_rule_hyp/len(papers)*100:.1f}%)")
    print(f"Rule-based evidence: {has_rule_ev}/{len(papers)} ({has_rule_ev/len(papers)*100:.1f}%)")
    print(f"LLM hypotheses: {has_llm_hyp}/{len(papers)} ({has_llm_hyp/len(papers)*100:.1f}%)")
    print(f"LLM evidence: {has_llm_ev}/{len(papers)} ({has_llm_ev/len(papers)*100:.1f}%)")
    print(f"LLM extraction flag: {has_llm_flag}/{len(papers)} ({has_llm_flag/len(papers)*100:.1f}%)")
    
    # Detailed count analysis
    print(f"\n📊 COUNT ANALYSIS")
    print("-" * 30)
    
    data = []
    for i, paper in enumerate(papers[:10]):  # First 10 papers
        rule_hyp_count = len(paper.get('hypotheses', []))
        llm_hyp_count = len(paper.get('llm_enhanced_hypotheses', []))
        rule_ev_count = len(paper.get('evidence', []))
        llm_ev_count = len(paper.get('llm_enhanced_evidence', []))
        
        data.append({
            'paper': i,
            'title': paper.get('title', 'No title')[:30],
            'rule_hyp': rule_hyp_count,
            'llm_hyp': llm_hyp_count,
            'rule_ev': rule_ev_count,
            'llm_ev': llm_ev_count,
            'llm_used': paper.get('llm_extraction_used', False)
        })
    
    df = pd.DataFrame(data)
    print(df.to_string(index=False))
    
    # Check for empty extractions
    print(f"\n❌ EMPTY EXTRACTION ANALYSIS")
    print("-" * 40)
    
    empty_rule_hyp = sum(1 for p in papers if len(p.get('hypotheses', [])) == 0)
    empty_llm_hyp = sum(1 for p in papers if len(p.get('llm_enhanced_hypotheses', [])) == 0)
    empty_rule_ev = sum(1 for p in papers if len(p.get('evidence', [])) == 0)
    empty_llm_ev = sum(1 for p in papers if len(p.get('llm_enhanced_evidence', [])) == 0)
    
    print(f"Empty rule-based hypotheses: {empty_rule_hyp}/{len(papers)} ({empty_rule_hyp/len(papers)*100:.1f}%)")
    print(f"Empty LLM hypotheses: {empty_llm_hyp}/{len(papers)} ({empty_llm_hyp/len(papers)*100:.1f}%)")
    print(f"Empty rule-based evidence: {empty_rule_ev}/{len(papers)} ({empty_rule_ev/len(papers)*100:.1f}%)")
    print(f"Empty LLM evidence: {empty_llm_ev}/{len(papers)} ({empty_llm_ev/len(papers)*100:.1f}%)")
    
    # Sample content inspection
    print(f"\n🔍 SAMPLE CONTENT INSPECTION")
    print("-" * 40)
    
    for i, paper in enumerate(papers[:3]):
        print(f"\nPaper {i+1}: {paper.get('title', 'No title')[:50]}...")
        
        rule_hyp = paper.get('hypotheses', [])
        llm_hyp = paper.get('llm_enhanced_hypotheses', [])
        
        print(f"Rule-based hypotheses ({len(rule_hyp)}):")
        for j, hyp in enumerate(rule_hyp[:2]):
            print(f"  {j+1}. {str(hyp)[:80]}...")
        
        print(f"LLM hypotheses ({len(llm_hyp)}):")
        for j, hyp in enumerate(llm_hyp[:2]):
            if isinstance(hyp, dict):
                print(f"  {j+1}. {hyp.get('text', '')[:80]}... (type: {hyp.get('type', 'unknown')})")
            else:
                print(f"  {j+1}. {str(hyp)[:80]}...")
    
    # Calculate basic statistics for alignment check
    print(f"\n📈 BASIC STATISTICS")
    print("-" * 30)
    
    rule_hyp_counts = [len(p.get('hypotheses', [])) for p in papers]
    llm_hyp_counts = [len(p.get('llm_enhanced_hypotheses', [])) for p in papers]
    rule_ev_counts = [len(p.get('evidence', [])) for p in papers]
    llm_ev_counts = [len(p.get('llm_enhanced_evidence', [])) for p in papers]
    
    import numpy as np
    
    print(f"Rule-based hypotheses: mean={np.mean(rule_hyp_counts):.2f}, std={np.std(rule_hyp_counts):.2f}")
    print(f"LLM hypotheses: mean={np.mean(llm_hyp_counts):.2f}, std={np.std(llm_hyp_counts):.2f}")
    print(f"Rule-based evidence: mean={np.mean(rule_ev_counts):.2f}, std={np.std(rule_ev_counts):.2f}")
    print(f"LLM evidence: mean={np.mean(llm_ev_counts):.2f}, std={np.std(llm_ev_counts):.2f}")
    
    # Identify potential issues
    print(f"\n⚠️  POTENTIAL ISSUES IDENTIFIED")
    print("-" * 40)
    
    issues = []
    
    if empty_llm_hyp > len(papers) * 0.5:
        issues.append("🔴 More than 50% of papers have empty LLM hypotheses")
    
    if empty_llm_ev > len(papers) * 0.5:
        issues.append("🔴 More than 50% of papers have empty LLM evidence")
    
    if np.mean(llm_hyp_counts) == 0:
        issues.append("🔴 LLM hypothesis extraction is completely failing")
    
    if np.mean(rule_hyp_counts) == 0:
        issues.append("🔴 Rule-based hypothesis extraction is completely failing")
    
    if has_llm_flag < len(papers) * 0.5:
        issues.append("🔴 LLM extraction flag indicates most extractions failed")
    
    if abs(np.mean(rule_hyp_counts) - np.mean(llm_hyp_counts)) > 10:
        issues.append("🔴 Huge difference in extraction counts between methods")
    
    if not issues:
        issues.append("✅ No obvious issues detected - alignment problem may be subtle")
    
    for issue in issues:
        print(f"  {issue}")
    
    # Recommendations
    print(f"\n🎯 RECOMMENDATIONS")
    print("-" * 30)
    
    if empty_llm_hyp > len(papers) * 0.3:
        print("1. Run: python test_raw_llm_output.py")
        print("   - Check if LLM is returning valid responses")
        
    if has_llm_flag < len(papers) * 0.5:
        print("2. Check API key and connection:")
        print("   - Verify OPENROUTER_API_KEY is set correctly")
        print("   - Run: python verify_llm_setup.py")
    
    if np.mean(rule_hyp_counts) == 0:
        print("3. Rule-based extraction is failing:")
        print("   - Check PDF text extraction")
        print("   - Review rule-based patterns")
    
    print("4. Run comprehensive diagnostics:")
    print("   - python diagnostic_extraction_analysis.py")


def main():
    """Run data inspection."""
    inspect_extracted_data()


if __name__ == '__main__':
    main()
