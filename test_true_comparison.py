#!/usr/bin/env python3
"""Test true comparison between rule-based and LLM methods."""

import logging
from pathlib import Path
from pdf_extractor import PDFExtractor
from enhanced_extraction_methods import EnhancedRuleBasedExtractor, EnhancedLLMExtractor

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')


def test_true_rule_based_vs_llm():
    """Test true rule-based vs LLM comparison."""
    
    print("🔍 TRUE RULE-BASED vs LLM COMPARISON")
    print("=" * 60)
    
    # Get sample PDF
    pdf_files = list(Path("nm_test").glob("*.pdf"))
    if not pdf_files:
        print("❌ No PDF files found")
        return
    
    sample_pdf = pdf_files[0]
    print(f"Testing with: {sample_pdf.name}")
    
    # Extract text
    extractor = PDFExtractor(use_llm=False)  # No LLM for text extraction
    paper_data = extractor.extract_pdf_text(str(sample_pdf))
    
    if not paper_data:
        print("❌ Failed to extract PDF text")
        return
    
    print(f"✅ Extracted {len(paper_data['full_text'])} characters")
    print(f"   Sections: {list(paper_data['sections'].keys())}")
    
    # Test 1: Pure rule-based extraction (original method)
    print("\n📝 PURE RULE-BASED EXTRACTION (Original)")
    print("-" * 50)
    
    # Force rule-based only by temporarily disabling LLM
    original_use_llm = extractor.use_llm
    extractor.use_llm = False
    extractor.llm_client = None
    
    original_logic = extractor.extract_logic_structure(paper_data)
    
    print(f"Original pure rule-based:")
    print(f"  Hypotheses: {len(original_logic['hypotheses'])}")
    print(f"  Evidence: {len(original_logic['evidence'])}")
    print(f"  Conclusions: {len(original_logic['conclusions'])}")
    
    if original_logic['hypotheses']:
        print(f"  Sample hypothesis: {original_logic['hypotheses'][0][:100]}...")
    if original_logic['evidence']:
        print(f"  Sample evidence: {original_logic['evidence'][0]['text'][:100]}...")
    
    # Test 2: Enhanced rule-based extraction
    print("\n🔧 ENHANCED RULE-BASED EXTRACTION")
    print("-" * 50)
    
    enhanced_rule_extractor = EnhancedRuleBasedExtractor()
    enhanced_rule_results = enhanced_rule_extractor.extract_with_citations(paper_data)
    
    print(f"Enhanced rule-based:")
    print(f"  Hypotheses: {len(enhanced_rule_results['hypotheses'])}")
    print(f"  Evidence: {len(enhanced_rule_results['evidence'])}")
    
    if enhanced_rule_results['hypotheses']:
        print(f"  Sample hypothesis: {enhanced_rule_results['hypotheses'][0].text[:100]}...")
    if enhanced_rule_results['evidence']:
        print(f"  Sample evidence: {enhanced_rule_results['evidence'][0].text[:100]}...")
    
    # Test 3: LLM-enhanced logic structure (what was called "Original Rule-based")
    print("\n🤖 LLM-ENHANCED LOGIC STRUCTURE (Original with LLM)")
    print("-" * 50)
    
    # Re-enable LLM
    llm_extractor = PDFExtractor(use_llm=True)
    if llm_extractor.use_llm and llm_extractor.llm_client:
        llm_logic = llm_extractor.extract_logic_structure(paper_data)
        
        print(f"LLM-enhanced logic structure:")
        print(f"  Hypotheses: {len(llm_logic['hypotheses'])}")
        print(f"  Evidence: {len(llm_logic['evidence'])}")
        print(f"  Conclusions: {len(llm_logic['conclusions'])}")
        
        if llm_logic['hypotheses']:
            print(f"  Sample hypothesis: {llm_logic['hypotheses'][0][:100]}...")
    else:
        print("❌ LLM not available")
        llm_logic = {'hypotheses': [], 'evidence': [], 'conclusions': []}
    
    # Test 4: Enhanced LLM extraction
    print("\n🚀 ENHANCED LLM EXTRACTION")
    print("-" * 50)
    
    try:
        enhanced_llm_extractor = EnhancedLLMExtractor()
        enhanced_llm_results = enhanced_llm_extractor.extract_with_citations(paper_data)
        
        print(f"Enhanced LLM:")
        print(f"  Hypotheses: {len(enhanced_llm_results['hypotheses'])}")
        print(f"  Evidence: {len(enhanced_llm_results['evidence'])}")
        
        if enhanced_llm_results['hypotheses']:
            print(f"  Sample hypothesis: {enhanced_llm_results['hypotheses'][0].text[:100]}...")
            print(f"  Confidence: {enhanced_llm_results['hypotheses'][0].confidence}")
    except Exception as e:
        print(f"❌ Enhanced LLM failed: {e}")
        enhanced_llm_results = {'hypotheses': [], 'evidence': []}
    
    # Summary comparison
    print(f"\n📊 COMPARISON SUMMARY")
    print("=" * 70)
    print(f"{'Method':<35} {'Hypotheses':<12} {'Evidence':<10} {'Conclusions':<12}")
    print("=" * 70)
    print(f"{'Original Pure Rule-based':<35} {len(original_logic['hypotheses']):<12} {len(original_logic['evidence']):<10} {len(original_logic['conclusions']):<12}")
    print(f"{'Enhanced Rule-based':<35} {len(enhanced_rule_results['hypotheses']):<12} {len(enhanced_rule_results['evidence']):<10} {'N/A':<12}")
    print(f"{'LLM-Enhanced Logic Structure':<35} {len(llm_logic['hypotheses']):<12} {len(llm_logic['evidence']):<10} {len(llm_logic['conclusions']):<12}")
    print(f"{'Enhanced LLM':<35} {len(enhanced_llm_results['hypotheses']):<12} {len(enhanced_llm_results['evidence']):<10} {'N/A':<12}")
    
    # Analysis
    print(f"\n🔍 ANALYSIS")
    print("-" * 30)
    
    if len(original_logic['hypotheses']) > 0:
        print("✅ Original rule-based IS finding hypotheses")
    else:
        print("❌ Original rule-based is NOT finding hypotheses")
    
    if len(enhanced_rule_results['hypotheses']) > len(original_logic['hypotheses']):
        print("✅ Enhanced rule-based is finding MORE hypotheses")
    elif len(enhanced_rule_results['hypotheses']) == len(original_logic['hypotheses']):
        print("⚠️  Enhanced rule-based is finding SAME number of hypotheses")
    else:
        print("❌ Enhanced rule-based is finding FEWER hypotheses")
    
    if len(llm_logic['hypotheses']) > 0:
        print("✅ LLM-enhanced logic structure IS working")
    else:
        print("❌ LLM-enhanced logic structure is NOT working")
    
    if len(enhanced_llm_results['hypotheses']) > 0:
        print("✅ Enhanced LLM IS working")
    else:
        print("❌ Enhanced LLM is NOT working")
    
    # Check what the original test was actually comparing
    print(f"\n🎯 ORIGINAL TEST CLARIFICATION")
    print("-" * 40)
    print("The 'Original Rule-based: 1 hypothesis' was actually:")
    print("LLM-Enhanced Logic Structure (not pure rule-based)")
    print("")
    print("True comparison should be:")
    print(f"  Pure Rule-based: {len(original_logic['hypotheses'])} hypotheses")
    print(f"  Enhanced LLM: {len(enhanced_llm_results['hypotheses'])} hypotheses")
    
    return {
        'original_rule': original_logic,
        'enhanced_rule': enhanced_rule_results,
        'llm_logic': llm_logic,
        'enhanced_llm': enhanced_llm_results
    }


def check_why_original_rule_based_fails():
    """Check why original rule-based extraction is failing."""
    
    print("\n🔍 DEBUGGING ORIGINAL RULE-BASED FAILURE")
    print("=" * 50)
    
    # Get sample PDF
    pdf_files = list(Path("nm_test").glob("*.pdf"))
    if not pdf_files:
        return
    
    sample_pdf = pdf_files[0]
    
    # Extract text
    extractor = PDFExtractor(use_llm=False)
    paper_data = extractor.extract_pdf_text(str(sample_pdf))
    
    # Check sections
    sections = paper_data.get('sections', {})
    print(f"Sections found: {list(sections.keys())}")
    
    # Check hypothesis extraction specifically
    abstract_text = sections.get('abstract', '')
    intro_text = sections.get('introduction', '')
    hypotheses_text = abstract_text + ' ' + intro_text
    
    print(f"Abstract text length: {len(abstract_text)}")
    print(f"Introduction text length: {len(intro_text)}")
    print(f"Combined hypothesis text length: {len(hypotheses_text)}")
    
    if len(hypotheses_text) > 0:
        print(f"First 300 chars of hypothesis text:")
        print(f"'{hypotheses_text[:300]}...'")
        
        # Test hypothesis extraction
        hypotheses = extractor.extract_hypotheses(hypotheses_text)
        print(f"Hypotheses found: {len(hypotheses)}")
        
        # Test patterns manually
        import re
        patterns = [
            r"we hypothesize that (.+?)(?:\.|;)",
            r"our hypothesis is (.+?)(?:\.|;)",
            r"we propose that (.+?)(?:\.|;)",
            r"we suggest that (.+?)(?:\.|;)",
            r"hypothesis: (.+?)(?:\.|;)"
        ]
        
        print(f"Manual pattern testing:")
        for pattern in patterns:
            matches = re.findall(pattern, hypotheses_text, re.IGNORECASE | re.DOTALL)
            print(f"  '{pattern}': {len(matches)} matches")
            for match in matches[:1]:
                print(f"    Match: {match[:100]}...")
    else:
        print("❌ No text available for hypothesis extraction")
        print("This explains why rule-based extraction is failing!")


def main():
    """Run true comparison tests."""
    
    results = test_true_rule_based_vs_llm()
    check_why_original_rule_based_fails()
    
    print(f"\n🎯 CONCLUSION")
    print("=" * 50)
    print("The original comparison was misleading because:")
    print("1. 'Original Rule-based' was actually LLM-enhanced logic structure")
    print("2. Pure rule-based extraction is failing due to missing abstract/introduction sections")
    print("3. Enhanced rule-based is working better (finding evidence)")
    print("4. Enhanced LLM is working well with citation-aware prompts")


if __name__ == '__main__':
    main()
