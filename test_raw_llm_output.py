#!/usr/bin/env python3
"""Test script to examine raw LLM output and build proper schema."""

import os
import json
import logging
from typing import Dict, Any, List
from llm_client import OpenRouterClient

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def test_raw_llm_output():
    """Test LLM with sample scientific text and examine raw output."""

    # Check for API key
    api_key = os.getenv('OPENROUTER_API_KEY')
    if not api_key:
        print("❌ OPENROUTER_API_KEY not found in environment")
        print("Please set your OpenRouter API key to test LLM output")
        return False

    print("✅ OPENROUTER_API_KEY found")

    try:
        client = OpenRouterClient()
        print("✅ LLM client initialized")

        # Sample scientific paper text for testing
        sample_text = """
        Abstract
        Microbial communities in marine environments play crucial roles in biogeochemical cycles.
        We hypothesize that temperature gradients significantly influence microbial diversity patterns
        in deep-sea hydrothermal vents. To test this hypothesis, we collected samples from five
        hydrothermal vent sites across different temperature ranges (15-85°C) and analyzed microbial
        communities using 16S rRNA gene sequencing.

        Introduction
        Previous studies have suggested that temperature is a primary driver of microbial community
        structure in extreme environments. We predict that higher temperatures will correlate with
        reduced alpha diversity but increased specialization of thermophilic taxa.

        Methods
        We collected water samples from five hydrothermal vent sites in the Pacific Ocean.
        Temperature measurements were recorded using calibrated thermocouples. DNA extraction
        was performed using the PowerSoil DNA Isolation Kit. 16S rRNA gene amplification
        targeted the V4 region using primers 515F and 806R.

        Results
        Our analysis revealed significant differences in microbial community composition across
        temperature gradients (PERMANOVA, p < 0.001). Alpha diversity (Shannon index) showed
        a strong negative correlation with temperature (R² = 0.78, p < 0.01). We identified
        15 novel thermophilic taxa, with relative abundances increasing significantly at
        temperatures above 60°C (p < 0.05).

        Discussion
        These results support our hypothesis that temperature is a major structuring force
        for microbial communities in hydrothermal vents. The observed decrease in diversity
        with increasing temperature aligns with ecological theory predicting reduced species
        richness in extreme environments. Our findings suggest that thermophilic specialists
        dominate high-temperature niches, while mesophilic generalists are more abundant
        in moderate temperature zones.
        """

        title = "Temperature-driven microbial community structure in deep-sea hydrothermal vents"

        print("\n" + "="*60)
        print("TESTING LOGIC GRAPH EXTRACTION")
        print("="*60)

        # Test logic graph extraction and capture raw output
        messages = [
            {"role": "system", "content": client.extract_logic_graph.__doc__.split('"""')[1].split('"""')[0]},
            {"role": "user", "content": f"Analyze this scientific paper and extract its logical structure:\n\nTitle: {title}\n\nPaper text:\n{sample_text}\n\nExtract the hypotheses, evidence, conclusions, and their logical relationships. Return only valid JSON."}
        ]

        # Get raw response
        response = client._make_sync_request(messages, max_tokens=2000, temperature=0.1)
        raw_content = response["choices"][0]["message"]["content"]

        print("RAW LLM OUTPUT:")
        print("-" * 40)
        print(raw_content)
        print("-" * 40)

        # Save raw output for inspection
        with open('raw_llm_logic_output.txt', 'w') as f:
            f.write(raw_content)
        print("💾 Raw output saved to: raw_llm_logic_output.txt")

        # Try to parse it
        try:
            parsed_json = json.loads(raw_content)
            print("\n✅ Successfully parsed as JSON")
            print("PARSED STRUCTURE:")
            print(json.dumps(parsed_json, indent=2))

            # Analyze structure
            print(f"\nSTRUCTURE ANALYSIS:")
            print(f"- Hypotheses: {len(parsed_json.get('hypotheses', []))}")
            print(f"- Evidence: {len(parsed_json.get('evidence', []))}")
            print(f"- Conclusions: {len(parsed_json.get('conclusions', []))}")

        except json.JSONDecodeError as e:
            print(f"\n❌ JSON parsing failed: {e}")
            print("Attempting to extract JSON from response...")

            import re
            json_match = re.search(r'\{.*\}', raw_content, re.DOTALL)
            if json_match:
                try:
                    extracted_json = json.loads(json_match.group())
                    print("✅ Successfully extracted JSON")
                    print("EXTRACTED STRUCTURE:")
                    print(json.dumps(extracted_json, indent=2))
                except json.JSONDecodeError:
                    print("❌ Even extracted content is not valid JSON")
                    print("ATTEMPTING ROBUST PARSING...")

                    # Test our robust parser
                    from llm_client import OpenRouterClient
                    test_client = OpenRouterClient()
                    robust_result = test_client._robust_json_parse(raw_content, "object")

                    if robust_result:
                        print("✅ Robust parser succeeded!")
                        print(json.dumps(robust_result, indent=2))
                    else:
                        print("❌ Even robust parser failed")
            else:
                print("❌ No JSON structure found in response")

        print("\n" + "="*60)
        print("TESTING HYPOTHESIS EXTRACTION")
        print("="*60)

        # Test hypothesis extraction
        hyp_messages = [
            {"role": "system", "content": """You are an expert at identifying scientific hypotheses in research papers.

Extract the key hypotheses from the given paper. Return a JSON array of hypothesis objects:
[
    {
        "text": "clear hypothesis statement",
        "type": "primary|secondary|null",
        "section": "abstract|introduction|methods|results|discussion",
        "confidence": 0.0-1.0,
        "testable": true|false
    }
]

Focus on:
- Explicit hypothesis statements ("we hypothesize that...", "we predict...", etc.)
- Implicit predictions or expectations
- Research questions that imply testable predictions
- Distinguish between primary (main) and secondary (supporting) hypotheses"""},
            {"role": "user", "content": f"Extract key hypotheses from this scientific paper:\n\nTitle: {title}\n\nPaper text:\n{sample_text}\n\nReturn only the JSON array of hypotheses."}
        ]

        hyp_response = client._make_sync_request(hyp_messages, max_tokens=1500, temperature=0.1)
        hyp_raw_content = hyp_response["choices"][0]["message"]["content"]

        print("RAW HYPOTHESIS OUTPUT:")
        print("-" * 40)
        print(hyp_raw_content)
        print("-" * 40)

        try:
            hyp_parsed = json.loads(hyp_raw_content)
            print("\n✅ Successfully parsed hypothesis JSON")
            print("PARSED HYPOTHESES:")
            print(json.dumps(hyp_parsed, indent=2))
        except json.JSONDecodeError as e:
            print(f"\n❌ Hypothesis JSON parsing failed: {e}")

        print("\n" + "="*60)
        print("TESTING EVIDENCE EXTRACTION")
        print("="*60)

        # Test evidence extraction
        ev_messages = [
            {"role": "system", "content": """You are an expert at identifying scientific evidence in research papers.

Extract lines of evidence from the given paper. Return a JSON array of evidence objects:
[
    {
        "text": "evidence statement",
        "type": "experimental|observational|computational|statistical|literature",
        "section": "methods|results|discussion",
        "strength": "strong|moderate|weak",
        "quantitative": true|false,
        "statistical_significance": "p<0.05|p<0.01|not_reported|not_applicable"
    }
]

Focus on:
- Experimental results and observations
- Statistical analyses and their outcomes
- Computational findings
- Literature support
- Quantitative measurements and their significance"""},
            {"role": "user", "content": f"Extract lines of evidence from this scientific paper:\n\nTitle: {title}\n\nPaper text:\n{sample_text}\n\nReturn only the JSON array of evidence."}
        ]

        ev_response = client._make_sync_request(ev_messages, max_tokens=1500, temperature=0.1)
        ev_raw_content = ev_response["choices"][0]["message"]["content"]

        print("RAW EVIDENCE OUTPUT:")
        print("-" * 40)
        print(ev_raw_content)
        print("-" * 40)

        try:
            ev_parsed = json.loads(ev_raw_content)
            print("\n✅ Successfully parsed evidence JSON")
            print("PARSED EVIDENCE:")
            print(json.dumps(ev_parsed, indent=2))
        except json.JSONDecodeError as e:
            print(f"\n❌ Evidence JSON parsing failed: {e}")

        return True

    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        return False

def analyze_output_patterns():
    """Analyze common patterns in LLM output to improve parsing."""
    print("\n" + "="*60)
    print("OUTPUT PATTERN ANALYSIS")
    print("="*60)

    print("""
Based on testing, common LLM output patterns include:

1. JSON wrapped in markdown code blocks:
   ```json
   {"key": "value"}
   ```

2. JSON with explanatory text before/after:
   Here is the analysis:
   {"key": "value"}
   This shows...

3. Malformed JSON with trailing commas or comments

4. Mixed format responses with partial JSON

RECOMMENDED PARSING STRATEGY:
1. Try direct JSON parsing first
2. Extract content between ```json and ``` markers
3. Extract content between first { and last }
4. Clean common JSON errors (trailing commas, comments)
5. Validate against expected schema
6. Fallback to rule-based extraction
    """)

if __name__ == '__main__':
    print("🧪 Testing Raw LLM Output for Schema Development")
    print("=" * 60)

    success = test_raw_llm_output()
    analyze_output_patterns()

    if success:
        print("\n✅ Raw output testing completed successfully")
        print("Use the output above to refine the JSON parsing strategy")
    else:
        print("\n❌ Raw output testing failed")
        print("Check your OPENROUTER_API_KEY and network connection")
