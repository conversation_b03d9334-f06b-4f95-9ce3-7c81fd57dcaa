[project]
name = "novelty_impact_graphs"
version = "0.1.0"
description = "Scientific paper novelty graph analysis system"
authors = ["Claude Code <<EMAIL>>"]
channels = ["conda-forge", "pytorch", "pyg"]
platforms = ["linux-64"]

[tasks]
install-models = "python -m spacy download en_core_sci_sm"
run-pipeline = "python main.py"
extract-pdfs = "python pdf_extractor.py"

[dependencies]
python = "3.10.*"
numpy = ">=1.24"
pandas = ">=2.0"
scipy = ">=1.10"
scikit-learn = ">=1.3"
matplotlib = ">=3.7"
plotly = ">=5.14"
networkx = ">=3.1"
pyvis = ">=0.3.2"
beautifulsoup4 = ">=4.12"
requests = ">=2.31"
tqdm = ">=4.65"
click = ">=8.1"

# PDF processing
pymupdf = ">=1.23"
pdfplumber = ">=0.10"

# NLP dependencies
spacy = ">=3.6"
nltk = ">=3.8"
gensim = ">=4.3"

# Deep learning
pytorch = ">=2.0"
transformers = ">=4.30"
sentence-transformers = ">=2.2"

# Graph visualization
graphviz = ">=0.20"
cairo = ">=1.16"
pycairo = ">=1.24"

# Development tools
ipython = ">=8.14"
jupyter = ">=1.0"
pytest = ">=7.4"

[pypi-dependencies]
scispacy = "*"
grobid-client-python = "*"
python-louvain = "*"