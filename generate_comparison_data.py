#!/usr/bin/env python3
"""Generate both rule-based and LLM extractions for comparison."""

import json
import logging
from pathlib import Path
from pdf_extractor import PDFExtractor
from tqdm import tqdm

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')


def generate_both_extractions(input_dir: str = "nm_test", output_file: str = "comparison_data.json"):
    """Generate both rule-based and LLM extractions for comparison."""
    
    print("🔄 Generating Both Rule-Based and LLM Extractions")
    print("="*60)
    
    pdf_files = list(Path(input_dir).glob('*.pdf'))
    if not pdf_files:
        print(f"❌ No PDF files found in {input_dir}")
        return
    
    print(f"📁 Found {len(pdf_files)} PDF files")
    
    # Initialize extractors
    print("🤖 Initializing LLM extractor...")
    llm_extractor = PDFExtractor(use_llm=True)
    
    print("📝 Initializing rule-based extractor...")
    rule_extractor = PDFExtractor(use_llm=False)
    
    comparison_data = []
    
    for pdf_path in tqdm(pdf_files, desc="Processing PDFs"):
        print(f"\n📄 Processing: {pdf_path.name}")
        
        try:
            # Extract with rule-based method
            print("  📝 Rule-based extraction...")
            rule_data = rule_extractor.extract_pdf_text(str(pdf_path))
            
            if not rule_data:
                print(f"  ❌ Failed to extract text from {pdf_path}")
                continue
            
            # Get rule-based logic structure
            rule_logic = rule_extractor.extract_logic_structure(rule_data)
            
            # Extract with LLM method
            print("  🤖 LLM extraction...")
            llm_data = llm_extractor.extract_pdf_text(str(pdf_path))
            
            if not llm_data:
                print(f"  ❌ Failed to extract text from {pdf_path}")
                continue
            
            # Get LLM logic structure
            llm_logic = llm_extractor.extract_logic_structure(llm_data)
            
            # Get LLM enhanced data
            if llm_extractor.use_llm and llm_extractor.llm_client:
                try:
                    enhanced_hypotheses = llm_extractor.extract_enhanced_hypotheses(llm_data)
                    enhanced_evidence = llm_extractor.extract_enhanced_evidence(llm_data)
                    llm_extraction_used = True
                except Exception as e:
                    print(f"  ⚠️  LLM enhancement failed: {e}")
                    enhanced_hypotheses = []
                    enhanced_evidence = []
                    llm_extraction_used = False
            else:
                enhanced_hypotheses = []
                enhanced_evidence = []
                llm_extraction_used = False
            
            # Combine data for comparison
            combined_data = {
                'file_path': str(pdf_path),
                'title': rule_data.get('title', ''),
                'pages': rule_data.get('pages', 0),
                
                # Rule-based extraction
                'rule_based': {
                    'hypotheses': rule_logic['hypotheses'],
                    'evidence': rule_logic['evidence'],
                    'conclusions': rule_logic['conclusions'],
                    'relationships': rule_logic['relationships']
                },
                
                # LLM extraction (using same base text)
                'llm_based': {
                    'hypotheses': llm_logic['hypotheses'],
                    'evidence': llm_logic['evidence'],
                    'conclusions': llm_logic['conclusions'],
                    'relationships': llm_logic['relationships']
                },
                
                # LLM enhanced extraction
                'llm_enhanced_hypotheses': enhanced_hypotheses,
                'llm_enhanced_evidence': enhanced_evidence,
                'llm_extraction_used': llm_extraction_used,
                
                # For compatibility with existing code
                'hypotheses': rule_logic['hypotheses'],
                'evidence': rule_logic['evidence'],
                'conclusions': rule_logic['conclusions'],
                'logic_relationships': rule_logic['relationships']
            }
            
            comparison_data.append(combined_data)
            
            print(f"  ✅ Extracted - Rule: {len(rule_logic['hypotheses'])}H/{len(rule_logic['evidence'])}E, "
                  f"LLM: {len(llm_logic['hypotheses'])}H/{len(llm_logic['evidence'])}E, "
                  f"Enhanced: {len(enhanced_hypotheses)}H/{len(enhanced_evidence)}E")
            
        except Exception as e:
            print(f"  ❌ Error processing {pdf_path}: {e}")
            continue
    
    # Save comparison data
    print(f"\n💾 Saving comparison data to {output_file}...")
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(comparison_data, f, indent=2, ensure_ascii=False)
    
    print(f"✅ Generated comparison data for {len(comparison_data)} papers")
    
    # Print summary
    print(f"\n📊 EXTRACTION SUMMARY:")
    print("-"*40)
    
    rule_hyp_counts = [len(p['rule_based']['hypotheses']) for p in comparison_data]
    llm_hyp_counts = [len(p['llm_based']['hypotheses']) for p in comparison_data]
    enhanced_hyp_counts = [len(p['llm_enhanced_hypotheses']) for p in comparison_data]
    
    rule_ev_counts = [len(p['rule_based']['evidence']) for p in comparison_data]
    llm_ev_counts = [len(p['llm_based']['evidence']) for p in comparison_data]
    enhanced_ev_counts = [len(p['llm_enhanced_evidence']) for p in comparison_data]
    
    print(f"Rule-based Hypotheses: avg={sum(rule_hyp_counts)/len(rule_hyp_counts):.1f}, total={sum(rule_hyp_counts)}")
    print(f"LLM Hypotheses: avg={sum(llm_hyp_counts)/len(llm_hyp_counts):.1f}, total={sum(llm_hyp_counts)}")
    print(f"Enhanced Hypotheses: avg={sum(enhanced_hyp_counts)/len(enhanced_hyp_counts):.1f}, total={sum(enhanced_hyp_counts)}")
    
    print(f"Rule-based Evidence: avg={sum(rule_ev_counts)/len(rule_ev_counts):.1f}, total={sum(rule_ev_counts)}")
    print(f"LLM Evidence: avg={sum(llm_ev_counts)/len(llm_ev_counts):.1f}, total={sum(llm_ev_counts)}")
    print(f"Enhanced Evidence: avg={sum(enhanced_ev_counts)/len(enhanced_ev_counts):.1f}, total={sum(enhanced_ev_counts)}")
    
    llm_success_rate = sum(1 for p in comparison_data if p['llm_extraction_used']) / len(comparison_data)
    print(f"LLM Enhancement Success Rate: {llm_success_rate:.1%}")
    
    return comparison_data


def check_existing_data(file_path: str):
    """Check what extraction data already exists."""
    
    if not Path(file_path).exists():
        print(f"❌ {file_path} not found")
        return None
    
    with open(file_path, 'r') as f:
        papers = json.load(f)
    
    print(f"📊 Analyzing existing data in {file_path}:")
    print("-"*50)
    
    total_papers = len(papers)
    has_rule_based = sum(1 for p in papers if p.get('hypotheses'))
    has_llm_enhanced = sum(1 for p in papers if p.get('llm_enhanced_hypotheses'))
    has_both = sum(1 for p in papers if p.get('hypotheses') and p.get('llm_enhanced_hypotheses'))
    
    print(f"Total papers: {total_papers}")
    print(f"With rule-based extraction: {has_rule_based}")
    print(f"With LLM enhanced extraction: {has_llm_enhanced}")
    print(f"With both extractions: {has_both}")
    
    if has_both >= 2:
        print("✅ Sufficient data for comparison benchmark")
        return papers
    else:
        print("⚠️  Insufficient data for comparison - need both extraction types")
        return None


def main():
    """Main function to generate or check comparison data."""
    
    print("🔍 Extraction Comparison Data Generator")
    print("="*50)
    
    # Check if we already have good comparison data
    existing_data = check_existing_data("extracted_papers.json")
    
    if existing_data:
        print("\n✅ Existing data is sufficient for comparison")
        print("You can run: python simple_alignment_benchmark.py")
        return
    
    # Generate new comparison data
    print("\n🔄 Generating new comparison data...")
    
    # Check if we have PDFs
    pdf_dir = "nm_test"
    if not Path(pdf_dir).exists() or not list(Path(pdf_dir).glob('*.pdf')):
        print(f"❌ No PDFs found in {pdf_dir}")
        print("Please ensure you have PDF files to process")
        return
    
    # Generate both extractions
    comparison_data = generate_both_extractions(pdf_dir, "comparison_data.json")
    
    if comparison_data:
        print(f"\n🎯 Next steps:")
        print("1. Run benchmark: python simple_alignment_benchmark.py")
        print("2. Or detailed benchmark: python benchmark_extraction_methods.py")
        print("3. Data saved in: comparison_data.json")
    else:
        print("❌ Failed to generate comparison data")


if __name__ == '__main__':
    main()
