#!/usr/bin/env python3
"""Utilities for comparing papers using LLM-enhanced data."""

import json
import numpy as np
from typing import Dict, List, Any, Tuple
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.metrics.pairwise import cosine_similarity
import logging

logger = logging.getLogger(__name__)


class PaperComparisonEngine:
    """Engine for comparing papers using LLM-enhanced logic graphs and hypotheses."""
    
    def __init__(self):
        self.vectorizer = TfidfVectorizer(
            max_features=1000,
            stop_words='english',
            ngram_range=(1, 2)
        )
    
    def compare_papers(self, paper1: Dict[str, Any], paper2: Dict[str, Any]) -> Dict[str, float]:
        """Comprehensive comparison between two papers."""
        
        comparison_scores = {
            'hypothesis_similarity': self.compare_hypotheses(paper1, paper2),
            'evidence_similarity': self.compare_evidence(paper1, paper2),
            'conclusion_similarity': self.compare_conclusions(paper1, paper2),
            'logic_structure_similarity': self.compare_logic_structures(paper1, paper2),
            'methodology_similarity': self.compare_methodologies(paper1, paper2),
            'overall_similarity': 0.0
        }
        
        # Calculate weighted overall similarity
        weights = {
            'hypothesis_similarity': 0.25,
            'evidence_similarity': 0.25,
            'conclusion_similarity': 0.20,
            'logic_structure_similarity': 0.15,
            'methodology_similarity': 0.15
        }
        
        comparison_scores['overall_similarity'] = sum(
            comparison_scores[key] * weight 
            for key, weight in weights.items()
        )
        
        return comparison_scores
    
    def compare_hypotheses(self, paper1: Dict[str, Any], paper2: Dict[str, Any]) -> float:
        """Compare hypotheses between two papers."""
        
        # Get LLM-enhanced hypotheses if available, fallback to rule-based
        hyp1 = self._get_hypotheses_text(paper1)
        hyp2 = self._get_hypotheses_text(paper2)
        
        if not hyp1 or not hyp2:
            return 0.0
        
        # Calculate semantic similarity
        similarity = self._calculate_text_similarity(hyp1, hyp2)
        
        # Bonus for hypothesis type matching (if LLM data available)
        type_bonus = self._calculate_hypothesis_type_similarity(paper1, paper2)
        
        return min(1.0, similarity + type_bonus * 0.1)
    
    def compare_evidence(self, paper1: Dict[str, Any], paper2: Dict[str, Any]) -> float:
        """Compare evidence between two papers."""
        
        ev1 = self._get_evidence_text(paper1)
        ev2 = self._get_evidence_text(paper2)
        
        if not ev1 or not ev2:
            return 0.0
        
        # Base semantic similarity
        similarity = self._calculate_text_similarity(ev1, ev2)
        
        # Evidence type and strength bonuses
        type_bonus = self._calculate_evidence_type_similarity(paper1, paper2)
        strength_bonus = self._calculate_evidence_strength_similarity(paper1, paper2)
        
        return min(1.0, similarity + type_bonus * 0.1 + strength_bonus * 0.05)
    
    def compare_conclusions(self, paper1: Dict[str, Any], paper2: Dict[str, Any]) -> float:
        """Compare conclusions between two papers."""
        
        conc1 = self._get_conclusions_text(paper1)
        conc2 = self._get_conclusions_text(paper2)
        
        if not conc1 or not conc2:
            return 0.0
        
        return self._calculate_text_similarity(conc1, conc2)
    
    def compare_logic_structures(self, paper1: Dict[str, Any], paper2: Dict[str, Any]) -> float:
        """Compare logical argument structures between papers."""
        
        # Get logic relationships if available
        rel1 = paper1.get('logic_relationships', [])
        rel2 = paper2.get('logic_relationships', [])
        
        if not rel1 or not rel2:
            return 0.0
        
        # Compare relationship patterns
        pattern1 = self._extract_logic_patterns(rel1)
        pattern2 = self._extract_logic_patterns(rel2)
        
        # Calculate pattern similarity
        common_patterns = set(pattern1) & set(pattern2)
        total_patterns = set(pattern1) | set(pattern2)
        
        if not total_patterns:
            return 0.0
        
        return len(common_patterns) / len(total_patterns)
    
    def compare_methodologies(self, paper1: Dict[str, Any], paper2: Dict[str, Any]) -> float:
        """Compare methodological approaches."""
        
        # Get methods sections
        methods1 = paper1.get('sections', {}).get('methods', '')
        methods2 = paper2.get('sections', {}).get('methods', '')
        
        if not methods1 or not methods2:
            return 0.0
        
        return self._calculate_text_similarity([methods1], [methods2])
    
    def _get_hypotheses_text(self, paper: Dict[str, Any]) -> List[str]:
        """Extract hypothesis text from paper data."""
        
        hypotheses = []
        
        # Try LLM-enhanced hypotheses first
        llm_hyp = paper.get('llm_enhanced_hypotheses', [])
        if llm_hyp:
            for hyp in llm_hyp:
                if isinstance(hyp, dict):
                    hypotheses.append(hyp.get('text', ''))
                else:
                    hypotheses.append(str(hyp))
        
        # Fallback to rule-based hypotheses
        if not hypotheses:
            rule_hyp = paper.get('hypotheses', [])
            hypotheses.extend([str(h) for h in rule_hyp])
        
        return [h for h in hypotheses if h.strip()]
    
    def _get_evidence_text(self, paper: Dict[str, Any]) -> List[str]:
        """Extract evidence text from paper data."""
        
        evidence = []
        
        # Try LLM-enhanced evidence first
        llm_ev = paper.get('llm_enhanced_evidence', [])
        if llm_ev:
            for ev in llm_ev:
                if isinstance(ev, dict):
                    evidence.append(ev.get('text', ''))
                else:
                    evidence.append(str(ev))
        
        # Fallback to rule-based evidence
        if not evidence:
            rule_ev = paper.get('evidence', [])
            for ev in rule_ev:
                if isinstance(ev, dict):
                    evidence.append(ev.get('text', ''))
                else:
                    evidence.append(str(ev))
        
        return [e for e in evidence if e.strip()]
    
    def _get_conclusions_text(self, paper: Dict[str, Any]) -> List[str]:
        """Extract conclusion text from paper data."""
        
        conclusions = []
        
        # Get from rule-based extraction
        rule_conc = paper.get('conclusions', [])
        conclusions.extend([str(c) for c in rule_conc])
        
        return [c for c in conclusions if c.strip()]
    
    def _calculate_text_similarity(self, texts1: List[str], texts2: List[str]) -> float:
        """Calculate semantic similarity between two sets of texts."""
        
        if not texts1 or not texts2:
            return 0.0
        
        # Combine texts
        combined_texts = texts1 + texts2
        
        try:
            # Vectorize texts
            tfidf_matrix = self.vectorizer.fit_transform(combined_texts)
            
            # Calculate similarity between the two groups
            group1_vectors = tfidf_matrix[:len(texts1)]
            group2_vectors = tfidf_matrix[len(texts1):]
            
            # Calculate pairwise similarities and take the mean
            similarities = cosine_similarity(group1_vectors, group2_vectors)
            
            return float(np.mean(similarities))
            
        except Exception as e:
            logger.warning(f"Error calculating text similarity: {e}")
            return 0.0
    
    def _calculate_hypothesis_type_similarity(self, paper1: Dict[str, Any], paper2: Dict[str, Any]) -> float:
        """Calculate similarity based on hypothesis types."""
        
        hyp1 = paper1.get('llm_enhanced_hypotheses', [])
        hyp2 = paper2.get('llm_enhanced_hypotheses', [])
        
        if not hyp1 or not hyp2:
            return 0.0
        
        types1 = [h.get('type', 'unknown') for h in hyp1 if isinstance(h, dict)]
        types2 = [h.get('type', 'unknown') for h in hyp2 if isinstance(h, dict)]
        
        if not types1 or not types2:
            return 0.0
        
        common_types = set(types1) & set(types2)
        total_types = set(types1) | set(types2)
        
        return len(common_types) / len(total_types) if total_types else 0.0
    
    def _calculate_evidence_type_similarity(self, paper1: Dict[str, Any], paper2: Dict[str, Any]) -> float:
        """Calculate similarity based on evidence types."""
        
        ev1 = paper1.get('llm_enhanced_evidence', [])
        ev2 = paper2.get('llm_enhanced_evidence', [])
        
        if not ev1 or not ev2:
            return 0.0
        
        types1 = [e.get('type', 'unknown') for e in ev1 if isinstance(e, dict)]
        types2 = [e.get('type', 'unknown') for e in ev2 if isinstance(e, dict)]
        
        if not types1 or not types2:
            return 0.0
        
        common_types = set(types1) & set(types2)
        total_types = set(types1) | set(types2)
        
        return len(common_types) / len(total_types) if total_types else 0.0
    
    def _calculate_evidence_strength_similarity(self, paper1: Dict[str, Any], paper2: Dict[str, Any]) -> float:
        """Calculate similarity based on evidence strength."""
        
        ev1 = paper1.get('llm_enhanced_evidence', [])
        ev2 = paper2.get('llm_enhanced_evidence', [])
        
        if not ev1 or not ev2:
            return 0.0
        
        strengths1 = [e.get('strength', 'moderate') for e in ev1 if isinstance(e, dict)]
        strengths2 = [e.get('strength', 'moderate') for e in ev2 if isinstance(e, dict)]
        
        if not strengths1 or not strengths2:
            return 0.0
        
        # Map strengths to numeric values
        strength_map = {'weak': 1, 'moderate': 2, 'strong': 3}
        
        avg_strength1 = np.mean([strength_map.get(s, 2) for s in strengths1])
        avg_strength2 = np.mean([strength_map.get(s, 2) for s in strengths2])
        
        # Calculate similarity based on strength difference
        max_diff = 2.0  # Maximum possible difference
        actual_diff = abs(avg_strength1 - avg_strength2)
        
        return 1.0 - (actual_diff / max_diff)
    
    def _extract_logic_patterns(self, relationships: List[Dict[str, Any]]) -> List[str]:
        """Extract logical relationship patterns."""
        
        patterns = []
        for rel in relationships:
            if isinstance(rel, dict):
                pattern = f"{rel.get('from_type', 'unknown')}-{rel.get('relationship', 'unknown')}-{rel.get('to_type', 'unknown')}"
                patterns.append(pattern)
        
        return patterns


def create_comparison_matrix(papers: List[Dict[str, Any]]) -> np.ndarray:
    """Create a comparison matrix for all papers."""
    
    engine = PaperComparisonEngine()
    n_papers = len(papers)
    comparison_matrix = np.zeros((n_papers, n_papers))
    
    for i in range(n_papers):
        for j in range(i + 1, n_papers):
            scores = engine.compare_papers(papers[i], papers[j])
            similarity = scores['overall_similarity']
            
            comparison_matrix[i, j] = similarity
            comparison_matrix[j, i] = similarity  # Symmetric matrix
    
    # Set diagonal to 1.0 (self-similarity)
    np.fill_diagonal(comparison_matrix, 1.0)
    
    return comparison_matrix


def find_most_similar_papers(papers: List[Dict[str, Any]], top_k: int = 5) -> List[Tuple[int, int, float]]:
    """Find the most similar paper pairs."""
    
    comparison_matrix = create_comparison_matrix(papers)
    
    # Get upper triangle indices (avoid duplicates and self-comparisons)
    upper_triangle = np.triu_indices_from(comparison_matrix, k=1)
    similarities = comparison_matrix[upper_triangle]
    
    # Get top-k most similar pairs
    top_indices = np.argsort(similarities)[-top_k:][::-1]
    
    results = []
    for idx in top_indices:
        i, j = upper_triangle[0][idx], upper_triangle[1][idx]
        similarity = similarities[idx]
        results.append((i, j, similarity))
    
    return results
