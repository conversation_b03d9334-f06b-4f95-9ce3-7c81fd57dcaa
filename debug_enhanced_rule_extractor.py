#!/usr/bin/env python3
"""Debug the enhanced rule-based extractor to see why it's finding nothing."""

import re
from pathlib import Path
from pdf_extractor import PDFExtractor
from enhanced_extraction_methods import EnhancedRuleBasedExtractor, TextPreprocessor


def debug_rule_based_extraction():
    """Debug why enhanced rule-based extraction is failing."""
    
    print("🔍 DEBUGGING ENHANCED RULE-BASED EXTRACTOR")
    print("=" * 60)
    
    # Get a sample PDF
    pdf_files = list(Path("nm_test").glob("*.pdf"))
    if not pdf_files:
        print("❌ No PDF files found")
        return
    
    sample_pdf = pdf_files[0]
    print(f"Testing with: {sample_pdf.name}")
    
    # Extract text with original method
    print("\n📄 Extracting text with original PDF extractor...")
    original_extractor = PDFExtractor(use_llm=False)
    paper_data = original_extractor.extract_pdf_text(str(sample_pdf))
    
    if not paper_data:
        print("❌ Failed to extract PDF text")
        return
    
    print(f"✅ Extracted {len(paper_data['full_text'])} characters")
    
    # Test original rule-based extraction
    print("\n📝 Testing original rule-based extraction...")
    original_logic = original_extractor.extract_logic_structure(paper_data)
    
    print(f"Original rule-based results:")
    print(f"  Hypotheses: {len(original_logic['hypotheses'])}")
    print(f"  Evidence: {len(original_logic['evidence'])}")
    
    if original_logic['hypotheses']:
        print(f"  Sample hypothesis: {original_logic['hypotheses'][0][:100]}...")
    
    # Test enhanced rule-based extraction
    print("\n🔧 Testing enhanced rule-based extraction...")
    enhanced_extractor = EnhancedRuleBasedExtractor()
    enhanced_results = enhanced_extractor.extract_with_citations(paper_data)
    
    print(f"Enhanced rule-based results:")
    print(f"  Hypotheses: {len(enhanced_results['hypotheses'])}")
    print(f"  Evidence: {len(enhanced_results['evidence'])}")
    
    # Debug the preprocessing
    print("\n🔍 DEBUGGING PREPROCESSING...")
    preprocessor = TextPreprocessor()
    processed = preprocessor.preprocess_text(paper_data['full_text'])
    
    print(f"Preprocessing results:")
    print(f"  Original length: {processed['original_length']}")
    print(f"  Processed length: {processed['processed_length']}")
    print(f"  Sections found: {list(processed['sections'].keys())}")
    print(f"  Is truncated: {processed['is_truncated']}")
    
    # Check section content
    for section_name, section_data in processed['sections'].items():
        print(f"\n  Section '{section_name}': {len(section_data['text'])} chars")
        if len(section_data['text']) > 0:
            print(f"    First 200 chars: {section_data['text'][:200]}...")
    
    # Debug pattern matching
    print("\n🔍 DEBUGGING PATTERN MATCHING...")
    
    # Test hypothesis patterns on each section
    hypothesis_patterns = [
        (r"we hypothesize(?:d)? that (.+?)(?:\.|;|$)", "explicit_hypothesis"),
        (r"our hypothesis (?:is|was) that (.+?)(?:\.|;|$)", "explicit_hypothesis"),
        (r"we propose(?:d)? that (.+?)(?:\.|;|$)", "proposal"),
        (r"we predict(?:ed)? that (.+?)(?:\.|;|$)", "prediction"),
        (r"we expect(?:ed)? that (.+?)(?:\.|;|$)", "expectation"),
        (r"hypothesis: (.+?)(?:\.|;|$)", "labeled_hypothesis")
    ]
    
    total_matches = 0
    for section_name, section_data in processed['sections'].items():
        section_text = section_data['text']
        print(f"\n  Testing patterns in section '{section_name}':")
        
        for pattern, pattern_type in hypothesis_patterns:
            matches = list(re.finditer(pattern, section_text, re.IGNORECASE | re.DOTALL))
            if matches:
                print(f"    Pattern '{pattern_type}': {len(matches)} matches")
                for i, match in enumerate(matches[:2]):  # Show first 2
                    hypothesis_text = match.group(1).strip()
                    print(f"      Match {i+1}: {hypothesis_text[:100]}...")
                total_matches += len(matches)
            else:
                print(f"    Pattern '{pattern_type}': 0 matches")
    
    print(f"\n  Total hypothesis pattern matches: {total_matches}")
    
    # Test evidence patterns
    evidence_patterns = [
        (r"(?:we|our) (?:found|observed|measured|detected|discovered) that (.+?)(?:\.|;|$)", "experimental_finding"),
        (r"results (?:show|indicate|demonstrate|reveal) that (.+?)(?:\.|;|$)", "result_statement"),
        (r"analysis (?:revealed|showed|indicated) that (.+?)(?:\.|;|$)", "analytical_finding"),
        (r"data (?:show|indicate|suggest) that (.+?)(?:\.|;|$)", "data_evidence"),
        (r"(?:significant|substantial) (?:increase|decrease|difference|effect) (.+?)(?:\.|;|$)", "statistical_evidence"),
        (r"p\s*[<>=]\s*0\.\d+", "statistical_significance")
    ]
    
    total_ev_matches = 0
    for section_name, section_data in processed['sections'].items():
        section_text = section_data['text']
        print(f"\n  Testing evidence patterns in section '{section_name}':")
        
        for pattern, pattern_type in evidence_patterns:
            matches = list(re.finditer(pattern, section_text, re.IGNORECASE | re.DOTALL))
            if matches:
                print(f"    Pattern '{pattern_type}': {len(matches)} matches")
                for i, match in enumerate(matches[:2]):  # Show first 2
                    if match.groups():
                        evidence_text = match.group(1).strip()
                    else:
                        evidence_text = match.group(0).strip()
                    print(f"      Match {i+1}: {evidence_text[:100]}...")
                total_ev_matches += len(matches)
            else:
                print(f"    Pattern '{pattern_type}': 0 matches")
    
    print(f"\n  Total evidence pattern matches: {total_ev_matches}")
    
    # Compare with original patterns
    print("\n🔍 TESTING ORIGINAL PATTERNS...")
    
    # Test original hypothesis extraction
    original_hyp_text = paper_data.get('sections', {}).get('abstract', '') + ' ' + paper_data.get('sections', {}).get('introduction', '')
    original_hypotheses = original_extractor.extract_hypotheses(original_hyp_text)
    
    print(f"Original hypothesis extraction on same text:")
    print(f"  Found: {len(original_hypotheses)} hypotheses")
    for i, hyp in enumerate(original_hypotheses):
        print(f"    {i+1}: {hyp[:100]}...")
    
    # Check what the original extract_hypotheses method does
    print(f"\n🔍 CHECKING ORIGINAL EXTRACT_HYPOTHESES METHOD...")
    
    # Let's manually test the original patterns
    original_patterns = [
        r"we hypothesize that (.+?)(?:\.|;)",
        r"our hypothesis is (.+?)(?:\.|;)",
        r"we propose that (.+?)(?:\.|;)",
        r"we suggest that (.+?)(?:\.|;)",
        r"hypothesis: (.+?)(?:\.|;)"
    ]
    
    full_text = paper_data['full_text']
    print(f"Testing original patterns on full text ({len(full_text)} chars):")
    
    for pattern in original_patterns:
        matches = re.findall(pattern, full_text, re.IGNORECASE | re.DOTALL)
        print(f"  Pattern '{pattern}': {len(matches)} matches")
        for i, match in enumerate(matches[:2]):
            print(f"    Match {i+1}: {match[:100]}...")


def compare_section_identification():
    """Compare original vs enhanced section identification."""
    
    print("\n🔍 COMPARING SECTION IDENTIFICATION")
    print("=" * 50)
    
    # Get sample PDF
    pdf_files = list(Path("nm_test").glob("*.pdf"))
    if not pdf_files:
        return
    
    sample_pdf = pdf_files[0]
    
    # Original extraction
    original_extractor = PDFExtractor(use_llm=False)
    paper_data = original_extractor.extract_pdf_text(str(sample_pdf))
    
    print("Original section identification:")
    for section_name, section_text in paper_data['sections'].items():
        print(f"  {section_name}: {len(section_text)} chars")
    
    # Enhanced preprocessing
    preprocessor = TextPreprocessor()
    processed = preprocessor.preprocess_text(paper_data['full_text'])
    
    print("\nEnhanced section identification:")
    for section_name, section_data in processed['sections'].items():
        print(f"  {section_name}: {len(section_data['text'])} chars")
    
    # Check if sections are different
    original_sections = set(paper_data['sections'].keys())
    enhanced_sections = set(processed['sections'].keys())
    
    print(f"\nSection differences:")
    print(f"  Only in original: {original_sections - enhanced_sections}")
    print(f"  Only in enhanced: {enhanced_sections - original_sections}")
    print(f"  Common: {original_sections & enhanced_sections}")


def main():
    """Run debugging analysis."""
    debug_rule_based_extraction()
    compare_section_identification()


if __name__ == '__main__':
    main()
