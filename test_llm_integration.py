#!/usr/bin/env python3
"""Test script for LLM integration."""

import os
import json
import logging
from pdf_extractor import PDFExtractor
from llm_client import OpenRouterClient
from paper_comparison_utils import PaperComparisonEngine

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def test_llm_client():
    """Test basic LLM client functionality."""
    print("Testing LLM client...")

    try:
        client = OpenRouterClient()
        print("✓ LLM client initialized successfully")

        # Test with sample scientific text
        sample_text = """
        Abstract: We hypothesize that microbial communities in soil exhibit distinct patterns
        of diversity based on pH levels. Our study analyzed 50 soil samples across different
        pH ranges using 16S rRNA sequencing. Results showed significant differences in
        alpha diversity (p < 0.01) between acidic and alkaline soils. We conclude that
        soil pH is a major driver of microbial community structure.
        """

        # Test hypothesis extraction
        hypotheses = client.extract_key_hypotheses(sample_text, "Soil pH and Microbial Diversity")
        print(f"✓ Extracted {len(hypotheses)} hypotheses")

        # Test evidence extraction
        evidence = client.extract_lines_of_evidence(sample_text, "Soil pH and Microbial Diversity")
        print(f"✓ Extracted {len(evidence)} lines of evidence")

        # Test logic graph extraction
        logic_graph = client.extract_logic_graph(sample_text, "Soil pH and Microbial Diversity")
        print(f"✓ Extracted logic graph with {len(logic_graph.get('hypotheses', []))} hypotheses")

        return True

    except Exception as e:
        print(f"✗ LLM client test failed: {e}")
        return False

def test_pdf_extractor_integration():
    """Test PDF extractor with LLM integration."""
    print("\nTesting PDF extractor integration...")

    try:
        # Test with LLM enabled
        extractor_llm = PDFExtractor(use_llm=True)
        print(f"✓ PDF extractor with LLM: {extractor_llm.use_llm}")

        # Test without LLM
        extractor_no_llm = PDFExtractor(use_llm=False)
        print(f"✓ PDF extractor without LLM: {not extractor_no_llm.use_llm}")

        return True

    except Exception as e:
        print(f"✗ PDF extractor integration test failed: {e}")
        return False

def test_sample_paper():
    """Test extraction on a sample paper if available."""
    print("\nTesting sample paper extraction...")

    # Check if we have any PDFs to test with
    pdf_files = list(os.listdir('nm_test'))[:1] if os.path.exists('nm_test') else []

    if not pdf_files:
        print("ℹ No sample PDFs found in nm_test directory")
        return True

    try:
        extractor = PDFExtractor(use_llm=True)

        # Test on first PDF
        pdf_path = os.path.join('nm_test', pdf_files[0])
        print(f"Testing with: {pdf_files[0]}")

        paper_data = extractor.extract_pdf_text(pdf_path)
        if paper_data:
            print(f"✓ Extracted text from {pdf_files[0]}")
            print(f"  - Title: {paper_data.get('title', 'N/A')[:50]}...")
            print(f"  - Pages: {paper_data.get('pages', 'N/A')}")

            # Test logic structure extraction
            logic_structure = extractor.extract_logic_structure(paper_data)
            print(f"✓ Logic structure extracted:")
            print(f"  - Hypotheses: {len(logic_structure.get('hypotheses', []))}")
            print(f"  - Evidence: {len(logic_structure.get('evidence', []))}")
            print(f"  - Conclusions: {len(logic_structure.get('conclusions', []))}")

            # Test enhanced extraction if LLM is available
            if extractor.use_llm and extractor.llm_client:
                enhanced_hyp = extractor.extract_enhanced_hypotheses(paper_data)
                enhanced_ev = extractor.extract_enhanced_evidence(paper_data)
                print(f"✓ LLM enhancement:")
                print(f"  - Enhanced hypotheses: {len(enhanced_hyp)}")
                print(f"  - Enhanced evidence: {len(enhanced_ev)}")
        else:
            print(f"✗ Failed to extract text from {pdf_files[0]}")
            return False

        return True

    except Exception as e:
        print(f"✗ Sample paper test failed: {e}")
        return False

def main():
    """Run all tests."""
    print("=== LLM Integration Test Suite ===\n")

    # Check environment
    api_key = os.getenv('OPENROUTER_API_KEY')
    if not api_key:
        print("⚠ OPENROUTER_API_KEY not found in environment")
        print("  LLM features will be disabled")
        print("  Set the API key to test LLM functionality\n")
    else:
        print("✓ OPENROUTER_API_KEY found in environment\n")

    def test_paper_comparison():
        """Test paper comparison functionality."""
        print("\nTesting paper comparison...")

        try:
            # Create mock paper data with LLM-enhanced fields
            paper1 = {
                "title": "Temperature effects on microbial communities",
                "llm_enhanced_hypotheses": [
                    {
                        "text": "Temperature significantly affects microbial diversity",
                        "type": "primary",
                        "confidence": 0.8,
                        "testable": True
                    }
                ],
                "llm_enhanced_evidence": [
                    {
                        "text": "16S rRNA sequencing showed diversity differences",
                        "type": "experimental",
                        "strength": "strong",
                        "quantitative": True
                    }
                ],
                "conclusions": ["Temperature is a major driver of microbial structure"]
            }

            paper2 = {
                "title": "pH impacts on soil microbiome",
                "llm_enhanced_hypotheses": [
                    {
                        "text": "Soil pH influences microbial community composition",
                        "type": "primary",
                        "confidence": 0.9,
                        "testable": True
                    }
                ],
                "llm_enhanced_evidence": [
                    {
                        "text": "Metagenomic analysis revealed pH-dependent patterns",
                        "type": "experimental",
                        "strength": "strong",
                        "quantitative": True
                    }
                ],
                "conclusions": ["pH is a key factor in soil microbial diversity"]
            }

            # Test comparison
            engine = PaperComparisonEngine()
            scores = engine.compare_papers(paper1, paper2)

            print("✓ Paper comparison completed")
            print(f"  - Hypothesis similarity: {scores['hypothesis_similarity']:.3f}")
            print(f"  - Evidence similarity: {scores['evidence_similarity']:.3f}")
            print(f"  - Overall similarity: {scores['overall_similarity']:.3f}")

            return True

        except Exception as e:
            print(f"✗ Paper comparison test failed: {e}")
            return False

    tests = [
        ("LLM Client", test_llm_client),
        ("PDF Extractor Integration", test_pdf_extractor_integration),
        ("Sample Paper", test_sample_paper),
        ("Paper Comparison", test_paper_comparison)
    ]

    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"✗ {test_name} test crashed: {e}")
            results.append((test_name, False))

    # Summary
    print("\n=== Test Results ===")
    passed = sum(1 for _, result in results if result)
    total = len(results)

    for test_name, result in results:
        status = "✓ PASS" if result else "✗ FAIL"
        print(f"{status}: {test_name}")

    print(f"\nOverall: {passed}/{total} tests passed")

    if passed == total:
        print("🎉 All tests passed! LLM integration is working correctly.")
    else:
        print("⚠ Some tests failed. Check the output above for details.")

    return passed == total

if __name__ == '__main__':
    success = main()
    exit(0 if success else 1)
