#!/usr/bin/env python3
"""Main novelty graph pipeline for scientific papers."""

import json
import numpy as np
from pathlib import Path
from typing import Dict, List, Optional, Tuple
import networkx as nx
from sklearn.metrics.pairwise import cosine_similarity
from sklearn.cluster import KMeans
from sentence_transformers import SentenceTransformer
from pyvis.network import Network
import plotly.graph_objects as go
from tqdm import tqdm
import click


class PaperSimilarityEngine:
    """Compute similarity metrics between scientific papers."""
    
    def __init__(self, model_name: str = 'all-MiniLM-L6-v2'):
        # Using a lighter model that doesn't require GPU
        self.embedder = SentenceTransformer(model_name)
        print(f"Loaded embedding model: {model_name}")
    
    def compute_embeddings(self, papers: List[Dict]) -> np.ndarray:
        """Generate embeddings for papers using title and abstract."""
        paper_texts = []
        
        for paper in papers:
            # Combine title and abstract/full text for embedding
            title = paper.get('title', '')
            abstract = paper.get('sections', {}).get('abstract', '')
            
            # If no abstract, use first part of full text
            if not abstract and paper.get('full_text'):
                abstract = paper['full_text'][:1000]
            
            text = f"{title} {abstract}"
            paper_texts.append(text)
        
        print("Generating embeddings...")
        embeddings = self.embedder.encode(paper_texts, show_progress_bar=True)
        return embeddings
    
    def compute_similarity_matrix(self, embeddings: np.ndarray) -> np.ndarray:
        """Compute pairwise cosine similarity between embeddings."""
        return cosine_similarity(embeddings)


class NoveltyGraphBuilder:
    """Build and analyze novelty graphs from paper similarities."""
    
    def __init__(self, similarity_threshold: float = 0.5):
        self.threshold = similarity_threshold
    
    def build_graph(self, papers: List[Dict], similarity_matrix: np.ndarray, 
                    embeddings: np.ndarray) -> nx.Graph:
        """Construct network graph from papers and similarities."""
        G = nx.Graph()
        
        # Add nodes with paper metadata
        for idx, paper in enumerate(papers):
            # Extract paper filename as ID
            file_name = Path(paper['file_path']).stem
            
            G.add_node(idx,
                      file_name=file_name,
                      title=paper.get('title', f'Paper {idx}'),
                      doi=paper.get('doi', ''),
                      hypotheses=paper.get('hypotheses', []),
                      embedding=embeddings[idx].tolist())
        
        # Add edges based on similarity threshold
        n_papers = len(papers)
        for i in range(n_papers):
            for j in range(i + 1, n_papers):
                similarity = similarity_matrix[i][j]
                if similarity > self.threshold:
                    G.add_edge(i, j, weight=similarity)
        
        # Calculate novelty scores
        self._calculate_novelty_scores(G, similarity_matrix)
        
        print(f"Graph created: {G.number_of_nodes()} nodes, {G.number_of_edges()} edges")
        return G
    
    def _calculate_novelty_scores(self, G: nx.Graph, similarity_matrix: np.ndarray):
        """Compute novelty score for each paper."""
        for node in G.nodes():
            # Get similarities to all other papers
            node_similarities = similarity_matrix[node]
            # Exclude self-similarity
            other_similarities = np.concatenate([node_similarities[:node], 
                                               node_similarities[node+1:]])
            
            # Novelty score: 1 - max similarity to any other paper
            if len(other_similarities) > 0:
                novelty_score = 1 - np.max(other_similarities)
            else:
                novelty_score = 1.0
            
            G.nodes[node]['novelty_score'] = novelty_score


class NoveltyGraphVisualizer:
    """Create interactive visualizations of novelty graphs."""
    
    def __init__(self):
        self.color_palette = [
            '#FF6B6B', '#4ECDC4', '#45B7D1', '#FFA07A', '#98D8C8',
            '#6C5CE7', '#55A3FF', '#FD79A8', '#FDCB6E', '#A29BFE'
        ]
    
    def create_interactive_visualization(self, G: nx.Graph, papers: List[Dict],
                                       output_file: str = 'novelty_graph.html') -> str:
        """Generate interactive network visualization."""
        
        # Determine topics using K-means clustering on embeddings
        embeddings = np.array([G.nodes[node]['embedding'] for node in G.nodes()])
        n_topics = min(5, len(G.nodes()) // 3)  # Adaptive topic count
        
        if n_topics > 1:
            kmeans = KMeans(n_clusters=n_topics, random_state=42, n_init=10)
            topics = kmeans.fit_predict(embeddings)
        else:
            topics = [0] * len(G.nodes())
        
        # Create PyVis network
        net = Network(height='750px', width='100%', bgcolor='#f0f0f0',
                     font_color='black', notebook=False)
        
        # Configure physics for better layout
        net.barnes_hut(gravity=-80000, central_gravity=0.3,
                      spring_length=200, spring_strength=0.001)
        
        # Add nodes with topic-based coloring
        for idx, (node, data) in enumerate(G.nodes(data=True)):
            # Create hover text
            hover_text = f"<b>{data['title']}</b><br>"
            hover_text += f"File: {data['file_name']}<br>"
            hover_text += f"Novelty: {data['novelty_score']:.3f}<br>"
            if data.get('hypotheses'):
                hover_text += f"Hypotheses: {len(data['hypotheses'])}"
            
            net.add_node(node,
                        label=data['file_name'][:20] + '...',
                        title=hover_text,
                        color=self.color_palette[topics[idx] % len(self.color_palette)],
                        size=15 + data['novelty_score'] * 25,
                        borderWidth=2,
                        borderWidthSelected=4)
        
        # Add edges
        for source, target, data in G.edges(data=True):
            net.add_edge(source, target,
                        value=float(data['weight']),  # Convert to Python float
                        title=f"Similarity: {data['weight']:.3f}")
        
        # Add controls and save
        net.show_buttons(filter_=['physics'])
        net.save_graph(output_file)
        
        print(f"Interactive visualization saved to: {output_file}")
        return output_file
    
    def create_plotly_visualization(self, G: nx.Graph, papers: List[Dict],
                                  output_file: str = 'novelty_graph_3d.html') -> str:
        """Create 3D visualization using Plotly."""
        # Use spring layout for positioning
        pos = nx.spring_layout(G, k=3, iterations=50, dim=3)
        
        # Extract positions
        node_x = [pos[node][0] for node in G.nodes()]
        node_y = [pos[node][1] for node in G.nodes()]
        node_z = [pos[node][2] for node in G.nodes()]
        
        # Create edge traces
        edge_traces = []
        for edge in G.edges():
            x0, y0, z0 = pos[edge[0]]
            x1, y1, z1 = pos[edge[1]]
            edge_traces.append(go.Scatter3d(
                x=[x0, x1, None], y=[y0, y1, None], z=[z0, z1, None],
                mode='lines',
                line=dict(color='#888', width=2),
                hoverinfo='none'
            ))
        
        # Create node trace
        node_trace = go.Scatter3d(
            x=node_x, y=node_y, z=node_z,
            mode='markers+text',
            marker=dict(
                size=[G.nodes[node]['novelty_score'] * 20 + 5 for node in G.nodes()],
                color=[G.nodes[node]['novelty_score'] for node in G.nodes()],
                colorscale='Viridis',
                colorbar=dict(title="Novelty Score"),
                line=dict(color='DarkSlateGrey', width=0.5)
            ),
            text=[G.nodes[node]['file_name'][:15] for node in G.nodes()],
            textposition="top center",
            hovertext=[f"{G.nodes[node]['title']}<br>Novelty: {G.nodes[node]['novelty_score']:.3f}" 
                      for node in G.nodes()],
            hoverinfo='text'
        )
        
        # Create figure
        fig = go.Figure(data=edge_traces + [node_trace])
        fig.update_layout(
            title='3D Scientific Paper Novelty Network',
            showlegend=False,
            scene=dict(
                xaxis=dict(showgrid=False, showticklabels=False, title=''),
                yaxis=dict(showgrid=False, showticklabels=False, title=''),
                zaxis=dict(showgrid=False, showticklabels=False, title='')
            ),
            margin=dict(b=0, l=0, r=0, t=40),
            hovermode='closest'
        )
        
        fig.write_html(output_file)
        print(f"3D visualization saved to: {output_file}")
        return output_file


class NoveltyGraphPipeline:
    """Main pipeline for processing papers into novelty graphs."""
    
    def __init__(self, similarity_threshold: float = 0.5):
        self.similarity_engine = PaperSimilarityEngine()
        self.graph_builder = NoveltyGraphBuilder(similarity_threshold)
        self.visualizer = NoveltyGraphVisualizer()
    
    def process_papers(self, papers_json: str) -> Dict:
        """Process extracted papers and generate novelty graph."""
        # Load papers
        with open(papers_json, 'r', encoding='utf-8') as f:
            papers = json.load(f)
        
        print(f"Processing {len(papers)} papers...")
        
        # Generate embeddings
        embeddings = self.similarity_engine.compute_embeddings(papers)
        
        # Compute similarity matrix
        similarity_matrix = self.similarity_engine.compute_similarity_matrix(embeddings)
        
        # Build graph
        graph = self.graph_builder.build_graph(papers, similarity_matrix, embeddings)
        
        # Analyze graph
        metrics = self.analyze_graph(graph)
        
        # Create visualizations
        viz_2d = self.visualizer.create_interactive_visualization(graph, papers)
        viz_3d = self.visualizer.create_plotly_visualization(graph, papers)
        
        # Export comparison data
        comparison_data = self.export_comparison_data(papers, graph)
        comparison_file = 'paper_comparison_data.json'
        with open(comparison_file, 'w', encoding='utf-8') as f:
            json.dump(comparison_data, f, indent=2, ensure_ascii=False)
        print(f"Paper comparison data saved to: {comparison_file}")
        
        return {
            'graph': graph,
            'metrics': metrics,
            'visualization_2d': viz_2d,
            'visualization_3d': viz_3d,
            'n_papers': len(papers),
            'n_edges': graph.number_of_edges(),
            'comparison_data': comparison_file
        }
    
    def analyze_graph(self, G: nx.Graph) -> Dict:
        """Compute graph metrics."""
        metrics = {
            'n_nodes': G.number_of_nodes(),
            'n_edges': G.number_of_edges(),
            'density': nx.density(G),
            'n_components': nx.number_connected_components(G),
            'avg_clustering': nx.average_clustering(G),
        }
        
        # Find most novel papers
        novelty_scores = nx.get_node_attributes(G, 'novelty_score')
        sorted_nodes = sorted(novelty_scores.items(), key=lambda x: x[1], reverse=True)
        
        metrics['most_novel_papers'] = [
            {
                'node_id': node,
                'title': G.nodes[node]['title'],
                'file_name': G.nodes[node]['file_name'],
                'novelty_score': score
            }
            for node, score in sorted_nodes[:5]
        ]
        
        return metrics

    def export_comparison_data(self, papers: List[Dict], G: nx.Graph) -> List[Dict]:
        """Export paper data used for comparison as JSON."""
        comparison_data = []
        
        for idx, paper in enumerate(papers):
            # Extract paper filename as ID
            file_name = Path(paper['file_path']).stem
            
            # Get the text used for comparison
            title = paper.get('title', '')
            abstract = paper.get('sections', {}).get('abstract', '')
            
            # If no abstract, use first part of full text
            if not abstract and paper.get('full_text'):
                abstract = paper['full_text'][:1000]
            
            comparison_text = f"{title} {abstract}"
            
            # Get novelty score from graph
            novelty_score = G.nodes[idx].get('novelty_score', 0)
            
            comparison_data.append({
                'node_id': idx,
                'file_name': file_name,
                'title': title,
                'comparison_text': comparison_text,  # No truncation
                'novelty_score': novelty_score
            })
        
        return comparison_data


@click.command()
@click.option('--input', '-i', default='extracted_papers.json', 
              help='Input JSON file with extracted papers')
@click.option('--threshold', '-t', default=0.5, type=float,
              help='Similarity threshold for edges (0-1)')
@click.option('--extract-first', is_flag=True,
              help='Extract PDFs first before processing')
def main(input, threshold, extract_first):
    """Run the novelty graph pipeline."""
    
    if extract_first:
        print("Extracting PDFs first...")
        from pdf_extractor import PDFExtractor
        extractor = PDFExtractor()
        extractor.process_directory('nm_test', input)
    
    # Check if input file exists
    if not Path(input).exists():
        print(f"Input file {input} not found. Run with --extract-first flag.")
        return
    
    # Run pipeline
    pipeline = NoveltyGraphPipeline(similarity_threshold=threshold)
    results = pipeline.process_papers(input)
    
    # Print results
    print("\n=== Novelty Graph Analysis Results ===")
    print(f"Papers processed: {results['n_papers']}")
    print(f"Graph edges: {results['n_edges']}")
    print(f"Graph density: {results['metrics']['density']:.3f}")
    print(f"Connected components: {results['metrics']['n_components']}")
    print(f"Average clustering: {results['metrics']['avg_clustering']:.3f}")
    
    print("\n=== Most Novel Papers ===")
    for i, paper in enumerate(results['metrics']['most_novel_papers'], 1):
        print(f"{i}. {paper['file_name']} (Novelty: {paper['novelty_score']:.3f})")
        if paper['title']:
            print(f"   Title: {paper['title'][:80]}...")
    
    print(f"\nVisualizations saved:")
    print(f"- 2D Interactive: {results['visualization_2d']}")
    print(f"- 3D Interactive: {results['visualization_3d']}")
    print(f"- Paper comparison data: {results['comparison_data']}")


if __name__ == '__main__':
    main()
