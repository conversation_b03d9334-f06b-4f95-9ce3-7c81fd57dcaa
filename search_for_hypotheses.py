#!/usr/bin/env python3
"""Search for any hypothesis-like text in the paper."""

import re
from pathlib import Path
from pdf_extractor import PDFExtractor


def search_for_hypothesis_patterns():
    """Search for any hypothesis-like patterns in the paper."""
    
    print("🔍 SEARCHING FOR HYPOTHESIS PATTERNS")
    print("=" * 50)
    
    # Get sample PDF
    pdf_files = list(Path("nm_test").glob("*.pdf"))
    if not pdf_files:
        print("❌ No PDF files found")
        return
    
    sample_pdf = pdf_files[0]
    print(f"Searching in: {sample_pdf.name}")
    
    # Extract text
    extractor = PDFExtractor(use_llm=False)
    paper_data = extractor.extract_pdf_text(str(sample_pdf))
    
    if not paper_data:
        print("❌ Failed to extract PDF text")
        return
    
    full_text = paper_data['full_text']
    print(f"Full text length: {len(full_text)} characters")
    
    # Search for hypothesis-related words
    hypothesis_words = [
        'hypothesis', 'hypothesize', 'hypothesized', 'hypotheses',
        'predict', 'predicted', 'prediction', 'predictions',
        'expect', 'expected', 'expectation', 'expectations',
        'propose', 'proposed', 'proposal', 'proposals',
        'suggest', 'suggested', 'suggestion', 'suggestions'
    ]
    
    print(f"\n📝 SEARCHING FOR HYPOTHESIS-RELATED WORDS:")
    print("-" * 40)
    
    for word in hypothesis_words:
        # Case-insensitive search
        pattern = re.compile(r'\b' + word + r'\b', re.IGNORECASE)
        matches = pattern.findall(full_text)
        
        if matches:
            print(f"'{word}': {len(matches)} occurrences")
            
            # Find context around each match
            for match in pattern.finditer(full_text):
                start = max(0, match.start() - 100)
                end = min(len(full_text), match.end() + 100)
                context = full_text[start:end].replace('\n', ' ')
                print(f"  Context: ...{context}...")
                break  # Just show first occurrence
        else:
            print(f"'{word}': 0 occurrences")
    
    # Search for specific patterns that LLM might be finding
    print(f"\n🤖 SEARCHING FOR PATTERNS LLM MIGHT FIND:")
    print("-" * 40)
    
    llm_patterns = [
        r"we (\w+) that (.{50,200})",
        r"our (\w+) (?:is|was) (.{50,200})",
        r"this (\w+) (.{50,200})",
        r"these (\w+) (.{50,200})",
        r"(?:results|findings|data) (?:suggest|indicate|show) (.{50,200})",
        r"(?:consistent with|support) (?:the|our) (\w+) (.{50,200})"
    ]
    
    for pattern in llm_patterns:
        matches = re.findall(pattern, full_text, re.IGNORECASE | re.DOTALL)
        if matches:
            print(f"Pattern '{pattern}': {len(matches)} matches")
            for i, match in enumerate(matches[:2]):  # Show first 2
                if isinstance(match, tuple):
                    print(f"  Match {i+1}: {' '.join(match)[:150]}...")
                else:
                    print(f"  Match {i+1}: {match[:150]}...")
        else:
            print(f"Pattern '{pattern}': 0 matches")
    
    # Check what the LLM actually found
    print(f"\n🔍 CHECKING WHAT LLM FOUND:")
    print("-" * 40)
    
    try:
        from enhanced_extraction_methods import EnhancedLLMExtractor
        llm_extractor = EnhancedLLMExtractor()
        llm_results = llm_extractor.extract_with_citations(paper_data)
        
        print(f"LLM found {len(llm_results['hypotheses'])} hypotheses:")
        for i, hyp in enumerate(llm_results['hypotheses']):
            print(f"  {i+1}. {hyp.text[:200]}...")
            print(f"     Section: {hyp.section}")
            print(f"     Type: {hyp.item_type}")
            
            # Try to find this text in the original
            if hyp.text in full_text:
                print(f"     ✅ Found exact match in original text")
            else:
                # Try partial match
                words = hyp.text.split()[:5]  # First 5 words
                partial = ' '.join(words)
                if partial in full_text:
                    print(f"     ⚠️  Found partial match: '{partial}'")
                else:
                    print(f"     ❌ No match found - LLM may have paraphrased")
    
    except Exception as e:
        print(f"❌ LLM extraction failed: {e}")


def check_sections_for_hypotheses():
    """Check each section for hypothesis content."""
    
    print(f"\n📚 CHECKING SECTIONS FOR HYPOTHESIS CONTENT:")
    print("-" * 50)
    
    # Get sample PDF
    pdf_files = list(Path("nm_test").glob("*.pdf"))
    if not pdf_files:
        return
    
    sample_pdf = pdf_files[0]
    
    # Extract text
    extractor = PDFExtractor(use_llm=False)
    paper_data = extractor.extract_pdf_text(str(sample_pdf))
    
    sections = paper_data.get('sections', {})
    
    for section_name, section_text in sections.items():
        print(f"\nSection: {section_name} ({len(section_text)} chars)")
        
        # Look for hypothesis words in this section
        hypothesis_words = ['hypothesis', 'predict', 'expect', 'propose', 'suggest']
        
        for word in hypothesis_words:
            count = len(re.findall(r'\b' + word + r'\w*\b', section_text, re.IGNORECASE))
            if count > 0:
                print(f"  '{word}*': {count} occurrences")
        
        # Show first 300 characters
        if len(section_text) > 0:
            print(f"  First 300 chars: {section_text[:300]}...")


def main():
    """Run hypothesis search."""
    search_for_hypothesis_patterns()
    check_sections_for_hypotheses()


if __name__ == '__main__':
    main()
