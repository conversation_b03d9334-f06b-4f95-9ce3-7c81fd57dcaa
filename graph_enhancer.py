#!/usr/bin/env python3
"""Enhanced graph visualization with filtering, layout optimization, and PNG export."""

import json
import numpy as np
import networkx as nx
import matplotlib.pyplot as plt
import matplotlib.patches as mpatches
from matplotlib import cm
from typing import Dict, List, Tuple, Optional
import community as community_louvain
from pathlib import Path
import click
from tqdm import tqdm
import warnings
warnings.filterwarnings('ignore')


class GraphEnhancer:
    """Enhanced graph processing with filtering and layout optimization."""
    
    def __init__(self):
        self.color_palette = [
            '#FF6B6B', '#4ECDC4', '#45B7D1', '#FFA07A', '#98D8C8',
            '#6C5CE7', '#55A3FF', '#FD79A8', '#FDCB6E', '#A29BFE',
            '#74B9FF', '#A29BFE', '#FD79A8', '#FDCB6E', '#55EFC4'
        ]
    
    def filter_graph(self, G: nx.Graph, min_weight: float = 0.5, 
                    k_core: int = 2) -> nx.Graph:
        """Filter graph to reduce hairball effect."""
        # Create a copy to work with
        G_filtered = G.copy()
        
        # Remove low-weight edges
        edges_to_remove = [(u, v) for u, v, data in G_filtered.edges(data=True) 
                          if data.get('weight', 0) < min_weight]
        G_filtered.remove_edges_from(edges_to_remove)
        
        # Keep only nodes in k-core (nodes with at least k connections)
        if k_core > 1:
            k_core_nodes = set(nx.k_core(G_filtered, k=k_core).nodes())
            nodes_to_remove = [n for n in G_filtered.nodes() if n not in k_core_nodes]
            G_filtered.remove_nodes_from(nodes_to_remove)
        
        # Remove isolated nodes
        isolated = list(nx.isolates(G_filtered))
        G_filtered.remove_nodes_from(isolated)
        
        print(f"Filtered graph: {G.number_of_nodes()} → {G_filtered.number_of_nodes()} nodes, "
              f"{G.number_of_edges()} → {G_filtered.number_of_edges()} edges")
        
        return G_filtered
    
    def detect_communities(self, G: nx.Graph) -> Dict[int, int]:
        """Detect communities using Louvain algorithm."""
        # Convert to undirected for community detection
        G_undirected = G.to_undirected()
        
        # Detect communities
        partition = community_louvain.best_partition(G_undirected, 
                                                   weight='weight',
                                                   random_state=42)
        
        # Calculate modularity
        modularity = community_louvain.modularity(partition, G_undirected, weight='weight')
        print(f"Detected {len(set(partition.values()))} communities, modularity: {modularity:.3f}")
        
        return partition
    
    def find_cliques(self, G: nx.Graph, min_size: int = 3) -> List[set]:
        """Find cliques (fully connected subgraphs) of minimum size."""
        # Find all maximal cliques
        cliques = list(nx.find_cliques(G.to_undirected()))
        
        # Filter by size
        significant_cliques = [set(clique) for clique in cliques if len(clique) >= min_size]
        significant_cliques.sort(key=len, reverse=True)
        
        print(f"Found {len(significant_cliques)} cliques with ≥{min_size} nodes")
        
        return significant_cliques
    
    def compute_force_layout(self, G: nx.Graph, iterations: int = 500,
                           k: float = None, seed: int = 42) -> Dict[int, Tuple[float, float]]:
        """Compute force-directed layout with customized parameters."""
        # Calculate optimal k (distance between nodes) based on graph size
        if k is None:
            k = 1.0 / np.sqrt(G.number_of_nodes())
        
        # Use Fruchterman-Reingold force-directed algorithm
        pos = nx.spring_layout(G, 
                             k=k,
                             iterations=iterations,
                             weight='weight',
                             seed=seed,
                             scale=10)
        
        return pos
    
    def create_enhanced_visualization(self, G: nx.Graph, papers: List[Dict],
                                    output_file: str = 'novelty_graph_enhanced.png',
                                    min_weight: float = 0.5,
                                    k_core: int = 2,
                                    figsize: Tuple[int, int] = (20, 20),
                                    dpi: int = 300):
        """Create enhanced PNG visualization with filtered graph and optimized layout."""
        
        # Filter the graph
        G_filtered = self.filter_graph(G, min_weight=min_weight, k_core=k_core)
        
        if G_filtered.number_of_nodes() == 0:
            print("No nodes left after filtering. Try reducing filter parameters.")
            return
        
        # Detect communities
        communities = self.detect_communities(G_filtered)
        
        # Find cliques
        cliques = self.find_cliques(G_filtered, min_size=3)
        
        # Compute layout
        print("Computing force-directed layout...")
        pos = self.compute_force_layout(G_filtered, iterations=500)
        
        # Create figure
        fig, ax = plt.subplots(figsize=figsize, facecolor='white')
        ax.set_facecolor('white')
        
        # Draw edges with varying thickness based on weight
        edges = G_filtered.edges(data=True)
        weights = [data['weight'] for _, _, data in edges]
        
        # Normalize weights for edge thickness
        min_weight_val = min(weights)
        max_weight_val = max(weights)
        normalized_weights = [(w - min_weight_val) / (max_weight_val - min_weight_val) for w in weights]
        
        # Draw edges
        nx.draw_networkx_edges(G_filtered, pos,
                             edge_color='gray',
                             width=[0.5 + w * 3 for w in normalized_weights],
                             alpha=0.3,
                             ax=ax)
        
        # Prepare node colors based on communities
        node_colors = [self.color_palette[communities[node] % len(self.color_palette)] 
                      for node in G_filtered.nodes()]
        
        # Node sizes based on novelty score
        node_sizes = [300 + G_filtered.nodes[node].get('novelty_score', 0.5) * 1000 
                     for node in G_filtered.nodes()]
        
        # Draw nodes
        nx.draw_networkx_nodes(G_filtered, pos,
                             node_color=node_colors,
                             node_size=node_sizes,
                             alpha=0.8,
                             linewidths=2,
                             edgecolors='black',
                             ax=ax)
        
        # Add labels for high-novelty nodes
        high_novelty_nodes = {node: G_filtered.nodes[node]['file_name'][:15] + '...'
                            for node in G_filtered.nodes()
                            if G_filtered.nodes[node].get('novelty_score', 0) > 0.5}
        
        nx.draw_networkx_labels(G_filtered, pos,
                              labels=high_novelty_nodes,
                              font_size=8,
                              font_weight='bold',
                              ax=ax)
        
        # Highlight cliques with circles
        for i, clique in enumerate(cliques[:5]):  # Show top 5 cliques
            clique_nodes = [n for n in clique if n in G_filtered.nodes()]
            if len(clique_nodes) >= 3:
                # Calculate center and radius for clique
                clique_pos = np.array([pos[n] for n in clique_nodes])
                center = np.mean(clique_pos, axis=0)
                radius = np.max(np.linalg.norm(clique_pos - center, axis=1)) + 0.5
                
                # Draw circle around clique
                circle = plt.Circle(center, radius, fill=False, 
                                  edgecolor='red', linewidth=2, 
                                  linestyle='--', alpha=0.5)
                ax.add_patch(circle)
        
        # Add title and remove axes
        ax.set_title(f'Scientific Paper Novelty Network\n'
                    f'{G_filtered.number_of_nodes()} papers, '
                    f'{G_filtered.number_of_edges()} connections, '
                    f'{len(set(communities.values()))} communities',
                    fontsize=16, fontweight='bold', pad=20)
        ax.axis('off')
        
        # Add legend
        community_colors = list(set(communities.values()))
        legend_elements = [mpatches.Patch(color=self.color_palette[c % len(self.color_palette)], 
                                        label=f'Community {c}')
                         for c in sorted(community_colors)[:10]]  # Show max 10 communities
        
        ax.legend(handles=legend_elements, loc='upper left', 
                 bbox_to_anchor=(0, 1), fontsize=10)
        
        # Save figure
        plt.tight_layout()
        plt.savefig(output_file, dpi=dpi, bbox_inches='tight', 
                   facecolor='white', edgecolor='none')
        plt.close()
        
        print(f"Enhanced visualization saved to: {output_file}")
        
        # Save network statistics
        stats_file = output_file.replace('.png', '_stats.json')
        stats = {
            'nodes': G_filtered.number_of_nodes(),
            'edges': G_filtered.number_of_edges(),
            'density': nx.density(G_filtered),
            'communities': len(set(communities.values())),
            'cliques': len(cliques),
            'largest_clique_size': len(cliques[0]) if cliques else 0,
            'average_clustering': nx.average_clustering(G_filtered),
            'filter_params': {
                'min_weight': min_weight,
                'k_core': k_core
            }
        }
        
        with open(stats_file, 'w') as f:
            json.dump(stats, f, indent=2)
        
        print(f"Network statistics saved to: {stats_file}")
        
        return G_filtered, communities, cliques


def load_graph_from_results() -> Tuple[nx.Graph, List[Dict]]:
    """Load graph and papers from existing results."""
    # Load papers
    with open('extracted_papers.json', 'r') as f:
        papers = json.load(f)
    
    # Recreate graph from main.py results
    from main import NoveltyGraphPipeline
    pipeline = NoveltyGraphPipeline()
    
    # Load embeddings and similarities
    from main import PaperSimilarityEngine
    similarity_engine = PaperSimilarityEngine()
    embeddings = similarity_engine.compute_embeddings(papers)
    similarity_matrix = similarity_engine.compute_similarity_matrix(embeddings)
    
    # Build graph
    from main import NoveltyGraphBuilder
    graph_builder = NoveltyGraphBuilder(similarity_threshold=0.3)
    graph = graph_builder.build_graph(papers, similarity_matrix, embeddings)
    
    return graph, papers


@click.command()
@click.option('--min-weight', '-w', default=0.5, type=float,
              help='Minimum edge weight to keep (0-1)')
@click.option('--k-core', '-k', default=2, type=int,
              help='Minimum connections per node (k-core filtering)')
@click.option('--output', '-o', default='novelty_graph_enhanced.png',
              help='Output PNG file')
@click.option('--dpi', default=300, type=int,
              help='DPI for output image')
@click.option('--size', default=20, type=int,
              help='Figure size in inches (square)')
def main(min_weight, k_core, output, dpi, size):
    """Create enhanced graph visualization with filtering and PNG export."""
    print("Loading graph data...")
    graph, papers = load_graph_from_results()
    
    enhancer = GraphEnhancer()
    enhancer.create_enhanced_visualization(
        graph, papers,
        output_file=output,
        min_weight=min_weight,
        k_core=k_core,
        figsize=(size, size),
        dpi=dpi
    )


if __name__ == '__main__':
    main()